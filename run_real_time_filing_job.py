#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the real-time filing job.
This job fetches today's earnings calendar events and checks for new SEC filings
for companies with earnings today.
"""

import logging
from jobs.real_time_filing_job import RealTimeFilingJob
from config.settings import Settings

def configure_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def main():
    # Configure logging
    configure_logging()
    
    # Initialize and run job
    job = RealTimeFilingJob()
    job.run()

if __name__ == "__main__":
    main()
