#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the Hong Kong stock data job.
"""

import os
import sys
from dotenv import load_dotenv

from utils.logging_utils import setup_logging

# Add the current directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jobs.stock_hk_data_job import StockH<PERSON><PERSON><PERSON><PERSON><PERSON>

def main():
    """
    Main entry point.
    """
    # Load environment variables
    load_dotenv()
    
    # Set up logging
    logger = setup_logging(enable_colors=False)
    
    # Get rate limiting configuration from environment variables or use defaults
    max_retries = int(os.getenv('YF_MAX_RETRIES', '5'))
    min_wait = float(os.getenv('YF_MIN_WAIT', '60.0'))
    max_wait = float(os.getenv('YF_MAX_WAIT', '120.0'))
    
    logger.info("Starting Hong Kong stock data job with rate limiting enhancements")
    logger.info(f"Rate limiting configuration: max_retries={max_retries}, min_wait={min_wait}s, max_wait={max_wait}s")
    
    from config.settings import settings
    job = StockHKDataJob(
        full_refresh=settings.JOB_FULL_REFRESH,
        specific_tickers=settings.JOB_SPECIFIC_TICKERS,
        batch_size=settings.JOB_BATCH_SIZE,
        delay=settings.JOB_DELAY,
        price_days=settings.JOB_PRICE_DAYS,
        max_retries=max_retries,
        min_wait=min_wait,
        max_wait=max_wait
    )

    success = job.run()

    if success:
        logger.info("Hong Kong stock data job completed successfully")
        logger.info(f"Processed {job.processed_count} stocks with {job.error_count} errors")
    else:
        logger.error("Hong Kong stock data job failed")
        logger.error(f"Processed {job.processed_count} stocks with {job.error_count} errors")

    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
