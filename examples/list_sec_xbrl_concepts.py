#!/usr/bin/env python3
"""
<PERSON><PERSON>t to list all available concepts in the SEC XBRL API.
"""

import os
import sys
import argparse
import requests
import json
import pandas as pd
from collections import Counter

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from utils.logging_utils import logger

# Common financial concepts in the us-gaap taxonomy
COMMON_FINANCIAL_CONCEPTS = [
    # Income Statement
    "Revenue",
    "RevenueFromContractWithCustomerExcludingAssessedTax",
    "CostOfGoodsAndServicesSold",
    "GrossProfit",
    "OperatingExpenses",
    "OperatingIncomeLoss",
    "IncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest",
    "IncomeTaxExpenseBenefit",
    "NetIncomeLoss",
    "EarningsPerShareBasic",
    "EarningsPerShareDiluted",
    
    # Balance Sheet
    "Assets",
    "AssetsCurrent",
    "CashAndCashEquivalentsAtCarryingValue",
    "ShortTermInvestments",
    "AccountsReceivableNetCurrent",
    "Inventory",
    "AssetsNoncurrent",
    "PropertyPlantAndEquipmentNet",
    "Goodwill",
    "IntangibleAssetsNetExcludingGoodwill",
    "LongTermInvestments",
    
    "Liabilities",
    "LiabilitiesCurrent",
    "AccountsPayableCurrent",
    "AccruedLiabilitiesCurrent",
    "LongTermDebtCurrent",
    "LiabilitiesNoncurrent",
    "LongTermDebt",
    
    "StockholdersEquity",
    "CommonStockParOrStatedValuePerShare",
    "CommonStockSharesAuthorized",
    "CommonStockSharesIssued",
    "CommonStockSharesOutstanding",
    "RetainedEarningsAccumulatedDeficit",
    "AccumulatedOtherComprehensiveIncomeLossNetOfTax",
    
    # Cash Flow
    "NetCashProvidedByUsedInOperatingActivities",
    "NetCashProvidedByUsedInInvestingActivities",
    "NetCashProvidedByUsedInFinancingActivities",
    "CashAndCashEquivalentsPeriodIncreaseDecrease",
    
    # Other Key Metrics
    "ResearchAndDevelopmentExpense",
    "SellingGeneralAndAdministrativeExpense",
    "InterestExpense",
    "InterestIncome",
    "DepreciationAndAmortization",
    "CapitalExpenditure",
    "DividendsPaid",
    "ShareBasedCompensation",
    "WeightedAverageNumberOfSharesOutstandingBasic",
    "WeightedAverageNumberOfDilutedSharesOutstanding",
    
    # Ratios and Other Metrics
    "DebtToEquityRatio",
    "ReturnOnAssets",
    "ReturnOnEquity",
    "CurrentRatio",
    "QuickRatio",
    "InventoryTurnover",
    "DaysInventoryOutstanding",
    "AccountsReceivableTurnover",
    "DaysSalesOutstanding",
    "AccountsPayableTurnover",
    "DaysPayablesOutstanding",
    "FreeCashFlow",
    "EBITDA",
    "EBITDAMargin",
    "ProfitMargin",
    "GrossMargin",
    "OperatingMargin",
    "BookValuePerShare",
    "TangibleBookValuePerShare",
    "MarketCapitalization",
    "EnterpriseValue",
    "PriceToEarningsRatio",
    "PriceToBookRatio",
    "PriceToSalesRatio",
    "EnterpriseValueToEBITDA",
    "EnterpriseValueToRevenue",
    "DividendYield",
    "DividendPayoutRatio",
    "ReturnOnInvestedCapital",
    "DebtToEBITDA",
    "InterestCoverageRatio",
    "TaxRate",
    "EffectiveTaxRate",
    "AssetTurnover",
    "InventoryToSalesRatio",
    "DaysWorkingCapital",
    "CashConversionCycle",
    "OperatingCashFlowToSalesRatio",
    "FreeCashFlowToSalesRatio",
    "FreeCashFlowToNetIncomeRatio",
    "CapitalExpenditureToSalesRatio",
    "CapitalExpenditureToDepreciationRatio",
    "EquityToAssetsRatio",
    "DebtToAssetsRatio",
    "LongTermDebtToEquityRatio",
    "LongTermDebtToCapitalizationRatio",
    "TotalDebtToCapitalizationRatio",
    "CashToDebtRatio",
    "CashRatio",
    "OperatingCashFlowToCurrentLiabilitiesRatio",
    "OperatingCashFlowToDebtRatio",
    "OperatingCashFlowPerShare",
    "FreeCashFlowPerShare",
    "WorkingCapital",
    "WorkingCapitalToTotalAssetsRatio",
    "RetainedEarningsToTotalAssetsRatio",
    "EBITToTotalAssetsRatio",
    "MarketValueOfEquityToBookValueOfTotalLiabilitiesRatio",
    "SalesRevenueToTotalAssetsRatio",
    "AltmanZScore"
]

def fetch_company_facts(cik, ticker=None):
    """
    Fetch company facts for a given CIK.
    
    Args:
        cik: CIK number
        ticker: Optional ticker symbol for display
        
    Returns:
        dict: Company facts data
    """
    # Pad CIK with leading zeros to 10 digits
    cik_padded = cik.zfill(10)
    
    url = f"{settings.SEC_XBRL_API_URL}/CIK{cik_padded}.json"
    
    headers = {
        "User-Agent": f"{settings.SEC_API_ORGANIZATION} {settings.SEC_API_EMAIL}",
        "Content-Type": "application/json",
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        return response.json()
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching company facts for CIK {cik}: {str(e)}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON for CIK {cik}: {str(e)}")
        return None

def extract_concepts(company_facts):
    """
    Extract all concepts from company facts.
    
    Args:
        company_facts: Company facts data
        
    Returns:
        dict: Dictionary of taxonomies and their concepts
    """
    concepts_by_taxonomy = {}
    
    if not company_facts or 'facts' not in company_facts:
        return concepts_by_taxonomy
    
    facts = company_facts['facts']
    
    for taxonomy, taxonomy_facts in facts.items():
        concepts = []
        for concept, concept_data in taxonomy_facts.items():
            concepts.append({
                'concept': concept,
                'label': concept_data.get('label', ''),
                'description': concept_data.get('description', '')
            })
        
        concepts_by_taxonomy[taxonomy] = concepts
    
    return concepts_by_taxonomy

def main():
    """
    Main entry point.
    """
    parser = argparse.ArgumentParser(description='List all available concepts in the SEC XBRL API')
    parser.add_argument('--cik', type=str, help='CIK number to use for fetching concepts')
    parser.add_argument('--ticker', type=str, help='Ticker symbol (for display purposes)')
    parser.add_argument('--taxonomy', type=str, help='Filter by taxonomy (e.g., us-gaap, dei)')
    parser.add_argument('--output', type=str, help='Output file path (CSV)')
    parser.add_argument('--common', action='store_true', help='Show only common financial concepts')
    
    args = parser.parse_args()
    
    if args.common:
        # Display common financial concepts
        df = pd.DataFrame(COMMON_FINANCIAL_CONCEPTS, columns=['concept'])
        print("\nCommon Financial Concepts in the us-gaap taxonomy:")
        print(df)
        
        if args.output:
            df.to_csv(args.output, index=False)
            print(f"Common concepts saved to {args.output}")
        
        return
    
    if not args.cik:
        print("Error: CIK number is required. Use --cik to specify a CIK number.")
        print("Example: python list_sec_xbrl_concepts.py --cik 0000320193 --ticker AAPL")
        print("Or use --common to see a list of common financial concepts.")
        return
    
    # Fetch company facts
    company_facts = fetch_company_facts(args.cik, args.ticker)
    
    if not company_facts:
        print(f"Error: Could not fetch company facts for CIK {args.cik}")
        return
    
    # Extract concepts
    concepts_by_taxonomy = extract_concepts(company_facts)
    
    if not concepts_by_taxonomy:
        print(f"Error: No concepts found for CIK {args.cik}")
        return
    
    # Display company info
    entity_name = company_facts.get('entityName', 'Unknown')
    ticker_display = f" ({args.ticker})" if args.ticker else ""
    print(f"\nConcepts for {entity_name}{ticker_display} (CIK: {args.cik}):")
    
    # Filter by taxonomy if specified
    if args.taxonomy:
        if args.taxonomy in concepts_by_taxonomy:
            taxonomies = [args.taxonomy]
        else:
            print(f"Error: Taxonomy '{args.taxonomy}' not found. Available taxonomies: {', '.join(concepts_by_taxonomy.keys())}")
            return
    else:
        taxonomies = concepts_by_taxonomy.keys()
    
    # Create a list to store all concepts for CSV export
    all_concepts = []
    
    # Display concepts by taxonomy
    for taxonomy in taxonomies:
        concepts = concepts_by_taxonomy[taxonomy]
        
        print(f"\nTaxonomy: {taxonomy} ({len(concepts)} concepts)")
        
        # Convert to DataFrame for display
        df = pd.DataFrame(concepts)
        
        # Add taxonomy column for CSV export
        df['taxonomy'] = taxonomy
        all_concepts.append(df)
        
        # Display concepts
        pd.set_option('display.max_rows', 20)
        pd.set_option('display.max_colwidth', 50)
        print(df[['concept', 'label']])
        
        if len(df) > 20:
            print(f"... and {len(df) - 20} more concepts")
    
    # Save to CSV if output file is specified
    if args.output:
        if all_concepts:
            all_df = pd.concat(all_concepts)
            all_df.to_csv(args.output, index=False)
            print(f"Concepts saved to {args.output}")

if __name__ == '__main__':
    main()
