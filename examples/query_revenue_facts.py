#!/usr/bin/env python3
"""
Example script to query revenue facts from the SEC company facts database.
"""

import os
import sys
import argparse
import pandas as pd
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.db.mysql_client import get_db_session
from jobs.db.schema import Stock, SECCompanyFactMetadata, SECCompanyFact
from config.settings import settings

def query_revenue_facts(ticker=None, year=None, quarter=None, limit=10):
    """
    Query revenue facts from the SEC company facts database.
    
    Args:
        ticker: Optional ticker symbol to filter by
        year: Optional fiscal year to filter by
        quarter: Optional fiscal quarter to filter by (e.g., 'Q1', 'Q2', 'Q3', 'Q4', 'FY')
        limit: Maximum number of results to return
        
    Returns:
        DataFrame: Pandas DataFrame with revenue facts
    """
    with get_db_session() as session:
        # Build the query
        query = """
        SELECT 
            s.ticker,
            s.name,
            m.entity_name,
            f.concept,
            f.label,
            f.value,
            f.value_numeric,
            f.unit,
            f.fiscal_year,
            f.fiscal_period,
            f.form,
            f.end_date,
            f.filing_date
        FROM 
            sec_company_facts f
        JOIN 
            sec_company_fact_metadata m ON f.metadata_id = m.id
        JOIN 
            stocks s ON m.ticker = s.ticker
        WHERE 
            f.taxonomy = 'us-gaap'
            AND f.concept = 'Revenue'
        """
        
        # Add filters if provided
        params = {}
        if ticker:
            query += " AND s.ticker = :ticker"
            params['ticker'] = ticker
        
        if year:
            query += " AND f.fiscal_year = :year"
            params['year'] = year
            
        if quarter:
            query += " AND f.fiscal_period = :quarter"
            params['quarter'] = quarter
        
        # Add order by and limit
        query += """
        ORDER BY 
            s.ticker, 
            f.fiscal_year DESC, 
            CASE 
                WHEN f.fiscal_period = 'FY' THEN 5
                WHEN f.fiscal_period = 'Q4' THEN 4
                WHEN f.fiscal_period = 'Q3' THEN 3
                WHEN f.fiscal_period = 'Q2' THEN 2
                WHEN f.fiscal_period = 'Q1' THEN 1
                ELSE 0
            END DESC
        LIMIT :limit
        """
        params['limit'] = limit
        
        # Execute the query
        result = session.execute(text(query), params)
        
        # Convert to DataFrame
        df = pd.DataFrame(result.fetchall(), columns=result.keys())
        
        return df

def main():
    """
    Main entry point.
    """
    parser = argparse.ArgumentParser(description='Query revenue facts from the SEC company facts database')
    parser.add_argument('--ticker', type=str, help='Ticker symbol to filter by')
    parser.add_argument('--year', type=int, help='Fiscal year to filter by')
    parser.add_argument('--quarter', type=str, help='Fiscal quarter to filter by (e.g., Q1, Q2, Q3, Q4, FY)')
    parser.add_argument('--limit', type=int, default=10, help='Maximum number of results to return')
    parser.add_argument('--output', type=str, help='Output file path (CSV)')
    
    args = parser.parse_args()
    
    # Query revenue facts
    df = query_revenue_facts(
        ticker=args.ticker,
        year=args.year,
        quarter=args.quarter,
        limit=args.limit
    )
    
    # Print results
    if df.empty:
        print("No revenue facts found.")
    else:
        # Format the DataFrame for display
        display_df = df.copy()
        
        # Format numeric values
        if 'value_numeric' in display_df.columns:
            display_df['value_numeric'] = display_df['value_numeric'].apply(
                lambda x: f"{x:,.0f}" if pd.notnull(x) else None
            )
        
        # Format dates
        for date_col in ['end_date', 'filing_date']:
            if date_col in display_df.columns:
                display_df[date_col] = display_df[date_col].dt.strftime('%Y-%m-%d')
        
        # Print the DataFrame
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', 1000)
        print(display_df)
        
        # Save to CSV if output file is specified
        if args.output:
            df.to_csv(args.output, index=False)
            print(f"Results saved to {args.output}")

if __name__ == '__main__':
    main()
