# SEC Company Facts Performance Optimization

This document outlines the performance optimizations made to the SEC company facts job.

## Problem

The original implementation of the `process_company_facts` function was taking a significant amount of time to process company facts for a single ticker. As seen in the logs, processing company facts for AAOI took 285.83 seconds (almost 5 minutes), which is quite slow.

```
[32m2025-04-06 12:20:47,109 - go-gpt-job - INFO - [sec_company_facts_job.py:process_company_facts] - Starting to process company facts for AAOI[0mCompleted processing company facts for AAOI in 285.83s: 457 facts processed, 5160 facts stored
```

## Performance Bottlenecks Identified

After analyzing the code, the following performance bottlenecks were identified:

1. **Inefficient Database Operations**:
   - Individual database queries for each fact to check if it exists
   - Multiple database commits throughout the process
   - No batch processing of database operations

2. **Nested Loop Structure**:
   - Multiple nested loops (taxonomies → concepts → units → facts)
   - Processing each fact individually with database operations inside the innermost loop

3. **Redundant Data Processing**:
   - Filtering concepts after retrieving all data
   - Parsing dates and values for each fact individually

4. **Database Transaction Management**:
   - Committing after metadata creation/update
   - Committing after all facts are processed
   - No bulk operations

## Optimizations Implemented

The following optimizations were implemented to improve the performance of the `process_company_facts` function:

1. **Batch Database Operations**:
   - Fetch all existing facts for the ticker in a single query
   - Create an in-memory lookup dictionary for fast existence checks
   - Collect facts to be inserted/updated in memory first
   - Use SQLAlchemy's bulk insert operations

2. **Improved Transaction Management**:
   - Use a single transaction for the entire process
   - Commit only once at the end
   - Use `session.flush()` instead of `session.commit()` for intermediate operations

3. **Early Filtering**:
   - Pre-filter taxonomies and concepts we care about
   - Skip facts without end dates earlier in the process

4. **Reduced Database Commits**:
   - Removed multiple commits throughout the process
   - Commit only once at the end of the function

## Implementation Details

### 1. Fetch All Existing Facts in a Single Query

Instead of querying the database for each fact to check if it exists, we now fetch all existing facts for the ticker in a single query:

```python
# Fetch all existing facts for this metadata in a single query
existing_facts = session.query(SECCompanyFact).filter_by(metadata_id=metadata.id).all()
```

### 2. Create a Lookup Dictionary for Fast Existence Checks

We create an in-memory lookup dictionary for fast existence checks:

```python
# Create a lookup dictionary for fast existence checks
# Key format: (taxonomy, concept, end_date, form)
existing_facts_dict = {}
for fact in existing_facts:
    key = (fact.taxonomy, fact.concept, fact.end_date, fact.form)
    existing_facts_dict[key] = fact
```

### 3. Collect Facts to be Inserted/Updated in Memory

We collect facts to be inserted/updated in memory first:

```python
facts_to_update = []
facts_to_add = []

# ... processing logic ...

if existing_fact:
    # Update existing fact
    existing_fact.label = concept_data.get('label')
    # ... update other fields ...
    facts_to_update.append(existing_fact)
else:
    # Create new fact
    new_fact = SECCompanyFact(
        metadata_id=metadata.id,
        # ... other fields ...
    )
    facts_to_add.append(new_fact)
```

### 4. Use Bulk Insert Operations

We use SQLAlchemy's bulk insert operations:

```python
# Bulk add new facts
if facts_to_add:
    session.bulk_save_objects(facts_to_add)
```

### 5. Single Transaction and Commit

We use a single transaction for the entire process and commit only once at the end:

```python
# Commit all changes in a single transaction
session.commit()
```

## Expected Performance Improvement

The optimizations are expected to significantly reduce the processing time for company facts, especially for tickers with a large number of facts. The main improvements come from:

1. Reducing the number of database queries
2. Reducing the number of database commits
3. Using bulk operations for database inserts
4. Using in-memory lookups instead of database queries

## Testing

A test script (`test_sec_company_facts_performance.py`) has been created to test the performance of the optimized function. The script:

1. Sets up a test database
2. Clears existing data for the ticker
3. Runs the optimized function and measures the execution time
4. Verifies the number of facts stored in the database

To run the test:

```bash
python test_sec_company_facts_performance.py --ticker AAOI
```

## Optimization for CIK Lookup

In addition to the optimizations made to the `process_company_facts` function, we've also optimized the `get_cik_for_ticker` function to reduce unnecessary API calls.

### Problem

The original implementation of the `get_cik_for_ticker` function made an API call to the SEC CIK lookup URL (`https://www.sec.gov/files/company_tickers.json`) for each ticker being processed. Since this API returns a static JSON response with mappings for all tickers, making multiple calls to the same endpoint is inefficient.

### Optimization Implemented

We've implemented a caching mechanism at the job level:

1. **Single API Call**: The job now makes a single API call to the SEC CIK lookup URL at the beginning of the job run.
2. **In-Memory Cache**: The response is parsed and stored in an in-memory dictionary mapping all tickers to their CIKs.
3. **Lookup Order**:
   - First check if the CIK is in the database
   - Then check if it's in the in-memory cache
   - Only log a warning if not found in either location

### Implementation Details

1. **Global Cache Variable**:
```python
# Global cache for ticker to CIK mapping
_TICKER_TO_CIK_CACHE = {}
```

2. **Fetch All CIKs Once**:
```python
def fetch_all_ciks() -> Dict[str, str]:
    """
    Fetch all ticker to CIK mappings from the SEC API.
    This is called once at the beginning of the job run.
    """
    # Implementation details...
```

3. **Initialize Cache at Job Start**:
```python
# Fetch all ticker to CIK mappings once at the beginning
global _TICKER_TO_CIK_CACHE
_TICKER_TO_CIK_CACHE = fetch_all_ciks()
logger.info(f"Cached {len(_TICKER_TO_CIK_CACHE)} ticker to CIK mappings")
```

4. **Modified Lookup Function**:
```python
def get_cik_for_ticker(ticker: str) -> Optional[str]:
    """
    Get the CIK number for a ticker symbol.
    """
    # First check database
    # Then check in-memory cache
    # Log warning if not found
```

### Expected Performance Improvement

This optimization reduces the number of API calls from O(n) to O(1), where n is the number of tickers being processed. For large batches of tickers, this significantly reduces:

1. Network overhead
2. API request latency
3. Risk of hitting SEC API rate limits

## Conclusion

The optimizations made to both the `process_company_facts` and `get_cik_for_ticker` functions should significantly improve the performance of the SEC company facts job. The changes focus on reducing database operations, using bulk operations, improving transaction management, and minimizing API calls.

These optimizations should help reduce the processing time for company facts, making the job more efficient and allowing it to process more tickers in less time.
