# Financial Calendar Job

## Overview
The Financial Calendar Job is responsible for fetching and processing economic calendar events such as CPI, PPI, Non-Farm Payroll, and other key economic indicators. The job can run in three modes:

1. **Standard Mode**: Fetches upcoming events and schedules dynamic cron jobs for each event
2. **Event-Specific Mode**: Monitors a specific event for data release
3. **Schedule-Only Mode**: Only schedules cron jobs without monitoring events

## Features
- Fetches economic calendar events from Alpha Vantage API
- Dynamically creates cron jobs for each upcoming event
- Monitors events at their scheduled release times
- Processes and stores events in MySQL database
- Supports multiple data sources with priority-based fallback
- Handles API errors and retries
- Provides detailed logging for monitoring

## Dynamic Cron Scheduling
The job uses a dynamic cron scheduling approach:

1. Fetch upcoming economic events with their dates and times
2. Create individual cron jobs for each event, scheduled for the event's release time
3. When an event's scheduled time arrives, the cron job executes to check for data release
4. After the event data is captured, the cron job is automatically removed

This approach is more efficient than continuous polling as it only runs when needed.

## Configuration
The following environment variables are available:

### Required Settings
- `ALPHA_VANTAGE_API_KEY`: API key for accessing Alpha Vantage data
- `MYSQL_HOST`: MySQL database host
- `MYSQL_USER`: MySQL database user
- `MYSQL_PASSWORD`: MySQL database password
- `MYSQL_DATABASE`: MySQL database name

### Optional Settings
- `FINANCIAL_CALENDAR_REAL_TIME`: Set to "true" to enable real-time monitoring mode
- `FINANCIAL_CALENDAR_CHECK_INTERVAL`: Interval in seconds between checks in real-time mode (default: 60)
- `FINANCIAL_CALENDAR_DAYS_AHEAD`: Number of days ahead to fetch events (default: 7)
- `FINANCIAL_CALENDAR_EVENT_TYPES`: Comma-separated list of event types to monitor (default: CPI,CORE_CPI,PPI,NFP,GDP,INTEREST_RATE)
- `FINANCIAL_CALENDAR_EVENT_ID`: ID of a specific event to monitor
- `FINANCIAL_CALENDAR_SCHEDULE_ONLY`: Set to "true" to only schedule events without monitoring
- `FINANCIAL_CALENDAR_CRON_USER`: User for crontab operations (leave empty for current user)

## Usage
To run the job in standard mode (fetch events and schedule cron jobs):

```bash
python run_financial_calendar_job.py
```

To monitor a specific event by ID:

```bash
python run_financial_calendar_job.py --event-id 123
```

To only schedule events without monitoring:

```bash
python run_financial_calendar_job.py --schedule-only
```

To run the job in legacy real-time monitoring mode:

```bash
python run_financial_calendar_job.py --real-time
```

To schedule the job using system cron:

```bash
# Run daily to fetch upcoming events and schedule monitoring
0 8 * * * /path/to/cron/financial_calendar_job.sh
```

## Data Structure
The job stores data in the following tables:

### FinancialCalendarEvent
| Column       | Type        | Description                     |
|--------------|-------------|---------------------------------|
| id           | BIGINT      | Primary key                     |
| event_name   | VARCHAR(255)| Full event name                 |
| event_type   | VARCHAR(100)| Event type (e.g., CPI, PPI)     |
| country      | VARCHAR(50) | Country code                    |
| date         | DATE        | Event date                      |
| time         | VARCHAR(50) | Time of day if available        |
| actual       | FLOAT       | Actual value when released      |
| forecast     | FLOAT       | Forecasted value                |
| previous     | FLOAT       | Previous period value           |
| impact       | VARCHAR(20) | Impact level (High/Medium/Low)  |
| is_released  | BOOLEAN     | Whether data has been released  |
| last_updated | DATETIME    | Last update timestamp           |

### EventMonitoringStatus
| Column           | Type        | Description                     |
|------------------|-------------|---------------------------------|
| id               | BIGINT      | Primary key                     |
| event_id         | BIGINT      | Reference to event              |
| monitoring_start | DATETIME    | When monitoring started         |
| monitoring_end   | DATETIME    | When monitoring completed       |
| status           | VARCHAR(20) | Status (pending/monitoring/completed) |
| last_checked     | DATETIME    | Last check timestamp            |
| next_check       | DATETIME    | Next scheduled check            |
| check_frequency  | INTEGER     | Seconds between checks          |

### JobRun
| Column           | Type        | Description                     |
|------------------|-------------|---------------------------------|
| id               | BIGINT      | Primary key                     |
| job_name         | VARCHAR(100)| Name of the job                 |
| start_time       | DATETIME    | Job start time                  |
| end_time         | DATETIME    | Job end time                    |
| status           | VARCHAR(20) | Status (running/completed/failed) |
| records_processed| INTEGER     | Number of records processed     |
| error_message    | TEXT        | Error message if failed         |

## Data Sources
The job supports multiple data sources for fetching financial calendar events. Each data source is stored in the database with the following attributes:

- **Name**: Name of the data source (e.g., "Alpha Vantage")
- **Type**: Type of the data source (e.g., "API")
- **URL**: Base URL for the data source
- **API Key**: API key for accessing the data source
- **Priority**: Priority of the data source (lower number = higher priority)
- **Is Active**: Whether the data source is active
- **Error Count**: Number of consecutive errors encountered
- **Last Used**: When the data source was last used

### Data Source Management

- **Automatic Creation**: If no data sources exist in the database, a default Alpha Vantage source is automatically created
- **Priority-Based Fallback**: Data sources are tried in priority order, with fallback to lower priority sources if higher priority sources fail
- **Error Tracking**: Error count is incremented for each failed request, and sources are automatically disabled after 3 consecutive errors
- **Source Refresh**: The list of active sources is refreshed at the start of each job run

### Adding New Data Sources

To add a new data source, insert a record into the `DataSource` table:

```sql
INSERT INTO DataSource (name, type, url, api_key, priority, is_active, error_count, last_used)
VALUES ('New Source', 'API', 'https://api.example.com', 'your-api-key', 2, true, 0, NOW());
```

## Error Handling
The job implements the following error handling:

- API connection errors: Retry with exponential backoff
- Data source errors: Track error count and disable sources after repeated failures
- Data validation errors: Skip invalid records and log errors
- Database errors: Log errors and continue processing
- Cron job errors: Log errors and continue with other scheduled events

## Testing
Run tests using:

```bash
pytest tests/test_financial_calendar_job.py
```

## Monitoring
Check job status and logs in:

- Application logs: `/var/log/financial_calendar_job.log`
- Database: `job_runs` table
- System crontab: `crontab -l` to view scheduled jobs
