# YFinance Rate Limiting Enhancement

This document describes the rate limiting enhancement for the YFinance API in the stock data job.

## Overview

The YFinance API occasionally returns 429 (Too Many Requests) errors when rate limits are exceeded. To handle these errors gracefully, we've implemented a robust rate limiting solution with the following features:

1. **Retry with Exponential Backoff**: Automatically retry failed requests with exponential backoff and jitter
2. **Adaptive Batch Sizing**: Dynamically adjust batch sizes based on rate limiting encounters
3. **Circuit Breaker Pattern**: Prevent cascading failures during extended rate limiting periods

## Implementation

The implementation consists of two main components:

1. **YFinanceWrapper**: A wrapper class that provides enhanced methods for interacting with the YFinance API
2. **Rate Limiting Decorator**: A decorator that adds retry logic to YFinance API calls

### YFinanceWrapper

The `YFinanceWrapper` class provides the following methods:

- `get_ticker_info(ticker_symbol)`: Get info for a single ticker with retry logic
- `get_ticker_history(ticker_symbol, period, interval)`: Get price history for a single ticker with retry logic
- `get_tickers_batch(ticker_symbols, batch_size)`: Get multiple tickers as a batch with retry logic
- `process_tickers_in_adaptive_batches(ticker_symbols, process_func, initial_batch_size, delay_between_batches)`: Process tickers in adaptive batches with rate limit handling

### Rate Limiting Decorator

The `with_rate_limit_retry` decorator adds retry logic with exponential backoff to any function that interacts with the YFinance API. When a `YFRateLimitError` is encountered, the decorator will:

1. Wait for an exponentially increasing amount of time (with jitter)
2. Retry the function call
3. If the maximum number of retries is exceeded, raise the exception

## Configuration

The rate limiting behavior can be configured using the following environment variables:

- `YF_MAX_RETRIES`: Maximum number of retry attempts (default: 5)
- `YF_MIN_WAIT`: Minimum wait time between retries in seconds (default: 2.0)
- `YF_MAX_WAIT`: Maximum wait time between retries in seconds (default: 60.0)

These variables can be set in the `.env` file or passed as environment variables.

## Usage

The `StockDataJob` class has been updated to use the `YFinanceWrapper` for all YFinance API calls. The wrapper is initialized with the rate limiting configuration in the constructor:

```python
self.yf_wrapper = YFinanceWrapper(
    max_retries=max_retries,
    min_wait=min_wait,
    max_wait=max_wait,
    batch_size_reduction_factor=0.5,
    min_batch_size=5
)
```

The `_process_tickers` method has been updated to use the wrapper's `process_tickers_in_adaptive_batches` method, which automatically adjusts the batch size based on rate limiting encounters.

## Benefits

This enhancement provides the following benefits:

1. **Resilience**: The job is more resilient to temporary rate limiting issues
2. **Efficiency**: Dynamic batch sizing optimizes throughput while respecting API limits
3. **Visibility**: Better logging provides insights into rate limiting patterns
4. **Maintainability**: Centralized handling of rate limiting makes the code more maintainable

## Logging

The wrapper logs detailed information about rate limiting events, including:

- When rate limiting is encountered
- Retry attempts and wait times
- Batch size adjustments

This information can be used to optimize the rate limiting configuration for your specific use case.
