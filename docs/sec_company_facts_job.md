# SEC Company Facts Job

This job fetches structured financial data from the SEC's XBRL API for US listed stocks and stores it in the database. The job focuses on storing only the most important financial metrics defined in `config/sec_concepts.py` rather than all available concepts.

> **Performance Optimization**: The `process_company_facts` function has been optimized for better performance. See [SEC Company Facts Performance Optimization](sec_company_facts_performance_optimization.md) for details.

## Overview

The SEC provides an API that allows access to structured financial data (XBRL) for all US listed companies. This data includes key financial metrics such as revenue, net income, assets, liabilities, and more. The data is structured and standardized, making it easy to compare across companies and time periods.

This job:
1. Fetches the CIK (Central Index Key) for each ticker
2. Retrieves company facts data from the SEC XBRL API
3. Filters the data to include only the important concepts defined in `config/sec_concepts.py`
4. Processes and stores the filtered data in the database

## Configuration

The job uses the following configuration settings:

- `SEC_XBRL_API_URL`: The URL for the SEC XBRL API
- `SEC_CIK_LOOKUP_URL`: The URL for the SEC CIK lookup API
- `SEC_API_ORGANIZATION`: The organization name for the SEC API user agent
- `SEC_API_EMAIL`: The email for the SEC API user agent

## Important Financial Metrics

The job only stores the most important financial metrics, which are defined in `config/sec_concepts.py`. These include:

### Income Statement Metrics
- Revenue
- Net Income
- EPS (Basic and Diluted)
- Operating Income
- Cost of Goods Sold
- Gross Profit
- Operating Expenses
- Research and Development Expense
- Selling, General, and Administrative Expense
- Interest Expense
- Interest Income

### Balance Sheet Metrics
- Assets (Total, Current, Non-current)
- Liabilities (Total, Current, Non-current)
- Cash and Cash Equivalents
- Inventory
- Accounts Receivable
- Property, Plant, and Equipment
- Goodwill
- Long-term Debt
- Stockholders' Equity

### Cash Flow Metrics
- Operating Cash Flow
- Investing Cash Flow
- Financing Cash Flow
- Capital Expenditures
- Dividends Paid

### Other Metrics
- Weighted Average Shares Outstanding (Basic and Diluted)

## Running the Job

### Command Line

```bash
# Run for all stocks in the database
python run_sec_company_facts_job.py

# Run for specific tickers
python run_sec_company_facts_job.py --tickers AAPL,MSFT,GOOGL

# Run for S&P 500 stocks
python run_sec_company_facts_job.py --index sp500
```

### Cron

The job can be scheduled to run periodically using cron:

```bash
# Run daily at 1 AM
0 1 * * * /path/to/go-gpt-job/cron/sec_company_facts_job.sh
```

## Database Schema

The job uses the following database tables:

- `sec_company_fact_metadata`: Stores metadata about the company facts collection
- `sec_company_facts`: Stores individual financial facts

## Example Queries

### Get Revenue for a Specific Company

```sql
SELECT 
    s.ticker,
    s.name,
    f.fiscal_year,
    f.fiscal_period,
    f.value_numeric,
    f.unit
FROM 
    sec_company_facts f
JOIN 
    sec_company_fact_metadata m ON f.metadata_id = m.id
JOIN 
    stocks s ON m.ticker = s.ticker
WHERE 
    f.taxonomy = 'us-gaap'
    AND f.concept = 'Revenue'
    AND s.ticker = 'AAPL'
ORDER BY 
    f.fiscal_year DESC, 
    CASE 
        WHEN f.fiscal_period = 'FY' THEN 5
        WHEN f.fiscal_period = 'Q4' THEN 4
        WHEN f.fiscal_period = 'Q3' THEN 3
        WHEN f.fiscal_period = 'Q2' THEN 2
        WHEN f.fiscal_period = 'Q1' THEN 1
        ELSE 0
    END DESC;
```

### Compare Revenue Growth Across Companies

```sql
WITH company_revenue AS (
    SELECT 
        s.ticker,
        s.name,
        f.fiscal_year,
        MAX(CASE WHEN f.fiscal_period = 'FY' THEN f.value_numeric ELSE NULL END) AS annual_revenue
    FROM 
        sec_company_facts f
    JOIN 
        sec_company_fact_metadata m ON f.metadata_id = m.id
    JOIN 
        stocks s ON m.ticker = s.ticker
    WHERE 
        f.taxonomy = 'us-gaap'
        AND f.concept = 'Revenue'
        AND f.fiscal_year >= 2020
    GROUP BY 
        s.ticker, s.name, f.fiscal_year
)
SELECT 
    ticker,
    name,
    fiscal_year,
    annual_revenue,
    LAG(annual_revenue) OVER (PARTITION BY ticker ORDER BY fiscal_year) AS prev_year_revenue,
    (annual_revenue / NULLIF(LAG(annual_revenue) OVER (PARTITION BY ticker ORDER BY fiscal_year), 0) - 1) * 100 AS growth_percent
FROM 
    company_revenue
WHERE 
    annual_revenue IS NOT NULL
ORDER BY 
    ticker, fiscal_year DESC;
```

## Rate Limiting

The SEC API has a rate limit of 10 requests per second. The job implements rate limiting to respect this limit.

## Timeout Handling

The job implements timeout handling for SEC API requests to prevent hanging indefinitely:

- Default timeout of 30 seconds for general API requests
- Extended timeout of 60 seconds for company facts requests
- Detailed logging of request timing and any timeout issues
- Specific error handling for timeout exceptions

## Checkpointing

The job implements a checkpointing mechanism to resume from where it left off if interrupted:

- Tracks the last successfully processed ticker in the database
- Creates a `sec_job_progress` table to store progress information
- When restarted, the job will resume from the ticker after the last processed one
- This is particularly useful for handling Cloud Run timeout issues

## Error Handling

The job implements error handling to handle API errors, network errors, database errors, and timeouts. It will log detailed error information including timing data to help diagnose issues.

## Logging

The job logs its progress and any errors to the standard output and to a log file. The enhanced logging includes:

- Detailed timing information for API requests
- Processing time for each ticker
- Overall job execution time
- Checkpoint information when resuming a job
