# SEC Filings HTML URL Support

## Overview

This document describes the implementation of HTML URL support for SEC filings in the go-gpt-job system. The system now stores both the TXT and HTML URLs for SEC filings, allowing users to access the filings in either format.

## Background

Previously, the system only stored the TXT URL for SEC filings, which looks like:
```
https://www.sec.gov/Archives/edgar/data/0000001750/000141057825000519/0001410578-25-000519.txt
```

However, users often prefer to view the HTML version of the filings, which has better formatting and is more readable. The HTML URL looks like:
```
https://www.sec.gov/Archives/edgar/data/320193/000032019323000106/aapl-20230930.htm
```

## Implementation

### Database Schema

The `sec_filings` table already had an `html_url` column, but it wasn't being populated. We've added a migration script to ensure this column exists in all environments:

```sql
-- Add html_url column to sec_filings table if it doesn't exist
SET @exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'sec_filings'
    AND column_name = 'html_url'
);

SET @query = IF(
    @exists = 0,
    'ALTER TABLE sec_filings ADD COLUMN html_url TEXT NULL AFTER url',
    'SELECT "html_url column already exists in sec_filings table"'
);

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
```

### SEC Filings Job

The `SECFilingsJob` class in `sec_filings_job.py` has been updated to populate the `html_url` field when creating new SEC filing records. The class now includes a new method `_build_html_filing_url` that attempts to determine the HTML URL for a filing:

1. First, it tries to extract the HTML URL from the TXT filing by looking for links to HTML documents.
2. If that fails, it constructs a URL based on the ticker and filing date.

```python
def _build_html_filing_url(self, cik: str, accession_number: str, ticker: str) -> Optional[str]:
    """
    Build URL for a filing in HTML format.
    
    Args:
        cik: CIK number
        accession_number: Accession number
        ticker: Ticker symbol
        
    Returns:
        Optional[str]: URL for the filing in HTML format or None if not found
    """
    # Implementation details...
```

### Update Script for Existing Records

A new script `update_sec_filings_html_url.py` has been created to update existing SEC filing records with HTML URLs. This script:

1. Queries the database for SEC filings without HTML URLs
2. Processes them in batches
3. For each filing, attempts to determine the HTML URL
4. Updates the database record with the HTML URL

The script can be run with various options:

```bash
python update_sec_filings_html_url.py --batch-size 100 --delay 0.1
```

## Usage

### Accessing HTML URLs

When retrieving SEC filings from the database, you can now access both the TXT and HTML URLs:

```python
filing = session.query(SECFiling).filter(SECFiling.id == filing_id).first()
txt_url = filing.url
html_url = filing.html_url
```

### Running the Update Script

To update existing SEC filings with HTML URLs, run:

```bash
cd go-gpt-job
./update_sec_filings_html_url.py
```

## Future Improvements

1. Improve the HTML URL detection algorithm to handle more edge cases
2. Add a fallback mechanism to try multiple HTML URL patterns if the first one fails
3. Add a validation step to verify that the HTML URL is valid before storing it
