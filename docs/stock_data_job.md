# Stock Data Job

This document describes the stock data job that fetches US stock market data and stores it in a MySQL database.

## Overview

The stock data job is a standalone Python script that fetches stock information from US exchanges using the Alpha Vantage Listing Status API to get the ticker list, and then uses the yfinance library to fetch detailed data for each ticker. The job also updates index membership information for each ticker by scraping data from Wikipedia pages for major indices (S&P 500, Nasdaq-100, Nasdaq Composite, Dow Jones, and Russell 2000). The data is stored in a MySQL database. The job is designed to be run periodically (e.g., daily after market close) to keep the stock data up to date.

## Features

- Fetches basic stock information (ticker, name, sector, industry, etc.)
- Fetches price history for a configurable number of days
- Fetches fundamental data (dividend rate, PE ratio, EPS, etc.)
- Updates index membership information (S&P 500, Nasdaq-100, Nasdaq Composite, Dow Jones, Russell 2000)
- Processes stocks in batches to avoid rate limiting
- Supports incremental updates (only updates changed data)
- Tracks job execution in the database
- Handles errors gracefully

## Database Schema

The job uses the following database tables:

1. **stocks** - Basic stock information
   - ticker (primary key)
   - name
   - indexes (comma-separated list of index memberships: SPX, NASDAQ100, NASDAQCOMP, DOW, RUSSELL2000)
   - sector
   - industry
   - exchange
   - country
   - market_cap
   - last_updated

2. **stock_prices** - Daily price data
   - id (primary key)
   - ticker (foreign key)
   - date
   - open
   - high
   - low
   - close
   - volume

3. **stock_fundamentals** - Fundamental data
   - id (primary key)
   - ticker (foreign key)
   - date
   - dividend_rate
   - dividend_yield
   - pe_ratio
   - eps
   - beta
   - fifty_two_week_high
   - fifty_two_week_low

4. **job_runs** - Track job execution
   - id (primary key)
   - job_name
   - start_time
   - end_time
   - status
   - records_processed
   - error_message

## Prerequisites

Before running the job, make sure you have:

1. MySQL database set up and running
2. Required Python packages installed (see requirements.txt)
3. Alpha Vantage API key (sign up at https://www.alphavantage.co/support/#api-key)
4. Internet access to Wikipedia for index membership data
5. Environment variables configured in .env file:
   - MYSQL_HOST
   - MYSQL_PORT
   - MYSQL_USER
   - MYSQL_PASSWORD
   - MYSQL_DATABASE
   - ALPHA_VANTAGE_API_KEY
   - ALPHA_VANTAGE_BASE_URL
   - ALPHA_VANTAGE_LISTING_FUNCTION

## Usage

### Running the Job

To run the job with default settings:

```bash
cd go-gpt-job
python jobs/stock_data_job.py
```

### Command Line Options

The job supports the following command line options:

- `--full-refresh`: Force refresh of all data (default: false)
- `--tickers`: Comma-separated list of specific tickers to process (default: all tickers from major indices)
- `--batch-size`: Number of stocks to process in each batch (default: 25)
- `--delay`: Delay between batches in seconds (default: 1.0)
- `--price-days`: Number of days of price history to fetch (default: 30)

Examples:

```bash
# Process only specific tickers
python jobs/stock_data_job.py --tickers AAPL,MSFT,GOOGL

# Process all tickers with a larger batch size
python jobs/stock_data_job.py --batch-size 50

# Force refresh of all data
python jobs/stock_data_job.py --full-refresh

# Fetch 60 days of price history
python jobs/stock_data_job.py --price-days 60
```

## Scheduling

To run the job automatically on a schedule, you can use cron (Linux/macOS) or Task Scheduler (Windows).

### Cron Example (Linux/macOS)

To run the job daily at 6:00 PM:

```bash
0 18 * * * cd /path/to/go-gpt-job && python jobs/stock_data_job.py >> /path/to/logs/stock_data_job.log 2>&1
```

### Docker Example

If you're running the application in Docker, you can add a cron job to the container or use a separate container for the job.

## Monitoring

The job logs its progress to both the console and a log file (`stock_data_job.log`). You can monitor the job by checking the log file or the `job_runs` table in the database.

## Troubleshooting

If the job fails, check the following:

1. MySQL connection settings in the .env file
2. Log file for error messages
3. `job_runs` table in the database for the error message
4. Network connectivity to the yfinance API

## Extending the Job

To extend the job with additional functionality:

1. Add new fields to the database schema in `jobs/db/schema.py`
2. Update the data fetching and storage logic in `jobs/stock_data_job.py`
3. Add new command line options in the `parse_args` function if needed

### Adding More Indices

To add more indices to track:

1. Create a new method in the `StockDataJob` class to fetch tickers for the index (e.g., `_get_russell2000_tickers`)
2. Update the `_update_index_memberships` method to include the new index
3. Add the new index identifier to the indexes field in the database (e.g., "RUSSELL")
