# SEC Filings Job

This document describes the SEC filings job, which fetches SEC filings data for US listed stocks and stores it in a MySQL database.

## Overview

The SEC filings job fetches SEC filings (10-K, 10-Q) for US listed stocks from the SEC EDGAR database. It extracts key sections from the filings (e.g., Risk Factors, Management's Discussion and Analysis, Business) and stores them in a MySQL database for later use.

## Features

- Fetches SEC filings for all US listed stocks (S&P 500, Nasdaq 100, Nasdaq Composite, <PERSON>, <PERSON> 2000)
- Extracts key sections from filings
- Handles rate limiting for the SEC API
- Processes stocks in batches to manage large numbers of tickers
- Supports incremental updates (only fetch new filings)
- Provides detailed logging of progress and errors

## Configuration

The job can be configured using environment variables or command-line arguments:

### Environment Variables

- `SEC_API_ORGANIZATION`: Organization name for SEC API requests (default: "Example-Organization")
- `SEC_API_EMAIL`: Email address for SEC API requests (default: "<EMAIL>")
- `SEC_API_RATE_LIMIT`: Rate limit for SEC API requests in requests per second (default: 10)
- `SEC_FILING_TYPES`: Comma-separated list of filing types to fetch (default: "10-K,10-Q")
- `SEC_FILING_SECTIONS`: Comma-separated list of sections to extract (default: "RISK_FACTORS,MD_AND_A,BUSINESS")
- `SEC_FILINGS_YEARS`: Number of years of filings to fetch (default: 3)
- `SEC_BATCH_SIZE`: Number of tickers to process in each batch (default: 5)
- `SEC_DELAY`: Delay between batches in seconds (default: 2.0)

### Command-Line Arguments

- `--full-refresh`: Force refresh of all data
- `--tickers`: Comma-separated list of specific tickers to process
- `--batch-size`: Number of stocks to process in each batch
- `--delay`: Delay between batches in seconds
- `--years`: Number of years of filings to fetch
- `--max-retries`: Maximum number of retry attempts for rate limiting
- `--min-wait`: Minimum wait time between retries in seconds
- `--max-wait`: Maximum wait time between retries in seconds

## Database Schema

The job uses the following database tables:

### SEC Filings Table

Stores metadata about SEC filings:

- `id`: Primary key
- `ticker`: Stock ticker symbol
- `cik`: SEC CIK number
- `accession_number`: SEC accession number
- `filing_type`: Filing type (e.g., 10-K, 10-Q)
- `filing_date`: Date the filing was submitted
- `report_date`: Date of the report
- `fiscal_year`: Fiscal year of the report
- `fiscal_quarter`: Fiscal quarter of the report (for 10-Q filings)
- `url`: URL to the filing
- `is_processed`: Whether the filing has been processed
- `last_updated`: Timestamp of the last update

### SEC Filing Sections Table

Stores sections extracted from SEC filings:

- `id`: Primary key
- `filing_id`: Foreign key to the SEC filings table
- `section_name`: Name of the section (e.g., RISK_FACTORS, MD_AND_A, BUSINESS)
- `section_text`: Text content of the section
- `last_updated`: Timestamp of the last update

## Usage

### Running the Job

To run the job:

```bash
python run_sec_filings_job.py
```

### Running with Specific Tickers

To run the job for specific tickers:

```bash
python run_sec_filings_job.py --tickers AAPL,MSFT,GOOGL
```

### Running with Full Refresh

To force a refresh of all data:

```bash
python run_sec_filings_job.py --full-refresh
```

### Running with Cron

To schedule the job to run periodically, use the provided cron script:

```bash
./cron/sec_filings_job.sh
```

You can add this script to your crontab to run it automatically:

```bash
# Run the SEC filings job every Sunday at 1:00 AM
0 1 * * 0 /path/to/go-gpt-job/cron/sec_filings_job.sh
```

## Limitations

- The SEC API has rate limits that must be respected
- Processing a large number of tickers can take a significant amount of time
- The section extraction logic may not work perfectly for all filings due to variations in formatting

## Troubleshooting

### Common Issues

- **Rate Limiting**: If you encounter rate limiting issues, try increasing the `SEC_DELAY` parameter
- **Missing Sections**: If sections are not being extracted correctly, check the section patterns in the `SECTION_MAPPING` dictionary
- **Database Connection Issues**: Ensure that the MySQL database is running and accessible

### Logs

The job logs its progress and errors to both the console and a log file (`sec_filings_job.log`). Check these logs for detailed information about any issues.
