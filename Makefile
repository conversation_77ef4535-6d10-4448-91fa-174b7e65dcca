.PHONY: install test run clean

# Default target
all: install

# Install dependencies
install:
	pip install -r requirements.txt

# Run tests
test:
	python -m unittest discover -s tests

# Run the stock data job
run:
	python run_stock_data_job.py

# Run the stock data job with specific tickers
run-specific:
	python run_stock_data_job.py --tickers AAPL,MSFT,GOOGL,AMZN,META

# Run the stock data job with full refresh
run-full-refresh:
	python run_stock_data_job.py --full-refresh

# Clean up generated files
clean:
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name "*.egg" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".coverage" -exec rm -rf {} +
	find . -type d -name "htmlcov" -exec rm -rf {} +
	find . -type d -name ".tox" -exec rm -rf {} +
	find . -type d -name "dist" -exec rm -rf {} +
	find . -type d -name "build" -exec rm -rf {} +
	rm -f stock_data_job.log
