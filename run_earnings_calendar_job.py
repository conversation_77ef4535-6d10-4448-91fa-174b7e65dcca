import logging
import argparse
from jobs.earnings_calendar_job import EarningsCalendarJob
from config.settings import Settings

def configure_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def parse_arguments():
    parser = argparse.ArgumentParser(description='Run the earnings calendar job')
    return parser.parse_args()

def main():
    # Parse command line arguments
    args = parse_arguments()
    
    # Configure logging
    configure_logging()
    
    # Load settings
    settings = Settings()
    
    # Initialize and run job
    job = EarningsCalendarJob(settings)
    job.run()

if __name__ == "__main__":
    main()
