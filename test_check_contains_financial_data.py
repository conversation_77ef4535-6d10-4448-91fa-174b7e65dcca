#!/usr/bin/env python3
"""
Test script for the check_contains_financial_data_by_gpt method in RealTimeFilingJob.
"""

from jobs.real_time_filing_job import RealTimeFilingJob
import unittest
from unittest.mock import patch, MagicMock

class TestCheckContainsFinancialData(unittest.TestCase):
    """Test cases for check_contains_financial_data_by_gpt method."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.job = RealTimeFilingJob()
    
    @patch('jobs.real_time_filing_job.do_gpt_query')
    @patch('jobs.real_time_filing_job.RealTimeFilingJob.get_text_from_url')
    def test_check_contains_financial_data_positive(self, mock_get_text, mock_gpt_query):
        """Test that financial data is correctly identified."""
        # Mock the get_text_from_url method to return sample text
        mock_get_text.return_value = """
        CONSOLIDATED BALANCE SHEETS
        (In millions, except share data)
        
        Assets                           December 31, 2024    December 31, 2023
        Current assets:
        Cash and cash equivalents        $        5,000       $        4,500
        Short-term investments                    2,000                1,800
        Accounts receivable, net                  3,500                3,200
        Inventories                               1,500                1,400
        Other current assets                        800                  750
        Total current assets             $       12,800       $       11,650
        """
        
        # Mock the GPT query response
        mock_gpt_query.return_value = '{"financial": true}'
        
        # Call the method
        result = self.job.check_contains_financial_data_by_gpt("http://example.com/filing.txt")
        
        # Verify the result
        self.assertEqual('{"financial": true}', result)
        
        # Verify the get_text_from_url method was called with the correct parameters
        mock_get_text.assert_called_once_with(
            "http://example.com/filing.txt",
            ignore_links=True,
            ignore_images=True,
            ignore_tables=False,
            body_width=0,
            timeout=60
        )
        
        # Verify the GPT query was called with the correct parameters
        mock_gpt_query.assert_called_once()
    
    @patch('jobs.real_time_filing_job.do_gpt_query')
    @patch('jobs.real_time_filing_job.RealTimeFilingJob.get_text_from_url')
    def test_check_contains_financial_data_negative(self, mock_get_text, mock_gpt_query):
        """Test that non-financial data is correctly identified."""
        # Mock the get_text_from_url method to return sample text
        mock_get_text.return_value = """
        EXHIBIT 99.1
        
        PRESS RELEASE
        
        Company XYZ Announces New Product Launch
        
        CITY, STATE - May 23, 2025 - Company XYZ (NYSE: XYZ) today announced the launch of its new product line.
        
        "We are excited to introduce our new product to the market," said John Doe, CEO of Company XYZ.
        
        About Company XYZ
        Company XYZ is a leading provider of innovative solutions for the industry.
        """
        
        # Mock the GPT query response
        mock_gpt_query.return_value = '{"financial": false}'
        
        # Call the method
        result = self.job.check_contains_financial_data_by_gpt("http://example.com/filing.txt")
        
        # Verify the result
        self.assertEqual('{"financial": false}', result)
        
        # Verify the get_text_from_url method was called with the correct parameters
        mock_get_text.assert_called_once_with(
            "http://example.com/filing.txt",
            ignore_links=True,
            ignore_images=True,
            ignore_tables=False,
            body_width=0,
            timeout=60
        )
        
        # Verify the GPT query was called with the correct parameters
        mock_gpt_query.assert_called_once()

if __name__ == "__main__":
    unittest.main()
