#!/usr/bin/env python3
"""
Tests for the SEC company facts job.
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import datetime
import json

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.sec_company_facts_job import SECCompanyFactsJob
from jobs.db.schema import SECCompanyFactMetadata, SECCompanyFact, JobRun

class TestSECCompanyFactsJob(unittest.TestCase):
    """
    Test cases for the SEC company facts job.
    """
    
    def setUp(self):
        """
        Set up test fixtures.
        """
        # Create a mock session
        self.mock_session = MagicMock()
        
        # Create a mock for get_db_session
        self.mock_get_db_session = MagicMock()
        self.mock_get_db_session.return_value.__enter__.return_value = self.mock_session
        self.mock_get_db_session.return_value.__exit__.return_value = None
        
        # Create a mock for apply_migrations
        self.mock_apply_migrations = MagicMock()
        
        # Create a mock for requests.get
        self.mock_requests_get = MagicMock()
        
        # Create a mock for JobRun
        self.mock_job_run = MagicMock()
        self.mock_job_run.id = 1
        
        # Set up JobRun.create_job_run to return the mock job run
        self.mock_create_job_run = MagicMock(return_value=self.mock_job_run)
        
        # Sample CIK response
        self.sample_cik_response = MagicMock()
        self.sample_cik_response.text = 'CIK=0000320193'
        self.sample_cik_response.raise_for_status = MagicMock()
        
        # Sample company facts response
        self.sample_company_facts = {
            'entityName': 'APPLE INC',
            'version': '1.0',
            'taxonomies': [
                {'taxonomy': 'us-gaap', 'version': '2021'}
            ],
            'facts': {
                'us-gaap': {
                    'Revenue': {
                        'label': 'Revenue',
                        'description': 'Revenue from sale of goods and services',
                        'units': {
                            'USD': [
                                {
                                    'val': 365817000000,
                                    'end': '2021-09-25',
                                    'filed': '2021-10-29',
                                    'form': '10-K',
                                    'frame': 'CY2021'
                                }
                            ]
                        }
                    }
                }
            }
        }
        
        self.sample_company_facts_response = MagicMock()
        self.sample_company_facts_response.json = MagicMock(return_value=self.sample_company_facts)
        self.sample_company_facts_response.raise_for_status = MagicMock()
    
    @patch('jobs.sec_company_facts_job.get_db_session')
    @patch('jobs.sec_company_facts_job.apply_migrations')
    @patch('jobs.sec_company_facts_job.JobRun.create_job_run')
    @patch('jobs.sec_company_facts_job.requests.get')
    def test_process_ticker(self, mock_requests_get, mock_create_job_run, mock_apply_migrations, mock_get_db_session):
        """
        Test processing a single ticker.
        """
        # Set up mocks
        mock_get_db_session.return_value.__enter__.return_value = self.mock_session
        mock_get_db_session.return_value.__exit__.return_value = None
        mock_create_job_run.return_value = self.mock_job_run
        
        # Set up requests.get to return different responses based on URL
        def mock_get(url, headers=None):
            if 'browse-edgar' in url:
                return self.sample_cik_response
            elif 'companyfacts' in url:
                return self.sample_company_facts_response
            else:
                raise ValueError(f"Unexpected URL: {url}")
        
        mock_requests_get.side_effect = mock_get
        
        # Create job instance with specific tickers
        job = SECCompanyFactsJob(specific_tickers=['AAPL'])
        
        # Mock the _get_tickers method to return our specific ticker
        job._get_tickers = MagicMock(return_value=['AAPL'])
        
        # Run the job
        job.run()
        
        # Verify that the job run was created
        mock_create_job_run.assert_called_once_with(self.mock_session, 'sec_company_facts_job')
        
        # Verify that the job run was completed
        self.mock_job_run.complete.assert_called_once()
        
        # Verify that the ticker was processed
        self.assertEqual(job.processed_count, 1)
        self.assertEqual(job.error_count, 0)
    
    @patch('jobs.sec_company_facts_job.get_db_session')
    @patch('jobs.sec_company_facts_job.apply_migrations')
    @patch('jobs.sec_company_facts_job.requests.get')
    def test_get_cik_by_ticker(self, mock_requests_get, mock_apply_migrations, mock_get_db_session):
        """
        Test getting CIK by ticker.
        """
        # Set up mocks
        mock_get_db_session.return_value.__enter__.return_value = self.mock_session
        mock_get_db_session.return_value.__exit__.return_value = None
        mock_requests_get.return_value = self.sample_cik_response
        
        # Create job instance
        job = SECCompanyFactsJob()
        
        # Get CIK by ticker
        cik = job._get_cik_by_ticker('AAPL')
        
        # Verify that the CIK was returned
        self.assertEqual(cik, '0000320193')
        
        # Verify that requests.get was called with the correct URL
        mock_requests_get.assert_called_once()
        args, kwargs = mock_requests_get.call_args
        self.assertIn('browse-edgar', args[0])
        self.assertIn('AAPL', args[0])
    
    @patch('jobs.sec_company_facts_job.get_db_session')
    @patch('jobs.sec_company_facts_job.apply_migrations')
    @patch('jobs.sec_company_facts_job.requests.get')
    def test_get_company_facts_by_cik(self, mock_requests_get, mock_apply_migrations, mock_get_db_session):
        """
        Test getting company facts by CIK.
        """
        # Set up mocks
        mock_get_db_session.return_value.__enter__.return_value = self.mock_session
        mock_get_db_session.return_value.__exit__.return_value = None
        mock_requests_get.return_value = self.sample_company_facts_response
        
        # Create job instance
        job = SECCompanyFactsJob()
        
        # Get company facts by CIK
        company_facts = job._get_company_facts_by_cik('0000320193')
        
        # Verify that the company facts were returned
        self.assertEqual(company_facts, self.sample_company_facts)
        
        # Verify that requests.get was called with the correct URL
        mock_requests_get.assert_called_once()
        args, kwargs = mock_requests_get.call_args
        self.assertIn('companyfacts', args[0])
        self.assertIn('0000320193', args[0])

if __name__ == '__main__':
    unittest.main()
