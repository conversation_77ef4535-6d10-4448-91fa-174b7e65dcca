#!/usr/bin/env python3
"""
Test script for the YFinance wrapper.

This script demonstrates the YFinance wrapper in action by fetching data for a list of tickers
and handling rate limiting gracefully.
"""

import os
import sys
import time
import logging
from dotenv import load_dotenv

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.yfinance_wrapper import YFinanceWrapper
from utils.logging_utils import logger

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

def main():
    """
    Main entry point.
    """
    # Load environment variables
    load_dotenv()
    
    # Get rate limiting configuration from environment variables or use defaults
    max_retries = int(os.getenv('YF_MAX_RETRIES', '5'))
    min_wait = float(os.getenv('YF_MIN_WAIT', '2.0'))
    max_wait = float(os.getenv('YF_MAX_WAIT', '60.0'))
    
    logger.info(f"Rate limiting configuration: max_retries={max_retries}, min_wait={min_wait}s, max_wait={max_wait}s")
    
    # Initialize the YFinance wrapper
    yf_wrapper = YFinanceWrapper(
        max_retries=max_retries,
        min_wait=min_wait,
        max_wait=max_wait,
        batch_size_reduction_factor=0.5,
        min_batch_size=5
    )
    
    # Define a list of tickers to test with
    tickers = [
        "AAPL", "MSFT", "GOOGL", "AMZN", "META", "TSLA", "NVDA", "JPM", "V", "PG",
        "UNH", "HD", "BAC", "MA", "XOM", "DIS", "ADBE", "CRM", "NFLX", "CMCSA"
    ]
    
    logger.info(f"Testing with {len(tickers)} tickers: {', '.join(tickers)}")
    
    # Test getting info for a single ticker
    logger.info("Testing get_ticker_info...")
    try:
        info = yf_wrapper.get_ticker_info("AAPL")
        logger.info(f"Successfully got info for AAPL: {info.get('shortName', 'Unknown')}")
    except Exception as e:
        logger.error(f"Error getting info for AAPL: {str(e)}")
    
    # Test getting history for a single ticker
    logger.info("Testing get_ticker_history...")
    try:
        history = yf_wrapper.get_ticker_history("AAPL", period="5d")
        logger.info(f"Successfully got history for AAPL: {len(history)} data points")
    except Exception as e:
        logger.error(f"Error getting history for AAPL: {str(e)}")
    
    # Test processing tickers in adaptive batches
    logger.info("Testing process_tickers_in_adaptive_batches...")
    
    def process_batch(tickers_data, batch_symbols):
        """Process a batch of tickers."""
        results = []
        for symbol in batch_symbols:
            try:
                # Get the current price
                ticker = tickers_data.tickers[symbol]
                info = ticker.info
                current_price = info.get('currentPrice', info.get('regularMarketPrice', 'Unknown'))
                results.append((symbol, current_price))
                logger.info(f"Processed {symbol}: Current price = {current_price}")
            except Exception as e:
                logger.error(f"Error processing {symbol}: {str(e)}")
        return results
    
    try:
        start_time = time.time()
        results = yf_wrapper.process_tickers_in_adaptive_batches(
            tickers,
            process_batch,
            initial_batch_size=10,
            delay_between_batches=1.0
        )
        end_time = time.time()
        
        logger.info(f"Successfully processed {len(tickers)} tickers in {end_time - start_time:.2f} seconds")
        logger.info(f"Final optimal batch size: {yf_wrapper.current_optimal_batch_size}")
        logger.info(f"Rate limit count: {yf_wrapper.rate_limit_count}")
    except Exception as e:
        logger.error(f"Error processing tickers: {str(e)}")
    
    logger.info("Test completed")

if __name__ == '__main__':
    main()
