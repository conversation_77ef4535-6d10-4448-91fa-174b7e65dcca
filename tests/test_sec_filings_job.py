#!/usr/bin/env python3
"""
Tests for the SEC filings job.
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import datetime

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.sec_filings_job import SECFilingsJob
from jobs.db.schema import SECFiling, SECFilingSection
from config.settings import settings


class TestSECFilingsJob(unittest.TestCase):
    """
    Tests for the SEC filings job.
    """
    
    @patch('jobs.sec_filings_job.get_db_session')
    @patch('jobs.sec_filings_job.apply_migrations')
    def setUp(self, mock_apply_migrations, mock_get_db_session):
        """
        Set up the test.
        """
        # Mock the database session
        self.mock_session = MagicMock()
        mock_get_db_session.return_value.__enter__.return_value = self.mock_session
        
        # Mock the SP500 tickers
        self.mock_session.query().all.return_value = [('AAPL',), ('MSFT',), ('GOOGL',)]
        
        # Create the job
        self.job = SECFilingsJob(
            full_refresh=False,
            specific_tickers=['AAPL'],
            batch_size=1,
            delay=0.1,
            years=1,
            max_retries=1,
            min_wait=0.1,
            max_wait=0.2
        )
    
    @patch('jobs.sec_filings_job.get_db_session')
    @patch('jobs.sec_filings_job.JobRun')
    def test_run(self, mock_job_run, mock_get_db_session):
        """
        Test the run method.
        """
        # Mock the database session
        mock_session = MagicMock()
        mock_get_db_session.return_value.__enter__.return_value = mock_session
        
        # Mock the job run
        mock_job_run_instance = MagicMock()
        mock_job_run.create_job_run.return_value = mock_job_run_instance
        
        # Mock the process_tickers method
        with patch.object(self.job, '_process_tickers') as mock_process_tickers:
            # Run the job
            result = self.job.run()
            
            # Check that the job run was created
            mock_job_run.create_job_run.assert_called_once_with(mock_session, "sec_filings_job")
            
            # Check that the tickers were processed
            mock_process_tickers.assert_called_once()
            
            # Check that the job run was completed
            mock_job_run_instance.complete.assert_called_once()
            
            # Check that the result is True
            self.assertTrue(result)
    
    def test_get_tickers(self):
        """
        Test the _get_tickers method.
        """
        # Test with specific tickers
        tickers = self.job._get_tickers()
        self.assertEqual(tickers, ['AAPL'])
        
        # Test without specific tickers
        self.job.specific_tickers = None
        tickers = self.job._get_tickers()
        self.assertIsInstance(tickers, list)
    
    @patch('jobs.sec_filings_job.get_db_session')
    @patch('jobs.sec_filings_job.SECFilingsJob._get_cik_by_ticker')
    @patch('jobs.sec_filings_job.SECFilingsJob._get_filings_by_cik')
    @patch('jobs.sec_filings_job.SECFilingsJob._process_filing')
    def test_process_ticker(self, mock_process_filing, mock_get_filings, mock_get_cik, mock_get_db_session):
        """
        Test the _process_ticker method.
        """
        # Mock the CIK
        mock_get_cik.return_value = '0000320193'
        
        # Mock the filings
        mock_get_filings.return_value = [
            {
                'accessionNumber': '0000320193-23-000077',
                'form': '10-K',
                'filingDate': '2023-11-03',
                'reportDate': '2023-09-30'
            }
        ]
        
        # Process the ticker
        self.job._process_ticker('AAPL')
        
        # Check that the CIK was fetched
        mock_get_cik.assert_called_once_with('AAPL')
        
        # Check that the filings were fetched
        mock_get_filings.assert_called_once_with('0000320193')
        
        # Check that the filing was processed
        mock_process_filing.assert_called_once_with('AAPL', '0000320193', mock_get_filings.return_value[0])
    
    @patch('jobs.sec_filings_job.get_db_session')
    @patch('jobs.sec_filings_job.SECFiling')
    @patch('jobs.sec_filings_job.SECFilingSection')
    @patch('jobs.sec_filings_job.SECFilingsJob._get_filing_content')
    @patch('jobs.sec_filings_job.SECFilingsJob._extract_sections')
    def test_process_filing(self, mock_extract_sections, mock_get_content, mock_section_class, mock_filing_class, mock_get_db_session):
        """
        Test the _process_filing method.
        """
        # Mock the database session
        mock_session = MagicMock()
        mock_get_db_session.return_value.__enter__.return_value = mock_session
        
        # Mock the filing
        mock_filing = {
            'accessionNumber': '0000320193-23-000077',
            'form': '10-K',
            'filingDate': '2023-11-03',
            'reportDate': '2023-09-30'
        }
        
        # Mock the existing filing
        mock_existing_filing = MagicMock()
        mock_existing_filing.id = 1
        mock_existing_filing.is_processed = False
        mock_session.query().filter().first.return_value = mock_existing_filing
        
        # Mock the filing content
        mock_get_content.return_value = 'Filing content'
        
        # Mock the extracted sections
        mock_extract_sections.return_value = {
            'RISK_FACTORS': 'Risk factors content',
            'MD_AND_A': 'MD&A content'
        }
        
        # Mock the existing section
        mock_existing_section = MagicMock()
        mock_session.query().filter().first.side_effect = [mock_existing_filing, mock_existing_section, None]
        
        # Process the filing
        self.job._process_filing('AAPL', '0000320193', mock_filing)
        
        # Check that the filing content was fetched
        mock_get_content.assert_called_once_with('0000320193', '0000320193-23-000077')
        
        # Check that the sections were extracted
        mock_extract_sections.assert_called_once_with('Filing content')
        
        # Check that the existing section was updated
        self.assertEqual(mock_existing_section.section_text, 'Risk factors content')
        
        # Check that a new section was created
        mock_section_class.assert_called_once()
        
        # Check that the filing was marked as processed
        self.assertTrue(mock_existing_filing.is_processed)
        
        # Check that the changes were committed
        mock_session.commit.assert_called()
    
    @patch('jobs.sec_filings_job.requests.get')
    def test_get_cik_by_ticker(self, mock_get):
        """
        Test the _get_cik_by_ticker method.
        """
        # Mock the response
        mock_response = MagicMock()
        mock_response.text = 'CIK=0000320193'
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response
        
        # Get the CIK
        cik = self.job._get_cik_by_ticker('AAPL')
        
        # Check that the CIK is correct
        self.assertEqual(cik, '0000320193')
        
        # Check that the request was made with the correct URL
        mock_get.assert_called_once()
        self.assertIn('AAPL', mock_get.call_args[0][0])
    
    @patch('jobs.sec_filings_job.requests.get')
    def test_get_filings_by_cik(self, mock_get):
        """
        Test the _get_filings_by_cik method.
        """
        # Mock the response
        mock_response = MagicMock()
        mock_response.content = b'''{
            "filings": {
                "recent": {
                    "accessionNumber": ["0000320193-23-000077"],
                    "form": ["10-K"],
                    "filingDate": ["2023-11-03"],
                    "reportDate": ["2023-09-30"]
                }
            }
        }'''
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response
        
        # Get the filings
        filings = self.job._get_filings_by_cik('0000320193')
        
        # Check that the filings are correct
        self.assertEqual(len(filings), 1)
        self.assertEqual(filings[0]['accessionNumber'], '0000320193-23-000077')
        self.assertEqual(filings[0]['form'], '10-K')
        self.assertEqual(filings[0]['filingDate'], '2023-11-03')
        self.assertEqual(filings[0]['reportDate'], '2023-09-30')
        
        # Check that the request was made with the correct URL
        mock_get.assert_called_once()
        self.assertIn('0000320193', mock_get.call_args[0][0])
    
    def test_build_filing_url(self):
        """
        Test the _build_filing_url method.
        """
        # Build the URL
        url = self.job._build_filing_url('0000320193', '0000320193-23-000077')
        
        # Check that the URL is correct
        self.assertEqual(url, 'https://www.sec.gov/Archives/edgar/data/0000320193/000032019323000077/0000320193-23-000077.txt')
    
    @patch('jobs.sec_filings_job.requests.get')
    def test_get_filing_content(self, mock_get):
        """
        Test the _get_filing_content method.
        """
        # Mock the response
        mock_response = MagicMock()
        mock_response.text = 'Filing content'
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response
        
        # Get the filing content
        content = self.job._get_filing_content('0000320193', '0000320193-23-000077')
        
        # Check that the content is correct
        self.assertEqual(content, 'Filing content')
        
        # Check that the request was made with the correct URL
        mock_get.assert_called_once()
        self.assertIn('0000320193', mock_get.call_args[0][0])
        self.assertIn('0000320193-23-000077', mock_get.call_args[0][0])
    
    def test_extract_sections(self):
        """
        Test the _extract_sections method.
        """
        # Mock the filing content
        filing_content = '''
        Item 1. Business
        This is the business section.
        
        Item 1A. Risk Factors
        This is the risk factors section.
        
        Item 7. Management's Discussion and Analysis
        This is the MD&A section.
        '''
        
        # Extract the sections
        sections = self.job._extract_sections(filing_content)
        
        # Check that the sections are correct
        self.assertEqual(len(sections), 3)
        self.assertIn('BUSINESS', sections)
        self.assertIn('RISK_FACTORS', sections)
        self.assertIn('MD_AND_A', sections)
        self.assertIn('This is the business section.', sections['BUSINESS'])
        self.assertIn('This is the risk factors section.', sections['RISK_FACTORS'])
        self.assertIn('This is the MD&A section.', sections['MD_AND_A'])
    
    def test_extract_section(self):
        """
        Test the _extract_section method.
        """
        # Mock the filing content
        filing_content = '''
        Item 1. Business
        This is the business section.
        
        Item 1A. Risk Factors
        This is the risk factors section.
        
        Item 7. Management's Discussion and Analysis
        This is the MD&A section.
        '''
        
        # Extract the sections
        business_section = self.job._extract_section(filing_content, 'BUSINESS')
        risk_factors_section = self.job._extract_section(filing_content, 'RISK_FACTORS')
        mda_section = self.job._extract_section(filing_content, 'MD_AND_A')
        
        # Check that the sections are correct
        self.assertIn('This is the business section.', business_section)
        self.assertIn('This is the risk factors section.', risk_factors_section)
        self.assertIn('This is the MD&A section.', mda_section)


class TestStripHTMLTags(TestSECFilingsJob):
    """
    Test the _strip_html_tags method functionality.
    """

    @patch('html2text.html2text')
    def test_html2text_called_correctly(self, mock_html2text):
        """Test that html2text is called with correct parameters."""
        mock_html2text.return_value = "clean text"
        result = self.job._strip_html_tags("<p>Test HTML</p>")
        
        mock_html2text.assert_called_once_with("<p>Test HTML</p>")
        self.assertEqual(result, "clean text")

    @patch('html2text.html2text')
    def test_simple_html(self, mock_html2text_module):
        """Test stripping simple HTML tags."""
        mock_html2text_module.return_value = "This is a simple paragraph."
        
        html = "<p>This is a simple paragraph.</p>"
        result = self.job._strip_html_tags(html)
        
        self.assertEqual(result, "This is a simple paragraph.")

    @patch('html2text.html2text')
    def test_complex_html(self, mock_html2text_module):
        """Test stripping complex HTML with nested tags and formatting."""
        mock_html2text_module.return_value = "Header\n\nThis is a paragraph with bold and italic text."
        
        html = """
        <div class="section">
            <h1>Header</h1>
            <p>This is a paragraph with <strong>bold</strong> and <em>italic</em> text.</p>
        </div>
        """
        result = self.job._strip_html_tags(html)
        
        self.assertEqual(result, "Header\n\nThis is a paragraph with bold and italic text.")

    @patch('html2text.html2text')
    def test_html_with_tables(self, mock_html2text_module):
        """Test stripping HTML that contains tables."""
        mock_html2text_module.return_value = "Header\n\nCol1 | Col2\n-----|-----\nData1 | Data2"
        
        html = """
        <h2>Header</h2>
        <table>
            <tr><th>Col1</th><th>Col2</th></tr>
            <tr><td>Data1</td><td>Data2</td></tr>
        </table>
        """
        result = self.job._strip_html_tags(html)
        
        self.assertEqual(result, "Header\n\nCol1 | Col2\n-----|-----\nData1 | Data2")

    @patch('html2text.html2text')
    def test_html_with_entities(self, mock_html2text_module):
        """Test handling of HTML entities."""
        mock_html2text_module.return_value = "This & that with spaces"
        
        html = "This &amp; that with&nbsp;spaces"
        result = self.job._strip_html_tags(html)
        
        self.assertEqual(result, "This & that with spaces")

    @patch('html2text.html2text')
    def test_strip_whitespace(self, mock_html2text_module):
        """Test that the method properly strips leading/trailing whitespace."""
        mock_html2text_module.return_value = "\n  Test content with whitespace  \n"
        
        html = "<div>  Test content with whitespace  </div>"
        result = self.job._strip_html_tags(html)
        
        self.assertEqual(result, "Test content with whitespace")


if __name__ == '__main__':
    unittest.main()
