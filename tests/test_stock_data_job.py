"""
Test module for the stock data job.
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import datetime
import io
import pandas as pd
import requests

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.stock_data_job import StockDataJob
from jobs.db.schema import Stock, StockPrice, StockFundamental, JobRun, DelistTicker


class TestStockDataJob(unittest.TestCase):
    """
    Test class for the stock data job.
    """
    
    @patch('jobs.stock_data_job.MySQLClient')
    def setUp(self, mock_mysql_client):
        """
        Set up the test case.
        """
        self.mock_mysql_client = mock_mysql_client
        self.job = StockDataJob(
            full_refresh=False,
            specific_tickers=['AAPL', 'MSFT'],
            batch_size=2,
            delay=0.1,
            price_days=5
        )
        
        # Mock the database session
        self.mock_session = MagicMock()
        self.mock_session_context = MagicMock()
        self.mock_session_context.__enter__.return_value = self.mock_session
        self.mock_session_context.__exit__.return_value = None
        
        # Mock the job run
        self.mock_job_run = MagicMock()
        self.mock_job_run.id = 1
        
        # Mock the query results
        self.mock_query = MagicMock()
        self.mock_session.query.return_value = self.mock_query
        self.mock_query.filter.return_value = self.mock_query
        self.mock_query.first.return_value = None
    
    @patch('jobs.stock_data_job.get_db_session')
    @patch('jobs.stock_data_job.JobRun')
    def test_run_creates_job_run(self, mock_job_run_class, mock_get_db_session):
        """
        Test that the run method creates a job run record.
        """
        # Mock the get_db_session function
        mock_get_db_session.return_value = self.mock_session_context
        
        # Mock the JobRun.create_job_run method
        mock_job_run_class.create_job_run.return_value = self.mock_job_run
        
        # Mock the _get_tickers method
        with patch.object(self.job, '_get_tickers', return_value=['AAPL', 'MSFT']):
            # Mock the _process_tickers method
            with patch.object(self.job, '_process_tickers'):
                # Run the job
                result = self.job.run()
                
                # Assert that the job run was created
                mock_job_run_class.create_job_run.assert_called_once_with(
                    self.mock_session, "stock_data_job"
                )
                
                # Assert that the job run was completed
                self.mock_job_run.complete.assert_called_once()
                
                # Assert that the result is True
                self.assertTrue(result)
    
    @patch('jobs.stock_data_job.get_db_session')
    @patch('jobs.stock_data_job.JobRun')
    def test_run_handles_error(self, mock_job_run_class, mock_get_db_session):
        """
        Test that the run method handles errors gracefully.
        """
        # Mock the get_db_session function
        mock_get_db_session.return_value = self.mock_session_context
        
        # Mock the JobRun.create_job_run method
        mock_job_run_class.create_job_run.return_value = self.mock_job_run
        
        # Mock the _get_tickers method to raise an exception
        with patch.object(self.job, '_get_tickers', side_effect=Exception("Test error")):
            # Run the job
            result = self.job.run()
            
            # Assert that the job run was created
            mock_job_run_class.create_job_run.assert_called_once_with(
                self.mock_session, "stock_data_job"
            )
            
            # Assert that the job run was marked as failed
            self.mock_job_run.fail.assert_called_once()
            
            # Assert that the result is False
            self.assertFalse(result)
    
    def test_get_tickers_with_specific_tickers(self):
        """
        Test that the _get_tickers method returns the specific tickers when provided.
        """
        tickers = self.job._get_tickers()
        self.assertEqual(tickers, ['AAPL', 'MSFT'])
    
    @patch('jobs.stock_data_job.requests.get')
    @patch('jobs.stock_data_job.settings')
    def test_get_delisted_tickers_fetches_from_alpha_vantage(self, mock_settings, mock_requests_get):
        """
        Test that the _get_delisted_tickers method fetches delisted tickers from Alpha Vantage API.
        """
        # Create a job
        job = StockDataJob()
        
        # Mock the settings
        mock_settings.ALPHA_VANTAGE_API_KEY = 'test_api_key'
        mock_settings.ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query'
        mock_settings.ALPHA_VANTAGE_LISTING_FUNCTION = 'LISTING_STATUS'
        
        # Create mock response
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_response.text = 'symbol,name,exchange,assetType,ipoDate,delistingDate,status\nDEL1,Delisted Company 1,NYSE,Stock,1980-12-12,2022-01-01,Delisted\nDEL2,Delisted Company 2,NASDAQ,Stock,1986-03-13,2022-02-01,Delisted\nDEL3,Delisted Company 3,NASDAQ,Stock,2004-08-19,2022-03-01,Delisted'
        mock_requests_get.return_value = mock_response
        
        # Mock the database session
        with patch('jobs.stock_data_job.get_db_session') as mock_get_db_session:
            mock_session = MagicMock()
            mock_session_context = MagicMock()
            mock_session_context.__enter__.return_value = mock_session
            mock_session_context.__exit__.return_value = None
            mock_get_db_session.return_value = mock_session_context
            
            # Mock the query results
            mock_query = MagicMock()
            mock_session.query.return_value = mock_query
            mock_query.all.return_value = []
            
            # Get the delisted tickers
            delisted_tickers = job._get_delisted_tickers()
            
            # Assert that the delisted tickers were fetched from Alpha Vantage
            self.assertEqual(set(delisted_tickers), {'DEL1', 'DEL2', 'DEL3'})
            
            # Assert that requests.get was called with the correct URL
            mock_requests_get.assert_called_once_with(
                'https://www.alphavantage.co/query?function=LISTING_STATUS&state=delisted&apikey=test_api_key'
            )
            
            # Assert that the delisted tickers were stored in the database
            self.assertEqual(mock_session.add.call_count, 3)
            mock_session.commit.assert_called_once()

    @patch('jobs.stock_data_job.requests.get')
    @patch('jobs.stock_data_job.settings')
    def test_get_tickers_fetches_from_alpha_vantage(self, mock_settings, mock_requests_get):
        """
        Test that the _get_tickers method fetches tickers from Alpha Vantage API when no specific tickers are provided.
        """
        # Create a job with no specific tickers
        job = StockDataJob(full_refresh=False, specific_tickers=None)
        
        # Mock the settings
        mock_settings.ALPHA_VANTAGE_API_KEY = 'test_api_key'
        mock_settings.ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query'
        mock_settings.ALPHA_VANTAGE_LISTING_FUNCTION = 'LISTING_STATUS'
        
        # Create mock response
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_response.text = 'symbol,name,exchange,assetType,ipoDate,delistingDate,status\nAAPL,Apple Inc,NYSE,Stock,1980-12-12,,Active\nMSFT,Microsoft Corporation,NASDAQ,Stock,1986-03-13,,Active\nGOOGL,Alphabet Inc,NASDAQ,Stock,2004-08-19,,Active\nAMZN,Amazon.com Inc,NASDAQ,Stock,1997-05-15,,Active'
        mock_requests_get.return_value = mock_response
        
        # Create mock DataFrame
        mock_df = pd.read_csv(io.StringIO(mock_response.text))
        
        # Mock the _get_delisted_tickers method to return a list of delisted tickers
        with patch.object(job, '_get_delisted_tickers', return_value=['DEL1', 'DEL2', 'DEL3']):
            # Get the tickers
            tickers = job._get_tickers()
            
            # Assert that the tickers were fetched from Alpha Vantage
            self.assertEqual(set(tickers), {'AAPL', 'MSFT', 'GOOGL', 'AMZN'})
            
            # Assert that requests.get was called with the correct URL
            mock_requests_get.assert_called_once_with(
                'https://www.alphavantage.co/query?function=LISTING_STATUS&state=active&apikey=test_api_key'
            )
    
    @patch('jobs.stock_data_job.requests.get')
    @patch('jobs.stock_data_job.settings')
    def test_get_tickers_merges_and_deduplicates_delisted_tickers(self, mock_settings, mock_requests_get):
        """
        Test that the _get_tickers method merges and deduplicates delisted tickers from both DB and API.
        """
        # Create a job with no specific tickers
        job = StockDataJob(full_refresh=False, specific_tickers=None)
        
        # Mock the settings
        mock_settings.ALPHA_VANTAGE_API_KEY = 'test_api_key'
        mock_settings.ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query'
        mock_settings.ALPHA_VANTAGE_LISTING_FUNCTION = 'LISTING_STATUS'
        
        # Create mock response with active tickers
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_response.text = 'symbol,name,exchange,assetType,ipoDate,delistingDate,status\nAAPL,Apple Inc,NYSE,Stock,1980-12-12,,Active\nMSFT,Microsoft Corporation,NASDAQ,Stock,1986-03-13,,Active\nGOOGL,Alphabet Inc,NASDAQ,Stock,2004-08-19,,Active\nAMZN,Amazon.com Inc,NASDAQ,Stock,1997-05-15,,Active\nDEL1,Delisted Company 1,NYSE,Stock,1980-12-12,,Active'
        mock_requests_get.return_value = mock_response
        
        # Mock the database session to return DB delisted tickers
        with patch('jobs.stock_data_job.get_db_session') as mock_get_db_session:
            # First session for getting DB delisted tickers
            mock_db_session = MagicMock()
            mock_db_session_context = MagicMock()
            mock_db_session_context.__enter__.return_value = mock_db_session
            mock_db_session_context.__exit__.return_value = None
            mock_get_db_session.return_value = mock_db_session_context
            
            # Mock the query results for DB delisted tickers
            mock_db_query = MagicMock()
            mock_db_session.query.return_value = mock_db_query
            # DB has DEL1 and DEL2 as delisted
            mock_db_query.all.return_value = [('DEL1',), ('DEL2',)]
            
            # Mock the _get_delisted_tickers method to return API delisted tickers
            # API has DEL2 and DEL3 as delisted
            with patch.object(job, '_get_delisted_tickers', return_value=['DEL2', 'DEL3']):
                # Get the tickers
                tickers = job._get_tickers()
                
                # Assert that all delisted tickers (DEL1, DEL2, DEL3) are excluded from the active tickers
                self.assertEqual(set(tickers), {'AAPL', 'MSFT', 'GOOGL', 'AMZN'})
                self.assertNotIn('DEL1', tickers)  # From DB only
                self.assertNotIn('DEL2', tickers)  # From both DB and API
                self.assertNotIn('DEL3', tickers)  # From API only
                
                # Assert that requests.get was called with the correct URL
                mock_requests_get.assert_called_once_with(
                    'https://www.alphavantage.co/query?function=LISTING_STATUS&state=active&apikey=test_api_key'
                )
                
                # Assert that the database was queried for delisted tickers
                mock_db_session.query.assert_called_with(DelistTicker.ticker)
    
    @patch('jobs.stock_data_job.pd.read_html')
    def test_get_sp500_tickers(self, mock_read_html):
        """
        Test that the _get_sp500_tickers method fetches S&P 500 tickers from Wikipedia.
        """
        # Create a job
        job = StockDataJob()
        
        # Create mock DataFrame
        mock_df = pd.DataFrame({
            'Symbol': ['AAPL', 'MSFT', 'GOOGL', 'AMZN']
        })
        
        # Mock the pandas read_html function
        mock_read_html.return_value = [mock_df]
        
        # Get the tickers
        tickers = job._get_sp500_tickers()
        
        # Assert that the tickers were fetched
        self.assertEqual(set(tickers), {'AAPL', 'MSFT', 'GOOGL', 'AMZN'})
        
        # Assert that read_html was called with the correct URL
        mock_read_html.assert_called_once_with('https://en.wikipedia.org/wiki/List_of_S%26P_500_companies')
    
    @patch('jobs.stock_data_job.pd.read_html')
    def test_get_nasdaq_tickers(self, mock_read_html):
        """
        Test that the _get_nasdaq_tickers method fetches Nasdaq-100 tickers from Wikipedia.
        """
        # Create a job
        job = StockDataJob()
        
        # Create mock DataFrame
        mock_df = pd.DataFrame({
            'Ticker': ['AAPL', 'MSFT', 'GOOGL', 'AMZN']
        })
        
        # Mock the pandas read_html function to return multiple tables
        mock_read_html.return_value = [
            pd.DataFrame(),  # First table
            pd.DataFrame(),  # Second table
            pd.DataFrame(),  # Third table
            pd.DataFrame(),  # Fourth table
            mock_df          # Fifth table (index 4)
        ]
        
        # Get the tickers
        tickers = job._get_nasdaq_tickers()
        
        # Assert that the tickers were fetched
        self.assertEqual(set(tickers), {'AAPL', 'MSFT', 'GOOGL', 'AMZN'})
        
        # Assert that read_html was called with the correct URL
        mock_read_html.assert_called_once_with('https://en.wikipedia.org/wiki/Nasdaq-100')
    
    @patch('jobs.stock_data_job.pd.read_html')
    def test_get_russell2000_tickers(self, mock_read_html):
        """
        Test that the _get_russell2000_tickers method fetches Russell 2000 tickers from Wikipedia.
        """
        # Create a job
        job = StockDataJob()
        
        # Create mock DataFrame
        mock_df = pd.DataFrame({
            'Ticker': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'PYPL', 'ADBE', 'NFLX', 'INTC']
        })
        
        # Mock the pandas read_html function
        mock_read_html.return_value = [mock_df]
        
        # Get the tickers
        tickers = job._get_russell2000_tickers()
        
        # Assert that the tickers were fetched
        self.assertEqual(set(tickers), {'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'PYPL', 'ADBE', 'NFLX', 'INTC'})
        
        # Assert that read_html was called with the correct URL
        mock_read_html.assert_called_once_with('https://en.wikipedia.org/wiki/Russell_2000_Index')
    
    @patch('jobs.stock_data_job.requests.get')
    @patch('jobs.stock_data_job.BeautifulSoup')
    def test_get_nasdaq_composite_tickers(self, mock_bs, mock_requests_get):
        """
        Test that the _get_nasdaq_composite_tickers method fetches Nasdaq Composite tickers from Wikipedia.
        """
        # Create a job
        job = StockDataJob()
        
        # Create mock response
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_response.text = '<html><body>Mock HTML content</body></html>'
        mock_requests_get.return_value = mock_response
        
        # Create mock BeautifulSoup object
        mock_soup = MagicMock()
        mock_bs.return_value = mock_soup
        
        # Create mock category content
        mock_category_content = MagicMock()
        mock_soup.find.return_value = mock_category_content
        
        # Create mock links
        mock_link1 = MagicMock()
        mock_link1.text = 'Apple Inc.'
        mock_link2 = MagicMock()
        mock_link2.text = 'Microsoft Corporation'
        mock_link3 = MagicMock()
        mock_link3.text = 'Alphabet Inc.'
        mock_link4 = MagicMock()
        mock_link4.text = 'previous page'  # Navigation link that should be skipped
        
        mock_category_content.find_all.return_value = [mock_link1, mock_link2, mock_link3, mock_link4]
        
        # Get the tickers
        tickers = job._get_nasdaq_composite_tickers()
        
        # Assert that the tickers were fetched
        self.assertTrue(len(tickers) > 0)
        self.assertIn('AAPL', tickers)
        self.assertIn('MSFT', tickers)
        self.assertIn('GOOGL', tickers)
        
        # Assert that requests.get was called with the correct URL
        mock_requests_get.assert_called_once_with(
            'https://en.wikipedia.org/wiki/Category:Companies_listed_on_the_Nasdaq'
        )
        
        # Assert that BeautifulSoup was called with the correct parameters
        mock_bs.assert_called_once_with(mock_response.text, 'html.parser')
        
        # Assert that we searched for the category content
        mock_soup.find.assert_called_once_with('div', {'id': 'mw-pages'})
        
        # Assert that we extracted links from the category content
        mock_category_content.find_all.assert_called_once_with('a')
    
    @patch('jobs.stock_data_job.pd.read_html')
    def test_get_dow_tickers(self, mock_read_html):
        """
        Test that the _get_dow_tickers method fetches Dow Jones tickers from Wikipedia.
        """
        # Create a job
        job = StockDataJob()
        
        # Create mock DataFrame
        mock_df = pd.DataFrame({
            'Symbol': ['AAPL', 'MSFT', 'JPM', 'GS']
        })
        
        # Mock the pandas read_html function
        mock_read_html.return_value = [mock_df]
        
        # Get the tickers
        tickers = job._get_dow_tickers()
        
        # Assert that the tickers were fetched
        self.assertEqual(set(tickers), {'AAPL', 'MSFT', 'JPM', 'GS'})
        
        # Assert that read_html was called with the correct URL
        mock_read_html.assert_called_once_with('https://en.wikipedia.org/wiki/Dow_Jones_Industrial_Average')
    
    @patch('jobs.stock_data_job.get_db_session')
    def test_update_index_memberships(self, mock_get_db_session):
        """
        Test that the _update_index_memberships method updates index memberships in the database.
        """
        # Create a job
        job = StockDataJob()
        
        # Mock the database session
        mock_session = MagicMock()
        mock_session_context = MagicMock()
        mock_session_context.__enter__.return_value = mock_session
        mock_session_context.__exit__.return_value = None
        mock_get_db_session.return_value = mock_session_context
        
        # Mock the stocks in the database
        mock_stock1 = MagicMock(ticker='AAPL')
        mock_stock2 = MagicMock(ticker='MSFT')
        mock_stock3 = MagicMock(ticker='JPM')
        mock_stock4 = MagicMock(ticker='GS')
        mock_stock5 = MagicMock(ticker='TSLA')
        mock_stock6 = MagicMock(ticker='NFLX')
        mock_session.query.return_value.all.return_value = [
            mock_stock1, mock_stock2, mock_stock3, mock_stock4, mock_stock5, mock_stock6
        ]
        
        # Mock the index ticker methods
        with patch.object(job, '_get_sp500_tickers', return_value=['AAPL', 'MSFT']):
            with patch.object(job, '_get_nasdaq100_tickers', return_value=['AAPL', 'MSFT']):
                with patch.object(job, '_get_nasdaq_composite_tickers', return_value=['AAPL', 'MSFT', 'NFLX']):
                    with patch.object(job, '_get_dow_tickers', return_value=['JPM', 'GS']):
                        with patch.object(job, '_get_russell2000_tickers', return_value=['TSLA', 'GS']):
                            # Update index memberships
                            job._update_index_memberships()
                            
                            # Assert that the indexes were updated correctly
                            self.assertEqual(mock_stock1.indexes, 'SPX,NASDAQ100,NASDAQCOMP')
                            self.assertEqual(mock_stock2.indexes, 'SPX,NASDAQ100,NASDAQCOMP')
                            self.assertEqual(mock_stock3.indexes, 'DOW')
                            self.assertEqual(mock_stock4.indexes, 'DOW,RUSSELL2000')
                            self.assertEqual(mock_stock5.indexes, 'RUSSELL2000')
                            self.assertEqual(mock_stock6.indexes, 'NASDAQCOMP')
                            
                            # Assert that the session was committed
                            mock_session.commit.assert_called_once()
    
    @patch('jobs.stock_data_job.yf.Tickers')
    def test_process_batch(self, mock_tickers_class):
        """
        Test that the _process_batch method processes a batch of tickers.
        """
        # Create mock tickers
        mock_tickers = MagicMock()
        mock_tickers_class.return_value = mock_tickers
        
        # Create mock ticker objects
        mock_ticker1 = MagicMock()
        mock_ticker2 = MagicMock()
        
        # Set up the tickers dictionary
        mock_tickers.tickers = {
            'AAPL': mock_ticker1,
            'MSFT': mock_ticker2
        }
        
        # Mock the _process_ticker method
        with patch.object(self.job, '_process_ticker') as mock_process_ticker:
            # Process the batch
            self.job._process_batch(['AAPL', 'MSFT'])
            
            # Assert that _process_ticker was called for each ticker
            mock_process_ticker.assert_any_call('AAPL', mock_ticker1)
            mock_process_ticker.assert_any_call('MSFT', mock_ticker2)
            
            # Assert that _process_ticker was called twice
            self.assertEqual(mock_process_ticker.call_count, 2)
            
            # Assert that the processed count was incremented
            self.assertEqual(self.job.processed_count, 2)
    
    @patch('jobs.stock_data_job.get_db_session')
    def test_store_stock_info_creates_new_stock(self, mock_get_db_session):
        """
        Test that the _store_stock_info method creates a new stock when it doesn't exist.
        """
        # Mock the get_db_session function
        mock_get_db_session.return_value = self.mock_session_context
        
        # Mock the query to return None (stock doesn't exist)
        self.mock_query.first.return_value = None
        
        # Create a mock info dictionary
        info = {
            'shortName': 'Apple Inc.',
            'sector': 'Technology',
            'industry': 'Consumer Electronics',
            'exchange': 'NASDAQ',
            'country': 'United States',
            'marketCap': 2000000000000
        }
        
        # Store the stock info
        self.job._store_stock_info('AAPL', info)
        
        # Assert that a new stock was added to the session
        self.mock_session.add.assert_called_once()
        
        # Get the stock that was added
        stock = self.mock_session.add.call_args[0][0]
        
        # Assert that the stock has the correct attributes
        self.assertEqual(stock.ticker, 'AAPL')
        self.assertEqual(stock.name, 'Apple Inc.')
        self.assertEqual(stock.sector, 'Technology')
        self.assertEqual(stock.industry, 'Consumer Electronics')
        self.assertEqual(stock.exchange, 'NASDAQ')
        self.assertEqual(stock.country, 'United States')
        self.assertEqual(stock.market_cap, 2000000000000)
    
    @patch('jobs.stock_data_job.get_db_session')
    def test_store_stock_info_updates_existing_stock(self, mock_get_db_session):
        """
        Test that the _store_stock_info method updates an existing stock.
        """
        # Mock the get_db_session function
        mock_get_db_session.return_value = self.mock_session_context
        
        # Create a mock existing stock
        existing_stock = Stock(
            ticker='AAPL',
            name='Apple Inc.',
            sector='Technology',
            industry='Consumer Electronics',
            exchange='NASDAQ',
            country='United States',
            market_cap=1900000000000
        )
        
        # Mock the query to return the existing stock
        self.mock_query.first.return_value = existing_stock
        
        # Create a mock info dictionary with updated market cap
        info = {
            'shortName': 'Apple Inc.',
            'sector': 'Technology',
            'industry': 'Consumer Electronics',
            'exchange': 'NASDAQ',
            'country': 'United States',
            'marketCap': 2000000000000
        }
        
        # Store the stock info
        self.job._store_stock_info('AAPL', info)
        
        # Assert that no new stock was added to the session
        self.mock_session.add.assert_not_called()
        
        # Assert that the existing stock was updated
        self.assertEqual(existing_stock.market_cap, 2000000000000)
        
        # Assert that the session was committed
        self.mock_session.commit.assert_called_once()


if __name__ == '__main__':
    unittest.main()
