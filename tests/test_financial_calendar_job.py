"""
Tests for financial calendar job functionality.
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from ..jobs.db.schema import (
    FinancialCalendarEvent,
    DataSource,
    EventMonitoringStatus,
    JobRun
)
from ..jobs.financial_calendar_job import FinancialCalendarJob

@pytest.fixture
def db_session():
    """Provide a database session for testing."""
    from ..jobs.db.mysql_client import get_db_session
    session = get_db_session()
    yield session
    session.rollback()
    session.close()

@pytest.fixture
def test_data_source(db_session: Session):
    """Create a test data source."""
    source = DataSource(
        name="Test Source",
        api_endpoint="http://test.source/api",
        priority=1,
        is_active=True
    )
    db_session.add(source)
    db_session.commit()
    return source

@pytest.fixture
def test_event(db_session: Session):
    """Create a test financial calendar event."""
    event = FinancialCalendarEvent(
        event_name="Test Event",
        event_type="CPI",
        country="US",
        date=datetime.now().date(),
        is_released=False
    )
    db_session.add(event)
    db_session.commit()
    return event

def test_fetch_upcoming_events(db_session: Session, test_data_source):
    """Test fetching upcoming events."""
    job = FinancialCalendarJob()
    
    # Mock the source-specific fetching
    def mock_fetch(source, end_date):
        return [
            FinancialCalendarEvent(
                event_name="Test Event",
                event_type="CPI",
                country="US",
                date=datetime.now().date() + timedelta(days=1)
            )
        ]
    
    job._fetch_from_source = mock_fetch
    
    events = job.fetch_upcoming_events()
    assert len(events) == 1
    assert events[0].event_type == "CPI"

def test_event_deduplication(db_session: Session, test_data_source):
    """Test event deduplication logic."""
    job = FinancialCalendarJob()
    
    # Create duplicate events
    event1 = FinancialCalendarEvent(
        event_name="Test Event",
        event_type="CPI",
        country="US",
        date=datetime.now().date()
    )
    event2 = FinancialCalendarEvent(
        event_name="Test Event",
        event_type="CPI",
        country="US",
        date=datetime.now().date()
    )
    
    events = job._deduplicate_events([event1, event2])
    assert len(events) == 1

def test_source_error_handling(db_session: Session, test_data_source):
    """Test data source error handling."""
    job = FinancialCalendarJob()
    
    # Simulate errors
    for _ in range(3):
        job._handle_source_error(test_data_source)
    
    # Source should be disabled after 3 errors
    assert test_data_source.is_active == False
    assert test_data_source.error_count == 3

def test_event_monitoring(db_session: Session, test_event):
    """Test event monitoring functionality."""
    job = FinancialCalendarJob()
    
    # Mock the release checking
    def mock_check(source, event):
        return True
    
    job._check_event_release = mock_check
    
    job.monitor_event_releases()
    
    # Verify event was marked as released
    updated_event = db_session.query(FinancialCalendarEvent)\
        .filter(FinancialCalendarEvent.id == test_event.id)\
        .first()
    assert updated_event.is_released == True
    
    # Verify monitoring status was updated
    status = db_session.query(EventMonitoringStatus)\
        .filter(EventMonitoringStatus.event_id == test_event.id)\
        .first()
    assert status.status == "completed"

def test_job_run_tracking(db_session: Session):
    """Test job run tracking functionality."""
    job = FinancialCalendarJob()
    
    # Run monitoring
    job.monitor_event_releases()
    
    # Verify job run was recorded
    job_run = db_session.query(JobRun)\
        .filter(JobRun.job_name == "financial_calendar_monitor")\
        .first()
    assert job_run is not None
    assert job_run.status == "completed"

def test_job_error_handling(db_session: Session):
    """Test job error handling."""
    job = FinancialCalendarJob()
    
    # Force an error
    def mock_monitor():
        raise Exception("Test error")
    
    job.monitor_event_releases = mock_monitor
    
    with pytest.raises(Exception):
        job.run()
    
    # Verify failed job run was recorded
    job_run = db_session.query(JobRun)\
        .filter(JobRun.job_name == "financial_calendar_monitor")\
        .first()
    assert job_run.status == "failed"
    assert "Test error" in job_run.error_message
