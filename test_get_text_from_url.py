#!/usr/bin/env python3
"""
Test script for the get_text_from_url method in RealTimeFilingJob.
"""

from jobs.real_time_filing_job import RealTimeFiling<PERSON>ob
import unittest
from unittest.mock import patch, MagicMock

class TestGetTextFromUrl(unittest.TestCase):
    """Test cases for get_text_from_url method."""

    def setUp(self):
        """Set up test fixtures."""
        self.job = RealTimeFilingJob()

    @patch('requests.get')
    def test_get_text_from_url_html_removal(self, mock_get):
        """Test that HTML tags are properly removed from the response."""
        # Mock response with HTML content
        mock_response = MagicMock()
        mock_response.text = """
        <html>
            <head><title>Test Document</title></head>
            <body>
                <h1>Header</h1>
                <p>This is a <b>test</b> paragraph with <i>formatting</i>.</p>
                <table>
                    <tr><th>Column 1</th><th>Column 2</th></tr>
                    <tr><td>Data 1</td><td>Data 2</td></tr>
                </table>
                <div class="footer">Footer text</div>
            </body>
        </html>
        """
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        # Call the method with custom parameters
        result = self.job.get_text_from_url(
            "http://example.com/test.html",
            ignore_links=True,
            ignore_images=True,
            ignore_tables=False,
            body_width=0,
            timeout=10
        )

        # Verify HTML tags are removed
        self.assertNotIn("<html>", result)
        self.assertNotIn("<head>", result)
        self.assertNotIn("<body>", result)
        self.assertNotIn("<h1>", result)
        self.assertNotIn("<p>", result)
        self.assertNotIn("<b>", result)
        self.assertNotIn("<i>", result)
        self.assertNotIn("<table>", result)
        self.assertNotIn("<tr>", result)
        self.assertNotIn("<th>", result)
        self.assertNotIn("<td>", result)
        self.assertNotIn("<div>", result)

        # Verify content is preserved (note that html2text converts to markdown)
        self.assertIn("Header", result)
        # html2text converts <b> to **bold** and <i> to _italic_
        self.assertIn("This is a", result)
        self.assertIn("test", result)
        self.assertIn("paragraph with", result)
        self.assertIn("formatting", result)
        self.assertIn("Column 1", result)
        self.assertIn("Column 2", result)
        self.assertIn("Data 1", result)
        self.assertIn("Data 2", result)
        self.assertIn("Footer text", result)

    @patch('requests.get')
    def test_get_text_from_url_xml_removal(self, mock_get):
        """Test that XML tags are properly removed from the response."""
        # Mock response with XML content
        mock_response = MagicMock()
        mock_response.text = """
        <?xml version="1.0" encoding="UTF-8"?>
        <root>
            <header>XML Test Document</header>
            <content>
                <paragraph>This is a <bold>test</bold> paragraph with <italic>XML formatting</italic>.</paragraph>
                <data>
                    <item>Item 1</item>
                    <item>Item 2</item>
                </data>
            </content>
            <footer>XML Footer</footer>
        </root>
        """
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        # Call the method
        result = self.job.get_text_from_url("http://example.com/test.xml")

        # Verify XML tags are removed
        self.assertNotIn("<?xml", result)
        self.assertNotIn("<root>", result)
        self.assertNotIn("<header>", result)
        self.assertNotIn("<content>", result)
        self.assertNotIn("<paragraph>", result)
        self.assertNotIn("<bold>", result)
        self.assertNotIn("<italic>", result)
        self.assertNotIn("<data>", result)
        self.assertNotIn("<item>", result)
        self.assertNotIn("<footer>", result)

        # Verify content is preserved (note that html2text converts to markdown)
        self.assertIn("XML Test Document", result)
        # Check for parts of the content since html2text may format it differently
        self.assertIn("This is a", result)
        self.assertIn("test", result)
        self.assertIn("paragraph with", result)
        self.assertIn("XML formatting", result)
        self.assertIn("Item 1", result)
        self.assertIn("Item 2", result)
        self.assertIn("XML Footer", result)

    @patch('requests.get')
    def test_get_text_from_url_with_ignore_tables(self, mock_get):
        """Test that tables can be ignored if requested."""
        # Mock response with HTML content containing tables
        mock_response = MagicMock()
        mock_response.text = """
        <html>
            <body>
                <h1>Table Test</h1>
                <table>
                    <tr><th>Column 1</th><th>Column 2</th></tr>
                    <tr><td>Data 1</td><td>Data 2</td></tr>
                </table>
            </body>
        </html>
        """
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        # Call the method with ignore_tables=True
        result = self.job.get_text_from_url(
            "http://example.com/test.html",
            ignore_tables=True
        )

        # Verify table structure is not present
        self.assertNotIn("Column 1|", result)
        self.assertNotIn("---|---", result)

        # Verify content is still there
        self.assertIn("Table Test", result)

    @patch('requests.get')
    def test_get_text_from_url_error_handling(self, mock_get):
        """Test error handling when the request fails."""
        # Mock a failed request
        mock_get.side_effect = Exception("Connection error")

        # Call the method
        result = self.job.get_text_from_url("http://example.com/test.html")

        # Verify an empty string is returned on error
        self.assertEqual("", result)

    @patch('requests.get')
    def test_get_text_from_url_empty_url(self, mock_get):
        """Test handling of empty URL."""
        # Call the method with an empty URL
        result = self.job.get_text_from_url("")

        # Verify an empty string is returned
        self.assertEqual("", result)

        # Verify the request was not made
        mock_get.assert_not_called()

if __name__ == "__main__":
    unittest.main()
