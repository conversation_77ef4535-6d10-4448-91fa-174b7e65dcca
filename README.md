# go-gpt-job

A Python-based job service for fetching and storing S&P 500 stock data.

## Overview

This project provides a standalone service for fetching stock data from Yahoo Finance and storing it in a MySQL database. It's designed to be run as a scheduled job (e.g., via cron) to keep the stock data up to date.

## Features

- Fetch stock data for S&P 500 companies using the yfinance library
- Store stock information, prices, and fundamentals in a MySQL database
- Support for incremental updates to minimize API calls
- Advanced rate limiting handling with retry logic and adaptive batch sizing
- Batch processing to optimize API usage
- Configurable via environment variables
- Comprehensive logging and error handling

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-organization/go-gpt-job.git
   cd go-gpt-job
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Create a `.env` file based on the provided `.env.example`:
   ```bash
   cp .env.example .env
   ```

5. Edit the `.env` file to configure your MySQL connection and job settings.

## Usage

### Running the Job Manually

To run the job manually:

```bash
python run_stock_data_job.py
```

You can customize the job behavior with command-line arguments:

```bash
python run_stock_data_job.py --full-refresh --batch-size 5 --delay 2.0 --price-days 60
```

### Setting Up as a Scheduled Job

To run the job automatically on a schedule, you can use the provided cron script:

```bash
# Make the script executable
chmod +x cron/stock_data_job.sh

# Add to crontab to run daily at 6:00 PM
crontab -e
# Add the following line:
0 18 * * * /path/to/go-gpt-job/cron/stock_data_job.sh
```

See the [cron documentation](cron/README.md) for more details.

## Database Schema

The job creates and maintains the following tables:

- `stocks`: Basic information about each stock (ticker, name, sector, etc.)
- `stock_prices`: Historical price data for each stock
- `stock_fundamentals`: Financial metrics for each stock
- `job_runs`: Records of job execution for monitoring

## Configuration

The job can be configured using environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| MYSQL_HOST | MySQL server hostname | localhost |
| MYSQL_PORT | MySQL server port | 3306 |
| MYSQL_USER | MySQL username | root |
| MYSQL_PASSWORD | MySQL password | password |
| MYSQL_DATABASE | MySQL database name | stock_data |
| JOB_BATCH_SIZE | Number of stocks to process in each batch | 10 |
| JOB_DELAY | Delay between batches in seconds | 1.0 |
| JOB_PRICE_DAYS | Number of days of price history to fetch | 30 |
| JOB_FULL_REFRESH | Whether to refresh all data (true) or only update changed data (false) | false |
| JOB_SPECIFIC_TICKERS | Comma-separated list of specific tickers to process | (all S&P 500) |
| YF_MAX_RETRIES | Maximum number of retry attempts for rate limiting | 5 |
| YF_MIN_WAIT | Minimum wait time between retries in seconds | 2.0 |
| YF_MAX_WAIT | Maximum wait time between retries in seconds | 60.0 |

## Development

### Running Tests

```bash
pytest
```

## Rate Limiting Enhancement

The job now includes advanced rate limiting handling for the YFinance API:

- **Retry with Exponential Backoff**: Automatically retries failed requests with exponential backoff and jitter
- **Adaptive Batch Sizing**: Dynamically adjusts batch sizes based on rate limiting encounters
- **Circuit Breaker Pattern**: Prevents cascading failures during extended rate limiting periods

For more details, see the [YFinance Rate Limiting Documentation](docs/yfinance_rate_limiting.md).

### Testing the Rate Limiting

You can test the rate limiting enhancement with the provided test script:

```bash
python tests/test_yfinance_wrapper.py
```

This script demonstrates the YFinance wrapper in action by fetching data for a list of tickers and handling rate limiting gracefully.

### Project Structure

- `jobs/`: Core job implementation
  - `db/`: Database models and utilities
    - `schema.py`: SQLAlchemy models
    - `mysql_client.py`: MySQL connection utilities
  - `stock_data_job.py`: Main job implementation
- `config/`: Configuration utilities
- `utils/`: Utility functions
  - `yfinance_wrapper.py`: YFinance API wrapper with rate limiting
- `cron/`: Cron job scripts
- `docs/`: Documentation
  - `yfinance_rate_limiting.md`: Documentation for the rate limiting enhancement
- `tests/`: Unit tests
  - `test_yfinance_wrapper.py`: Test script for the YFinance wrapper

## License

[MIT License](LICENSE)
