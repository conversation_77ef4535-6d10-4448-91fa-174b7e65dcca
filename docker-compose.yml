version: '3'

services:
  mysql:
    image: mysql:8.0
    container_name: go-gpt-job-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: stock_data
      MYSQL_USER: stockuser
      MYSQL_PASSWORD: stockpassword
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 5s
      timeout: 5s
      retries: 10

  stock-data-job:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: go-gpt-job-app
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_USER: stockuser
      MYSQL_PASSWORD: stockpassword
      MYSQL_DATABASE: stock_data
    volumes:
      - ./logs:/app/logs

volumes:
  mysql-data:
