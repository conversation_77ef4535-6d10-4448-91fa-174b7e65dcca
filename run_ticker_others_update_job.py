#!/usr/bin/env python3
"""
Runner script for ticker others update job.
This script runs the ticker update job for various countries (Singapore, Japan, France, Germany).
"""

import os
import sys
import argparse

# Add the current directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jobs.ticker_others_update_job import TickerOthersUpdateJob
from utils.logging_utils import setup_logging

def main():
    """
    Main function to run the ticker others update job.
    """
    parser = argparse.ArgumentParser(description='Run ticker update job for various countries')
    parser.add_argument('country', choices=['Singapore', 'Japan', 'France', 'Germany', 'Korea', 'Malaysia', 'United Kingdom', "Thailand", "Canada", "Taiwan"], 
                       help='Country to update tickers for')
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging(enable_colors=True)
    
    try:
        logger.info(f"Starting ticker update job for {args.country}")
        
        # Create and run the job
        job = TickerOthersUpdateJob(args.country)
        success = job.run()
        
        if success:
            logger.info(f"Ticker update job for {args.country} completed successfully")
            sys.exit(0)
        else:
            logger.error(f"Ticker update job for {args.country} failed")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error running ticker update job for {args.country}: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
