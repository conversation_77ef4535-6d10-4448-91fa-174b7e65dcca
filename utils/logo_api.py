"""
Utility functions for fetching company logos from third-party APIs.
"""

import os
import requests
import time
from bs4 import BeautifulSoup
from typing import Optional, Dict, Any, Tuple

from utils.logging_utils import setup_logging

# Configure logging
logger = setup_logging(enable_colors=False)

# Logo.dev direct URL format
LOGO_DEV_URL = "https://img.logo.dev/ticker/{ticker}?format=png&size=128&token={token}"
LOGO_DEV_TOKEN = os.getenv("LOGO_DEV_TOKEN", "pk_Kutb31wXRBmwFgtsPDJusw")  # Default token or from environment variable

# eodhd
EODHD_LOGO_URL = "https://eodhd.com/img/logos/{exchange}/{symbol}.png"

# Clearbit API (free tier with limitations)
CLEARBIT_LOGO_API_URL = "https://logo.clearbit.com/{domain}"

def get_logo_url_from_eodhd(ticker: str) -> Optional[str]:
    """
    Get company logo URL from eodhd.

    Args:
        ticker: Company ticker symbol

    Returns:
        Logo URL or None if not found
    """

    try:
        # split ticker and exchange
        if '.' in ticker:
            symbol, exchange = ticker.split('.')
        else:
            symbol = ticker
            exchange = 'US'
        # convert exchange to eodhd format
        if exchange == 'SI':
            exchange = 'SG'
        elif exchange == 'T':
            exchange = 'TSE'
        elif exchange == 'KS':
            exchange = 'KO'

        ticker = f"{symbol}.{exchange}"
        # use logo URL
        logo_url = EODHD_LOGO_URL.format(exchange=exchange, symbol=symbol)
        # check if the logo exists by making a HEAD request
        response = requests.head(logo_url, timeout=10)
        if response.status_code == 200:
            logger.info(f"Found logo for ticker {ticker} on eodhd")
            return logo_url
        elif response.status_code == 404:
            logger.warning(f"No logo found for ticker {ticker} on eodhd")
            return None
        else:
            logger.warning(f"Unexpected response from eodhd for ticker {ticker}: {response.status_code}")
            return None

    except Exception as e:
        logger.warning(f"Error fetching logo from eodhd for ticker {ticker}: {str(e)}")
        return None

def get_logo_url_from_clearbit(domain: str) -> Optional[str]:
    """
    Get company logo URL from Clearbit API.

    Args:
        domain: Company domain name (e.g., apple.com)

    Returns:
        Logo URL or None if not found
    """
    if not domain:
        return None

    try:
        # Clearbit logo API doesn't require authentication but has rate limits
        logo_url = CLEARBIT_LOGO_API_URL.format(domain=domain)

        # Check if the logo exists by making a HEAD request
        response = requests.head(logo_url, timeout=10)

        if response.status_code == 200:
            return logo_url
        else:
            logger.warning(f"No logo found for domain {domain} on Clearbit")
            return None

    except Exception as e:
        logger.error(f"Error fetching logo from Clearbit for domain {domain}: {str(e)}")
        return None

    """
    Get company logo URL from IEX Cloud API.

    Args:
        ticker: Company ticker symbol

    Returns:
        Logo URL or None if not found
    """
    if not IEX_API_KEY:
        logger.warning("IEX_API_KEY not set, skipping IEX logo fetch")
        return None

    try:
        url = IEX_API_URL.format(ticker=ticker)
        params = {"token": IEX_API_KEY}

        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data and "url" in data:
            return data["url"]

        logger.warning(f"No logo found for ticker {ticker} on IEX")
        return None

    except Exception as e:
        logger.error(f"Error fetching logo from IEX for ticker {ticker}: {str(e)}")
        return None

def get_logo_url_from_logodev(ticker: str, company_name: str = None) -> Optional[str]:
    """
    Get company logo URL from Logo.dev using direct URL format.

    Args:
        ticker: Company ticker symbol
        company_name: Company name (not used in this implementation)

    Returns:
        The URL of the logo image (or None if not found)
    """
    try:
        # Construct the direct URL
        logo_url = LOGO_DEV_URL.format(ticker=ticker, token=LOGO_DEV_TOKEN)

        # Check if the logo exists by making a HEAD request
        response = requests.get(logo_url, timeout=10)

        if response.status_code == 200:
            logger.info(f"Found logo for ticker {ticker} on Logo.dev")
            return logo_url
        elif response.status_code == 404:
            logger.warning(f"No logo found for ticker {ticker} on Logo.dev (status code: {response.status_code})")
            return None
        else:
            logger.warning(f"Unexpected response from Logo.dev for ticker {ticker}: {response.status_code}")
            return None

    except Exception as e:
        logger.warning(f"Error checking logo from Logo.dev for ticker {ticker}: {str(e)}")
        return None

def get_logo_url_from_tradingview(ticker: str, company_name: str = None) -> Optional[str]:
    """
    Get ticker logo URL from TradingView.

    Args:
        ticker: Company ticker symbol
        company_name: Company name (not used in this implementation)

    Returns:
        Logo URL or None if not found
    """
    url = f"https://www.tradingview.com/symbols/HKEX-{ticker}/"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        # use BeautifulSoup to parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # find the logo image element
        logo_img = soup.find('img', class_='logo-PsAlMQQF')
        if logo_img and 'src' in logo_img.attrs:
            return logo_img['src']
        else:
            logger.warning("Logo image not found on TradingView")
            return None
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching page: {e}")

def get_company_domain(ticker: str, company_name: str) -> Optional[str]:
    """
    Get company domain name based on ticker and company name.
    This is a simple implementation and might not be accurate for all companies.

    Args:
        ticker: Company ticker symbol
        company_name: Company name

    Returns:
        Domain name or None if not found
    """
    # Remove common suffixes from company name
    suffixes = [" Inc.", " Corporation", " Corp.", " Company", " Co.", " Ltd.", " LLC", " Group", " Holdings", " Holding", " PLC", " N.V.", " S.A."]
    name = company_name
    for suffix in suffixes:
        name = name.replace(suffix, "")

    # Remove any remaining special characters and convert to lowercase
    name = "".join(c for c in name if c.isalnum() or c.isspace()).strip().lower()

    # Replace spaces with empty string
    name = name.replace(" ", "")

    # Return domain with .com suffix
    return f"{name}.com"

def get_logo_url(ticker: str, company_name: str) -> Optional[str]:
    """
    Get company logo URL from various sources.

    Args:
        ticker: Company ticker symbol
        company_name: Company name

    Returns:
        The URL of the logo image (or None if not found)
    """
    # Try Logo.dev API first (if API key is available)
    logo_url = get_logo_url_from_logodev(ticker, company_name)
    if logo_url:
        return logo_url

    # Try EODHD API next
    logo_url = get_logo_url_from_eodhd(ticker)
    if logo_url:
        return logo_url

    logger.warning(f"No logo found for ticker {ticker} ({company_name})")
    return None


    """
    Get company logo URL and download the logo content.

    Args:
        ticker: Company ticker symbol
        company_name: Company name

    Returns:
        Tuple containing:
        - The URL of the logo image (or None if not found)
        - The binary data of the logo image (or None if download failed)
    """
    # Get the logo URL
    logo_url = get_logo_url(ticker, company_name)

    if not logo_url:
        return None, None

    # Download the logo content
    try:
        response = requests.get(logo_url, timeout=10)
        if response.status_code == 200:
            logger.info(f"Downloaded logo for ticker {ticker} from {logo_url}")
            return logo_url, response.content
        else:
            logger.warning(f"Failed to download logo for ticker {ticker} from {logo_url} (status code: {response.status_code})")
            return logo_url, None
    except Exception as e:
        logger.error(f"Error downloading logo for ticker {ticker} from {logo_url}: {str(e)}")
        return logo_url, None
