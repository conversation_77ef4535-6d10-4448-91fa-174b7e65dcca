"""
交易所检测工具 - 用于准确确定美股ticker的交易所
"""

import requests
import yfinance as yf
from typing import Optional, Dict, Set
from utils.logging_utils import setup_logging

logger = setup_logging(enable_colors=False)

class ExchangeDetector:
    """美股交易所检测器"""

    def __init__(self):
        # 已知的交易所映射
        self.known_exchanges = self._load_known_exchanges()

    def _load_known_exchanges(self) -> Dict[str, Set[str]]:
        """加载已知的交易所ticker映射"""
        return {
            'NYSE': {
                # 单字母ticker通常在NYSE
                'A', 'B', 'C', 'D', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
                # 知名蓝筹股
                'IBM', 'GE', 'JPM', 'BAC', 'WMT', 'JNJ', 'PG', 'KO', 'PFE', 'XOM', 'CVX', 'HD', 'VZ', 'T', 'MRK', 'DIS',
                'MMM', 'AXP', 'CAT', 'DD', 'GS', 'HON', 'MCD', 'NKE', 'TRV', 'UNH', 'UTX', 'V', 'WBA',
                # 银行股
                'C', 'WFC', 'USB', 'PNC', 'COF', 'AIG', 'MET', 'PRU', 'ALL',
                # 能源股
                'COP', 'SLB', 'EOG', 'KMI', 'OXY', 'PSX', 'VLO', 'MPC', 'HES', 'APA',
                # 工业股
                'BA', 'UNP', 'LMT', 'RTX', 'DE', 'FDX', 'UPS', 'NSC', 'CSX',
                # ADR stocks that are actually on NYSE
                'BABA', 'TSM', 'NIO', 'XPEV', 'LI', 'BIDU', 'JD', 'PDD', 'NTES', 'WB'
            },
            'NASDAQ': {
                # 科技巨头
                'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'META', 'TSLA', 'NVDA', 'NFLX', 'ADBE', 'CRM', 'ORCL', 'INTC', 'AMD', 'QCOM',
                # 其他科技股
                'AVGO', 'TXN', 'ASML', 'COST', 'CMCSA', 'PEP', 'TMUS', 'CHTR', 'PYPL', 'SBUX',
                'ISRG', 'GILD', 'BKNG', 'REGN', 'MRNA', 'MDLZ', 'ADP', 'FISV', 'ATVI',
                # 生物科技
                'BIIB', 'CELG', 'VRTX', 'ILMN', 'BMRN', 'ALXN', 'INCY', 'TECH', 'SGEN', 'EXAS',
                # 4字母科技股模式
                'MSCI', 'CTSH', 'PAYX', 'CDNS', 'SNPS', 'ANSS', 'KLAC', 'LRCX', 'AMAT', 'MCHP'
            },
            'AMEX': {
                # ETFs (很多在NYSE Arca，但归类为AMEX)
                'SPY', 'QQQ', 'IWM', 'GLD', 'SLV', 'VXX', 'TLT', 'EFA', 'EEM', 'XLF', 'XLE', 'XLI', 'XLK', 'XLV',
                'IVV', 'VOO', 'VTI', 'VEA', 'VWO', 'AGG', 'BND', 'VNQ', 'VYM', 'VXUS',
                # 小盘股和特殊情况
                'IAU', 'GDXJ', 'GDX', 'USO', 'UNG', 'UVXY', 'SQQQ', 'TQQQ', 'SPXL', 'SPXS'
            },
            'CBOE': {
                # CBOE主要是期权和波动率产品，股票较少
                'VIX', 'CBOE'  # 这些实际上不是股票ticker，但作为示例
            }
        }

    def detect_exchange(self, ticker_symbol: str, classify: str = 'EQ') -> str:
        """
        检测ticker的交易所

        Args:
            ticker_symbol: 股票代码
            classify: 分类 (EQ/ADR/GDR等)

        Returns:
            交易所名称 (NYSE/NASDAQ/AMEX/CBOE)
        """
        # 清理ticker符号
        clean_ticker = ticker_symbol.replace('.US', '').upper()

        # 方法1: 检查已知映射
        exchange = self._check_known_mappings(clean_ticker)
        if exchange:
            logger.debug(f"Found {clean_ticker} in known mappings: {exchange}")
            return exchange

        # 方法2: 使用Yahoo Finance API
        exchange = self._get_exchange_from_yahoo(clean_ticker)
        if exchange:
            logger.debug(f"Got {clean_ticker} exchange from Yahoo Finance: {exchange}")
            return exchange

        # 方法3: 使用模式匹配
        exchange = self._detect_by_patterns(clean_ticker, classify)
        if exchange:
            logger.debug(f"Detected {clean_ticker} exchange by patterns: {exchange}")
            return exchange

        # 方法4: 启发式规则
        exchange = self._detect_by_heuristics(clean_ticker, classify)
        logger.debug(f"Detected {clean_ticker} exchange by heuristics: {exchange}")
        return exchange

    def _check_known_mappings(self, ticker: str) -> Optional[str]:
        """检查已知的ticker映射"""
        for exchange, tickers in self.known_exchanges.items():
            if ticker in tickers:
                return exchange
        return None

    def _get_exchange_from_yahoo(self, ticker: str) -> Optional[str]:
        """从Yahoo Finance获取交易所信息"""
        try:
            yf_ticker = yf.Ticker(ticker)
            info = yf_ticker.info

            if 'exchange' in info and info['exchange']:
                exchange_code = info['exchange'].upper()

                # Yahoo Finance交易所代码映射
                exchange_mapping = {
                    'NYQ': 'NYSE',      # NYSE
                    'NMS': 'NASDAQ',    # NASDAQ Global Select Market
                    'NGM': 'NASDAQ',    # NASDAQ Global Market
                    'NCM': 'NASDAQ',    # NASDAQ Capital Market
                    'ASE': 'AMEX',      # NYSE American (formerly AMEX)
                    'NYE': 'NYSE',      # NYSE
                    'PCX': 'AMEX',      # NYSE Arca (归类为AMEX)
                    'BTS': 'NASDAQ',    # BATS (现在是CBOE BZX)
                    'CBE': 'CBOE',      # CBOE
                    'OTC': 'OTC',       # Over-the-counter
                    'PNK': 'OTC'        # Pink Sheets
                }

                return exchange_mapping.get(exchange_code, exchange_code)

        except Exception as e:
            logger.debug(f"Yahoo Finance lookup failed for {ticker}: {str(e)}")

        return None

    def _detect_by_patterns(self, ticker: str, classify: str) -> Optional[str]:
        """基于模式检测交易所"""

        # ETF模式检测
        if self._is_likely_etf(ticker):
            return 'AMEX'  # 大多数ETF在NYSE Arca，归类为AMEX

        # ADR模式
        if classify in ['ADR', 'GDR']:
            # 大型ADR通常在NYSE，小型的在NASDAQ
            if len(ticker) <= 3:
                return 'NYSE'
            else:
                return 'NASDAQ'

        return None

    def _is_likely_etf(self, ticker: str) -> bool:
        """判断是否可能是ETF"""
        etf_patterns = [
            ticker.endswith('ETF'),
            # 3字母且全大写的可能是ETF
            len(ticker) == 3 and ticker.isupper() and ticker.isalpha(),
            # 常见ETF前缀
            ticker.startswith(('SPY', 'QQQ', 'IWM', 'VT', 'EF', 'EM', 'XL')),
            # 包含数字的通常是ETF
            any(c.isdigit() for c in ticker) and len(ticker) <= 4
        ]
        return any(etf_patterns)

    def _detect_by_heuristics(self, ticker: str, classify: str) -> str:
        """基于启发式规则检测交易所"""

        # 单字母ticker几乎都在NYSE
        if len(ticker) == 1:
            return 'NYSE'

        # 2-3字母ticker通常是NYSE蓝筹股
        elif len(ticker) <= 3:
            return 'NYSE'

        # 4字母ticker通常在NASDAQ
        elif len(ticker) == 4:
            # 但如果是传统行业可能在NYSE
            traditional_sectors = ['BANK', 'REIT', 'UTIL']  # 这里可以扩展
            if any(sector in ticker for sector in traditional_sectors):
                return 'NYSE'
            return 'NASDAQ'

        # 5+字母ticker通常在NASDAQ
        else:
            return 'NASDAQ'

    def batch_detect(self, tickers: list) -> Dict[str, str]:
        """批量检测交易所"""
        results = {}
        for ticker in tickers:
            results[ticker] = self.detect_exchange(ticker)
        return results

# 创建全局实例
exchange_detector = ExchangeDetector()

def detect_exchange(ticker_symbol: str, classify: str = 'EQ') -> str:
    """
    便捷函数：检测ticker的交易所

    Args:
        ticker_symbol: 股票代码
        classify: 分类

    Returns:
        交易所名称
    """
    return exchange_detector.detect_exchange(ticker_symbol, classify)
