import yfinance as yf
import datetime

from utils.logging_utils import setup_logging

logger = setup_logging(enable_colors=False)

def get_filing_fiscal_year_and_quarter(ticker, accession_number, filing_type, report_date_str):
    tk = yf.Ticker(ticker)
    info = tk.info
    last_fiscal_year_end = info.get('lastFiscalYearEnd')
    most_recent_quarter = info.get('mostRecentQuarter')
    report_date = datetime.datetime.strptime(report_date_str, "%Y-%m-%d")
    last_fiscal_year_end_date = None
    most_recent_quarter_date = None
    if last_fiscal_year_end is None or most_recent_quarter is None:
        real_report_date = get_last_day_of_prev_quarter(report_date)
        fiscal_year_start_month = 1
        most_recent_quarter_date_str = real_report_date.strftime('%Y-%m-%d')
    else:
        last_fiscal_year_end_date = datetime.datetime.fromtimestamp(last_fiscal_year_end, datetime.UTC)
        most_recent_quarter_date = datetime.datetime.fromtimestamp(most_recent_quarter, datetime.UTC)
        fiscal_year_start_month = last_fiscal_year_end_date.month % 12 + 1
        
        most_recent_quarter_date_str = most_recent_quarter_date.strftime('%Y-%m-%d')
        real_report_date = report_date
        if report_date_str != most_recent_quarter_date_str:
            real_report_date = most_recent_quarter_date

    month = real_report_date.month
    adjusted_month = (month - fiscal_year_start_month + 12) % 12 + 1

    fiscal_year = real_report_date.year
    fiscal_quarter = None
    if filing_type == '10-Q' or filing_type == '6-K' or filing_type == '8-K':
        fiscal_quarter = (adjusted_month - 1) // 3 + 1
    
    if report_date_str != most_recent_quarter_date_str:
        logger.info(f"ticker {ticker}, last_fiscal_year_end: {last_fiscal_year_end_date}, fiscal_year_start_month: {fiscal_year_start_month}, accession_number: {accession_number}, filing_type: {filing_type}, use most_recent_quarter_date as report_date: {most_recent_quarter_date_str}, fiscal_year: {fiscal_year}, fiscal_quarter: {fiscal_quarter}")
    else:
        logger.info(f"ticker {ticker}, last_fiscal_year_end: {last_fiscal_year_end_date}, fiscal_year_start_month: {fiscal_year_start_month}, accession_number: {accession_number}, filing_type: {filing_type}, report_date: {report_date}, fiscal_year: {fiscal_year}, fiscal_quarter: {fiscal_quarter}")
    return fiscal_year, fiscal_quarter, real_report_date.strftime('%Y-%m-%d')


def get_last_day_of_prev_quarter(date=None):
    if date is None:
        date = datetime.today()
    
    month = date.month
    current_quarter = (month - 1) // 3 + 1
    prev_quarter = current_quarter - 1
    if prev_quarter == 0:
        prev_quarter = 4
        year = date.year - 1
    else:
        year = date.year
    last_month_of_prev_quarter = prev_quarter * 3
    import calendar
    last_day = calendar.monthrange(year, last_month_of_prev_quarter)[1]
    
    return datetime.datetime(year, last_month_of_prev_quarter, last_day)