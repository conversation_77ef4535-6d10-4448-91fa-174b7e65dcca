import requests
from sec_cik_mapper import StockMapper

def get_ticker_to_cik_map():
    url = "https://www.sec.gov/include/ticker.txt"
    headers = {
        "User-Agent": "Mozilla/5.0 (compatible; YourName/1.0; <EMAIL>)"
    }

    response = requests.get(url, headers=headers)
    response.raise_for_status()

    lines = response.text.strip().split('\n')

    ticker_to_cik = {}

    for line in lines:
        if '\t' in line:
            ticker, cik = line.strip().split('\t')
            ticker = ticker.replace('-', '').upper()
            ticker_to_cik[ticker] = cik.zfill(10)
    return ticker_to_cik

def get_ticker_to_cik_map_from_stock_mapper():
    mapper = StockMapper()
    return mapper.ticker_to_cik