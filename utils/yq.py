import time

import yahooquery as yq
import yfinance as yf
from utils.logging_utils import setup_logging
logger = setup_logging(enable_colors=False)

def get_earning_history(tickers, batch_size=1000):
    start_time = time.time()
    # Split tickers into batches
    ticker_batches = [tickers[i:i + batch_size] for i in range(0, len(tickers), batch_size)]
    
    logger.info(f"Processing {len(tickers)} tickers in {len(ticker_batches)} batches")
    
    result = {}
    
    for batch_idx, batch_tickers in enumerate(ticker_batches, 1):
        batch_start_time = time.time()
        print(f"\nProcessing batch {batch_idx}/{len(ticker_batches)}: {batch_tickers}")
        
        yq_tickers = yq.Ticker(batch_tickers, formatted=True)
        events = yq_tickers.calendar_events
        for ticker, data in events.items():
            if data is None:
                continue
            if ticker == 'CASY':
                pass

            if 'earnings' in data:
                # if 'earningsCallDate' in data['earnings'] and len(data['earnings']['earningsCallDate']) >= 1 and 'raw' in data['earnings']['earningsCallDate'][0]:
                    # result[ticker] = data['earnings']['earningsCallDate'][0]['raw']
                if 'earningsDate' in data['earnings'] and len(data['earnings']['earningsDate']) >= 1 and 'raw' in data['earnings']['earningsDate'][0]:
                    result[ticker] = data['earnings']['earningsDate'][0]['raw']
        
        batch_end_time = time.time()
        batch_execution_time = batch_end_time - batch_start_time
        logger.info(f"Batch {batch_idx} execution time: {batch_execution_time:.4f} seconds")
    
    end_time = time.time()
    execution_time = end_time - start_time
    logger.info(f"\nTotal execution time: {execution_time:.4f} seconds")
    
    return result

def get_earnings_dates(ticker):
    df = yf.Ticker(ticker).get_earnings_dates()
    f

if __name__ == "__main__":
    # tickers = [
    #     "GME",
    #     "CRWD", "FERG",
    #     "AAPL", "AACB", "AACG", "AFRI", "AFYA", 
    #     "9618.HK", "0001.HK", "0010.HK", "1000.HK", "1001.HK",
    #     "000100.KS", "001000.KQ", "100030.KS", "100090.KS", "100130.KS",
    #     "1301.T", "1332.T", "1333.T", "1352.T", "1376.T",
    #     "5483.TWO", "1101.TW", "1102.TW", "1103.TW", "1104.TW"
    # ]

    # print(get_next_earning_date_timestamp(tickers))


    dates = yf.Ticker("AAPL").get_earnings_dates().to_dict()
    pass
