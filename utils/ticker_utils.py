"""
Ticker symbol utility functions.

This module provides utility functions for handling ticker symbol conversions
and formatting across different markets and data providers.
"""

from typing import List, Dict, <PERSON>ple
import yfinance as yf
import logging

# Set up logger
logger = logging.getLogger(__name__)

# Cache for ticker validity results to avoid repeated API calls
_ticker_validity_cache = {}


def _test_ticker_validity(symbol: str) -> bool:
    """
    Test if a ticker symbol is valid by attempting to fetch basic info.

    This function provides a lightweight way to test ticker validity with caching
    to avoid repeated API calls for the same symbol.

    Args:
        symbol (str): Ticker symbol to test

    Returns:
        bool: True if ticker is valid and has data, False otherwise
    """
    # Check cache first
    if symbol in _ticker_validity_cache:
        logger.debug(f"Using cached result for {symbol}: {_ticker_validity_cache[symbol]}")
        return _ticker_validity_cache[symbol]

    try:
        ticker = yf.Ticker(symbol)

        # # Use fast_info which is lighter than full info
        # info = ticker.fast_info
        # # Check if we got meaningful data
        # # fast_info should have at least some basic fields if the ticker is valid
        # is_valid = hasattr(info, 'last_price') or hasattr(info, 'market_cap')

        # if not is_valid:
        #     # Fallback: try to get basic info (more expensive but more reliable)
        #     basic_info = ticker.info
        #     is_valid = bool(basic_info and len(basic_info) > 1)

        if ticker.info:
            is_valid = True
        else:
            is_valid = False

        # Cache the result
        _ticker_validity_cache[symbol] = is_valid
        logger.info(f"Ticker validation for {symbol}: {is_valid}")
        return is_valid

    except Exception as e:
        # Log the error for debugging but don't raise
        logger.warning(f"Ticker validation failed for {symbol}: {str(e)}")
        # Cache negative results too to avoid repeated failures
        _ticker_validity_cache[symbol] = False
        return False


def _get_kr_exchange_candidates(padded_symbol: str) -> tuple:
    """
    Get Korean exchange candidates in order of likelihood.

    This function uses heuristics to determine which Korean exchange
    (KOSPI or KOSDAQ) is more likely for a given symbol, reducing
    the number of API calls needed.

    Args:
        padded_symbol (str): 6-digit padded Korean symbol

    Returns:
        tuple: (primary_candidate, secondary_candidate)
    """
    # Convert to integer for range checking
    try:
        symbol_num = int(padded_symbol)
    except ValueError:
        # Non-numeric symbols default to KOSPI
        return f"{padded_symbol}.KS", f"{padded_symbol}.KQ"

    # KOSPI (main market) heuristics:
    # - Large companies typically have lower numbers
    # - Numbers starting with 0-2 are usually KOSPI
    # - Well-known ranges: 000001-099999 (mostly KOSPI)
    if symbol_num <= 99999:  # 000001-099999 range
        return f"{padded_symbol}.KS", f"{padded_symbol}.KQ"

    # KOSDAQ (secondary market) heuristics:
    # - Technology and smaller companies
    # - Numbers starting with 1-9 in higher ranges are often KOSDAQ
    # - Ranges: 100000+ (mixed, but more KOSDAQ)
    elif symbol_num >= 100000:
        # For higher numbers, KOSDAQ is slightly more likely
        return f"{padded_symbol}.KQ", f"{padded_symbol}.KS"

    # Default case: KOSPI first (it's the main market)
    return f"{padded_symbol}.KS", f"{padded_symbol}.KQ"


def _get_tw_exchange_candidates(symbol: str) -> tuple:
    """
    Get Taiwan exchange candidates in order of likelihood.

    This function uses heuristics to determine which Taiwan exchange
    (TWSE or TPEx) is more likely for a given symbol, reducing
    the number of API calls needed.

    Taiwan Stock Exchange (TWSE) - .TW:
    - Main board stocks (large companies)
    - 4-digit codes: 1000-9999
    - Most established companies

    Taipei Exchange (TPEx, formerly OTC) - .TWO:
    - Over-the-counter market (smaller companies)
    - 4-digit codes: typically higher numbers
    - Growth and emerging companies

    Args:
        symbol (str): Taiwan stock symbol

    Returns:
        tuple: (primary_candidate, secondary_candidate)
    """
    # Handle both numeric and non-numeric symbols
    if symbol.isdigit():
        try:
            symbol_num = int(symbol)

            # TWSE (main market) heuristics:
            # - Lower numbers (1000-6999) are typically TWSE
            # - Well-established companies
            if symbol_num <= 6999:
                return f"{symbol}.TW", f"{symbol}.TWO"

            # TPEx (OTC market) heuristics:
            # - Higher numbers (7000+) are often TPEx
            # - Smaller and emerging companies
            else:
                return f"{symbol}.TWO", f"{symbol}.TW"

        except ValueError:
            pass

    # For non-numeric symbols or edge cases, default to TWSE first
    # (it's the main market and more commonly used)
    return f"{symbol}.TW", f"{symbol}.TWO"


def clear_ticker_validity_cache():
    """
    Clear the ticker validity cache.

    This function can be called to free memory if the cache becomes too large
    or to force re-validation of all tickers.
    """
    global _ticker_validity_cache
    cache_size = len(_ticker_validity_cache)
    _ticker_validity_cache.clear()
    logger.info(f"Cleared ticker validity cache ({cache_size} entries)")


def get_ticker_validity_cache_stats():
    """
    Get statistics about the ticker validity cache.

    Returns:
        dict: Cache statistics including size and hit/miss ratios
    """
    return {
        'cache_size': len(_ticker_validity_cache),
        'cached_symbols': list(_ticker_validity_cache.keys()),
        'valid_symbols': [k for k, v in _ticker_validity_cache.items() if v],
        'invalid_symbols': [k for k, v in _ticker_validity_cache.items() if not v]
    }


def convert_jp_ticker_to_yf(ticker_symbol: str) -> str:
    """
    Convert Japanese ticker format to Yahoo Finance format.

    This function handles the conversion of Japanese stock ticker symbols
    from the standard format (without exchange suffix) to the Yahoo Finance format.

    Conversion Rules:
    - 1301 -> 1301.T (add .T suffix)
    - 7203 -> 7203.T (add .T suffix)
    - 1301.T -> 1301.T (already in YF format, keep as is)
    - AAPL -> AAPL (non-Japanese ticker, keep as is)
    - 1301.HK -> 1301.HK (other market suffix, keep as is)

    Args:
        ticker_symbol (str): Original Japanese ticker symbol

    Returns:
        str: Yahoo Finance formatted ticker symbol
    """

    if not ticker_symbol.endswith('.T'):
        ticker_symbol += '.T'
    return ticker_symbol


def convert_tw_ticker_to_yf(ticker_symbol: str) -> str:
    """
    Convert Taiwan ticker format to Yahoo Finance format.

    This function handles the conversion of Taiwan stock ticker symbols
    from the standard format to the Yahoo Finance format, supporting both
    TWSE (.TW) and TPEx (.TWO) exchanges.

    Taiwan Stock Exchanges:
    - TWSE (.TW): Taiwan Stock Exchange (main board)
    - TPEx (.TWO): Taipei Exchange (OTC market)

    Conversion Rules:
    - Strip leading zeros from symbol
    - Use intelligent heuristics to determine most likely exchange
    - Test primary exchange first, fallback to secondary if needed

    Args:
        ticker_symbol (str): Original Taiwan ticker symbol

    Returns:
        str: Yahoo Finance formatted ticker symbol

    Examples:
        >>> convert_tw_ticker_to_yf("2330")
        '2330.TW'  # TSMC - likely TWSE (after validation)
        >>> convert_tw_ticker_to_yf("8046")
        '8046.TWO'  # Higher number - likely TPEx (after validation)
        >>> convert_tw_ticker_to_yf("2330.TW")
        '2330.TW'  # Already formatted
    """
    if ticker_symbol.endswith('.TW') or ticker_symbol.endswith('.TWO'):
        return ticker_symbol

    # Strip leading zeros and use generate_yf_symbol for intelligent selection
    stripped_symbol = strip_leading_zeros(ticker_symbol)
    return generate_yf_symbol(stripped_symbol, 'TW')

def convert_kr_ticker_to_yf(ticker_symbol: str) -> str:
    """
    Convert Korean ticker format to Yahoo Finance format.

    This function handles the conversion of Korean stock ticker symbols
    from the standard format to the Yahoo Finance format, supporting both
    KOSPI (.KS) and KOSDAQ (.KQ) exchanges.

    Korean Stock Exchanges:
    - KOSPI (.KS): Korea Composite Stock Price Index (main board)
    - KOSDAQ (.KQ): Korea Securities Dealers Automated Quotations (secondary market)

    Conversion Rules:
    - Strip leading zeros from symbol, then restore leading zeros for YF format
    - Use intelligent heuristics to determine most likely exchange
    - Test primary exchange first, fallback to secondary if needed

    Args:
        ticker_symbol (str): Original Korean ticker symbol

    Returns:
        str: Yahoo Finance formatted ticker symbol

    Examples:
        >>> convert_kr_ticker_to_yf("000020")
        '000020.KS'  # After validation
        >>> convert_kr_ticker_to_yf("005930")
        '005930.KS'  # Samsung - likely KOSPI
        >>> convert_kr_ticker_to_yf("20")
        '000020.KS'  # After padding and validation
    """
    if ticker_symbol.endswith('.KS') or ticker_symbol.endswith('.KQ'):
        return ticker_symbol

    # Strip leading zeros first
    stripped_symbol = strip_leading_zeros(ticker_symbol)

    # Use generate_yf_symbol for intelligent exchange selection
    return generate_yf_symbol(stripped_symbol, 'KR')

def convert_hk_ticker_to_yf(ticker_symbol: str) -> str:
    """
    Convert Hong Kong ticker format to Yahoo Finance format.

    This function handles the conversion of Hong Kong stock ticker symbols
    from the standard format to the Yahoo Finance format.

    New Conversion Rules (Updated):
    - Strip leading zeros from symbol, then pad to 4 digits for Yahoo Finance
    - 00700 -> 0700.HK (strip leading zeros, then pad to 4 digits)
    - 00001 -> 0001.HK (strip leading zeros, then pad to 4 digits)
    - 09977 -> 9977.HK (strip leading zeros, already 4+ digits)
    - 01234 -> 1234.HK (strip leading zeros, already 4+ digits)
    - 12345 -> 12345.HK (no leading zeros to strip, already 4+ digits)

    Args:
        ticker_symbol (str): Original Hong Kong ticker symbol

    Returns:
        str: Yahoo Finance formatted ticker symbol

    Examples:
        >>> convert_hk_ticker_to_yf("00700")
        '0700.HK'
        >>> convert_hk_ticker_to_yf("00001")
        '0001.HK'
        >>> convert_hk_ticker_to_yf("09977")
        '9977.HK'
        >>> convert_hk_ticker_to_yf("01234")
        '1234.HK'
        >>> convert_hk_ticker_to_yf("700.HK")
        '700.HK'
    """
    if ticker_symbol.endswith('.HK'):
        return ticker_symbol

    # Strip leading zeros and generate YF symbol using generate_yf_symbol
    stripped_symbol = strip_leading_zeros(ticker_symbol)
    return generate_yf_symbol(stripped_symbol, 'HK')


def convert_hk_tickers_to_yf(ticker_symbols: List[str]) -> Tuple[List[str], Dict[str, str]]:
    """
    Convert a list of Hong Kong ticker symbols to Yahoo Finance format.

    This function converts multiple Hong Kong ticker symbols and returns both
    the converted list and a mapping dictionary for reverse lookup.

    Args:
        ticker_symbols (List[str]): List of original Hong Kong ticker symbols

    Returns:
        Tuple[List[str], Dict[str, str]]: A tuple containing:
            - List of converted Yahoo Finance ticker symbols
            - Dictionary mapping YF tickers back to original tickers

    Examples:
        >>> tickers = ["00700.HK", "00001.HK", "09977.HK"]
        >>> yf_tickers, mapping = convert_hk_tickers_to_yf(tickers)
        >>> yf_tickers
        ['0700.HK', '0001.HK', '09977.HK']
        >>> mapping
        {'0700.HK': '00700.HK', '0001.HK': '00001.HK', '09977.HK': '09977.HK'}
    """
    yf_tickers = []
    ticker_mapping = {}

    for ticker in ticker_symbols:
        yf_ticker = convert_hk_ticker_to_yf(ticker)
        yf_tickers.append(yf_ticker)
        ticker_mapping[yf_ticker] = ticker

    return yf_tickers, ticker_mapping


def is_hk_ticker(ticker_symbol: str) -> bool:
    """
    Check if a ticker symbol is a Hong Kong stock ticker.

    Args:
        ticker_symbol (str): Ticker symbol to check

    Returns:
        bool: True if it's a Hong Kong ticker, False otherwise

    Examples:
        >>> is_hk_ticker("00700.HK")
        True
        >>> is_hk_ticker("AAPL")
        False
        >>> is_hk_ticker("09977.HK")
        True
    """
    return ticker_symbol.endswith('.HK')


def normalize_hk_ticker(ticker_symbol: str, target_format: str = 'original') -> str:
    """
    Normalize Hong Kong ticker symbol to a specific format.

    Args:
        ticker_symbol (str): Ticker symbol to normalize
        target_format (str): Target format ('original' or 'yf')
            - 'original': Standard format with leading zeros (e.g., "00700.HK")
            - 'yf': Yahoo Finance format (e.g., "0700.HK")

    Returns:
        str: Normalized ticker symbol

    Examples:
        >>> normalize_hk_ticker("0700.HK", "original")
        '00700.HK'
        >>> normalize_hk_ticker("00700.HK", "yf")
        '0700.HK'
    """
    if not is_hk_ticker(ticker_symbol):
        return ticker_symbol

    if target_format == 'yf':
        return convert_hk_ticker_to_yf(ticker_symbol)
    elif target_format == 'original':
        # Convert YF format back to original format
        if '.' in ticker_symbol:
            code, market = ticker_symbol.split('.')
            # Add leading zero if code is 4 digits and doesn't already start with '00'
            if len(code) == 4 and not code.startswith('00'):
                code = '0' + code
            return f"{code}.{market}"

    return ticker_symbol


def strip_leading_zeros(symbol: str) -> str:
    """
    Strip leading zeros from a ticker symbol while preserving at least one digit.

    Args:
        symbol (str): Original ticker symbol

    Returns:
        str: Symbol with leading zeros stripped

    Examples:
        >>> strip_leading_zeros("00700")
        '700'
        >>> strip_leading_zeros("000020")
        '20'
        >>> strip_leading_zeros("0000")
        '0'
        >>> strip_leading_zeros("1234")
        '1234'
    """
    if not symbol:
        return symbol
    # remove .HK if exists
    if symbol.endswith('.HK'):
        symbol = symbol[:-3]
    # Strip leading zeros but keep at least one digit
    stripped = symbol.lstrip('0')
    return stripped if stripped else '0'


def generate_yf_symbol(symbol: str, region: str) -> str:
    """
    Generate Yahoo Finance compatible symbol based on region.

    Args:
        symbol (str): Base ticker symbol (without leading zeros)
        region (str): Region code (HK, SG, KR, JP, TW, CA, FR, GE, MY, TH, UK, US)

    Returns:
        str: Yahoo Finance compatible symbol

    Examples:
        >>> generate_yf_symbol("700", "HK")
        '0700.HK'
        >>> generate_yf_symbol("1", "HK")
        '0001.HK'
        >>> generate_yf_symbol("12345", "HK")
        '12345.HK'
        >>> generate_yf_symbol("20", "SG")
        '20.SI'
        >>> generate_yf_symbol("20", "KR")
        '000020.KS'
        >>> generate_yf_symbol("1301", "JP")
        '1301.T'
        >>> generate_yf_symbol("AAPL", "US")
        'AAPL'
    """
    region = region.upper()

    if region == 'HK':
        # Hong Kong stocks need to be padded to 4 digits for Yahoo Finance
        if symbol.isdigit() and len(symbol) < 4:
            padded_symbol = symbol.zfill(4)
            return f"{padded_symbol}.HK"
        return f"{symbol}.HK"
    elif region == 'SG':
        return f"{symbol}.SI"
    elif region == 'KR':
        # Korean stocks need leading zeros restored for Yahoo Finance
        if len(symbol) < 6 and symbol.isdigit():
            padded_symbol = symbol.zfill(6)

            # Use heuristics to determine the most likely exchange first
            primary_candidate, secondary_candidate = _get_kr_exchange_candidates(padded_symbol)

            # Test primary candidate first (most likely to succeed)
            if _test_ticker_validity(primary_candidate):
                return primary_candidate

            # Test secondary candidate if primary fails
            if _test_ticker_validity(secondary_candidate):
                return secondary_candidate

            # If both fail, return primary format as default (most common)
            logger.warning(f"Both KR exchanges failed for {symbol}, defaulting to {primary_candidate}")
            return primary_candidate

        # For symbols >= 6 digits or non-numeric, default to KOSPI
        return f"{symbol}.KS"
    elif region == 'JP':
        return f"{symbol}.T"
    elif region == 'TW':
        # Taiwan stocks can be on TWSE (.TW) or TPEx (.TWO)
        # Use heuristics to determine the most likely exchange first
        primary_candidate, secondary_candidate = _get_tw_exchange_candidates(symbol)

        # Test primary candidate first (most likely to succeed)
        if _test_ticker_validity(primary_candidate):
            return primary_candidate

        # Test secondary candidate if primary fails
        if _test_ticker_validity(secondary_candidate):
            return secondary_candidate

        # If both fail, return primary format as default (most common)
        logger.warning(f"Both TW exchanges failed for {symbol}, defaulting to {primary_candidate}")
        return primary_candidate
    elif region == 'CA':
        return f"{symbol}.TO"
    elif region == 'FR':
        return f"{symbol}.PA"
    elif region == 'GE':
        return f"{symbol}.DE"
    elif region == 'MY':
        return f"{symbol}.KL"
    elif region == 'TH':
        return f"{symbol}.BK"
    elif region == 'UK':
        return f"{symbol}.L"
    elif region == 'US':
        return symbol  # US symbols don't need suffix
    else:
        # Default case - return symbol as is
        return symbol


def convert_sg_ticker_to_yf(ticker_symbol: str) -> str:
    """
    Convert Singapore ticker format to Yahoo Finance format.

    This function handles the conversion of Singapore stock ticker symbols
    from the standard format to the Yahoo Finance format.

    Conversion Rules:
    - Strip leading zeros from symbol
    - Add .SI suffix for Yahoo Finance

    Args:
        ticker_symbol (str): Original Singapore ticker symbol

    Returns:
        str: Yahoo Finance formatted ticker symbol

    Examples:
        >>> convert_sg_ticker_to_yf("000020")
        '20.SI'
        >>> convert_sg_ticker_to_yf("D05")
        'D05.SI'
        >>> convert_sg_ticker_to_yf("20.SI")
        '20.SI'
    """
    if ticker_symbol.endswith('.SI'):
        return ticker_symbol

    # Strip leading zeros
    stripped_symbol = strip_leading_zeros(ticker_symbol)
    return f"{stripped_symbol}.SI"


def convert_ca_ticker_to_yf(ticker_symbol: str) -> str:
    """
    Convert Canadian ticker format to Yahoo Finance format.

    Args:
        ticker_symbol (str): Original Canadian ticker symbol

    Returns:
        str: Yahoo Finance formatted ticker symbol

    Examples:
        >>> convert_ca_ticker_to_yf("000020")
        '20.TO'
        >>> convert_ca_ticker_to_yf("SHOP")
        'SHOP.TO'
    """
    if ticker_symbol.endswith('.TO'):
        return ticker_symbol

    stripped_symbol = strip_leading_zeros(ticker_symbol)
    return f"{stripped_symbol}.TO"


def convert_fr_ticker_to_yf(ticker_symbol: str) -> str:
    """
    Convert French ticker format to Yahoo Finance format.

    Args:
        ticker_symbol (str): Original French ticker symbol

    Returns:
        str: Yahoo Finance formatted ticker symbol

    Examples:
        >>> convert_fr_ticker_to_yf("000020")
        '20.PA'
        >>> convert_fr_ticker_to_yf("MC")
        'MC.PA'
    """
    if ticker_symbol.endswith('.PA'):
        return ticker_symbol

    stripped_symbol = strip_leading_zeros(ticker_symbol)
    return f"{stripped_symbol}.PA"


def convert_ge_ticker_to_yf(ticker_symbol: str) -> str:
    """
    Convert German ticker format to Yahoo Finance format.

    Args:
        ticker_symbol (str): Original German ticker symbol

    Returns:
        str: Yahoo Finance formatted ticker symbol

    Examples:
        >>> convert_ge_ticker_to_yf("000020")
        '20.DE'
        >>> convert_ge_ticker_to_yf("SAP")
        'SAP.DE'
    """
    if ticker_symbol.endswith('.DE'):
        return ticker_symbol

    stripped_symbol = strip_leading_zeros(ticker_symbol)
    return f"{stripped_symbol}.DE"


def convert_my_ticker_to_yf(ticker_symbol: str) -> str:
    """
    Convert Malaysian ticker format to Yahoo Finance format.

    Args:
        ticker_symbol (str): Original Malaysian ticker symbol

    Returns:
        str: Yahoo Finance formatted ticker symbol

    Examples:
        >>> convert_my_ticker_to_yf("000020")
        '20.KL'
        >>> convert_my_ticker_to_yf("GENTING")
        'GENTING.KL'
    """
    if ticker_symbol.endswith('.KL'):
        return ticker_symbol

    stripped_symbol = strip_leading_zeros(ticker_symbol)
    return f"{stripped_symbol}.KL"


def convert_th_ticker_to_yf(ticker_symbol: str) -> str:
    """
    Convert Thai ticker format to Yahoo Finance format.

    Args:
        ticker_symbol (str): Original Thai ticker symbol

    Returns:
        str: Yahoo Finance formatted ticker symbol

    Examples:
        >>> convert_th_ticker_to_yf("000020")
        '20.BK'
        >>> convert_th_ticker_to_yf("PTT")
        'PTT.BK'
    """
    if ticker_symbol.endswith('.BK'):
        return ticker_symbol

    stripped_symbol = strip_leading_zeros(ticker_symbol)
    return f"{stripped_symbol}.BK"


def convert_uk_ticker_to_yf(ticker_symbol: str) -> str:
    """
    Convert UK ticker format to Yahoo Finance format.

    Args:
        ticker_symbol (str): Original UK ticker symbol

    Returns:
        str: Yahoo Finance formatted ticker symbol

    Examples:
        >>> convert_uk_ticker_to_yf("000020")
        '20.L'
        >>> convert_uk_ticker_to_yf("LLOY")
        'LLOY.L'
    """
    if ticker_symbol.endswith('.L'):
        return ticker_symbol

    stripped_symbol = strip_leading_zeros(ticker_symbol)
    return f"{stripped_symbol}.L"