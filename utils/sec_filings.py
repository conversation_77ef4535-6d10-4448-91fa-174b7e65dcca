import requests
import json
import datetime  # Added import for datetime

from utils.logging_utils import setup_logging

logger = setup_logging(enable_colors=False)


SEC_ARCHIVE_URL = "https://www.sec.gov/Archives/edgar/data"
SEC_SUBMISSIONS_URL = "https://data.sec.gov/submissions"

def get_filings_by_cik(cik: str, filing_types = ['8-K', '10-Q', '10-K', '6-K', '20-F'], min_date: datetime.date = None):
    json_name = f"CIK{cik.zfill(10)}.json"
    url = f"{SEC_SUBMISSIONS_URL}/{json_name}"

    headers = {
        "User-Agent": "your_name <EMAIL>",
        "Content-Type": "application/json",
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        content = json.loads(response.content)
        recent_forms = content["filings"]["recent"]
        
        # Filter filings by type and date
        filings = []
        for i in range(len(recent_forms["accessionNumber"])):
            form = recent_forms["form"][i]
            filing_date = recent_forms["filingDate"][i]
            report_date = recent_forms["reportDate"][i]

            if min_date:
                # Convert string dates to datetime.date objects before comparison
                filing_date_obj = datetime.date.fromisoformat(filing_date)
                if filing_date_obj < min_date:
                    continue
            
            # Check if form type is in the list of filing types to fetch
            if form not in filing_types:
                continue
            accession_number = recent_forms["accessionNumber"][i]

            filings.append({
                "accession_number": accession_number,
                "form": form,
                "filing_date": filing_date,
                "report_date": report_date,
                "index_url": _build_filing_index_url(cik, accession_number),
                "txt_url": _build_filing_txt_url(cik, accession_number),
                "html_url": _build_filing_html_url(cik, accession_number),
            })
        
        return filings
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching filings for CIK {cik}: {str(e)}")
        return []
    
def _build_filing_index_url(cik: str, accession_number: str) -> str:
        cik = str(int(cik))
        accession_number_no_dashes = accession_number.replace('-', '')
        return f"{SEC_ARCHIVE_URL}/{cik}/{accession_number_no_dashes}/index.json"
    
def _build_filing_txt_url(cik: str, accession_number: str) -> str:
    cik = str(int(cik))
    accession_number_no_dashes = accession_number.replace('-', '')
    accession_number_with_dashes = f"{accession_number_no_dashes[:10]}-{accession_number_no_dashes[10:12]}-{accession_number_no_dashes[12:]}"
    return f"{SEC_ARCHIVE_URL}/{cik}/{accession_number_no_dashes}/{accession_number_with_dashes}.txt"

def _build_filing_html_url(cik: str, accession_number: str) -> str:
    cik = str(int(cik))
    accession_number_no_dashes = accession_number.replace('-', '')
    accession_number_with_dashes = f"{accession_number_no_dashes[:10]}-{accession_number_no_dashes[10:12]}-{accession_number_no_dashes[12:]}"
    return f"{SEC_ARCHIVE_URL}/{cik}/{accession_number_no_dashes}/{accession_number_with_dashes}-index.htm"