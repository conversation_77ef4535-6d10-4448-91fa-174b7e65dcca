"""
Utility functions for handling images.
"""

import os
import requests
import base64
from typing import Op<PERSON>, <PERSON><PERSON>, Union
from pathlib import Path

# Import GCS utilities
from config.settings import settings
from utils.gcs_utils import upload_to_gcs, get_file_extension_from_url, generate_gcs_object_name
from utils.logging_utils import setup_logging

# Configure logging
logger = setup_logging(enable_colors=False)

def download_logo(ticker: str, logo_url: str, save_dir: str) -> Tuple[Optional[str], Optional[bytes]]:
    """
    Download a logo image from a URL and upload it to Google Cloud Storage.

    If gcs_bucket is provided, the logo will be uploaded to GCS and the GCS URL will be returned.
    If gcs_bucket is None, the logo will be saved locally and the local file path will be returned.

    Args:
        ticker: The ticker symbol
        logo_url: The URL of the logo image
        save_dir: The directory to save the logo image to (only used if gcs_bucket is None)

    Returns:
        Tuple containing:
        - The URL or path to the logo image (or None if download failed)
        - The binary data of the logo image (or None if download failed)
    """
    if not logo_url:
        logger.warning(f"No logo URL provided for ticker {ticker}")
        return None, None

    try:
        logger.info(f"Checking logo URL: {logo_url}")
        # Download the logo image
        response = requests.get(logo_url, timeout=30)
        if response.status_code == 200:
            logger.info(f"Found logo for ticker {ticker}")
        else:
            logger.warning(f"No logo found for ticker {ticker} (status code: {response.status_code})")
            return None, None

        # Get the file extension from the URL or default to .png
        file_extension = get_file_extension_from_url(logo_url)

        # If GCS bucket is provided, upload to GCS
        if settings.GCS_LOGO_BUCKET:
            # Generate object name for GCS
            object_name = generate_gcs_object_name(ticker, settings.GCS_LOGO_DIR+save_dir, file_extension)

            # Upload to GCS
            gcs_url = upload_to_gcs(
                file_data=response.content,
                object_name=object_name,
                bucket_name=settings.GCS_LOGO_BUCKET,
                content_type=f"image/{file_extension.lstrip('.')}"
            )

            if gcs_url:
                logger.info(f"Uploaded logo for ticker {ticker} to GCS: {gcs_url}")
                return gcs_url, response.content
            else:
                logger.error(f"Failed to upload logo for ticker {ticker} to GCS")
                return None, None
        # else:
        #     # Save locally (original behavior)
        #     # Create the save directory if it doesn't exist
        #     os.makedirs(settings.GCS_LOGO_DIR+save_dir, exist_ok=True)

        #     # Create the file path (ensure it's an absolute path)
        #     file_path = os.path.abspath(os.path.join(settings.GCS_LOGO_DIR+save_dir, f"{ticker}{file_extension}"))

        #     # Save the logo image to disk
        #     with open(file_path, "wb") as f:
        #         f.write(response.content)

        #     logger.info(f"Downloaded logo for ticker {ticker} to {file_path}")

        #     # Return the file path and binary data
        #     return file_path, response.content

    except Exception as e:
        logger.error(f"Error downloading logo for ticker {ticker}: {str(e)}")
        return None, None

def get_logo_as_base64(logo_path: Union[str, Path]) -> Optional[str]:
    """
    Convert a logo image to a base64-encoded string.

    Args:
        logo_path: The path to the logo image

    Returns:
        The base64-encoded string of the logo image, or None if conversion failed
    """
    try:
        with open(logo_path, "rb") as f:
            logo_data = f.read()

        # Convert to base64
        logo_base64 = base64.b64encode(logo_data).decode("utf-8")

        return logo_base64

    except Exception as e:
        logger.error(f"Error converting logo to base64: {str(e)}")
        return None
