"""
YFinance API wrapper with enhanced error handling for rate limiting.

This module provides a wrapper around the yfinance library that adds
retry logic with exponential backoff specifically for rate limiting errors.
"""

import time
import random
import logging
from functools import wraps
from typing import List, Dict, Any, Optional, Union, Callable

import yfinance as yf
import pandas as pd
from yfinance.exceptions import YFRateLimitError

from utils.logging import logger

# Default retry settings
DEFAULT_MAX_RETRIES = 5
DEFAULT_MIN_WAIT = 60.0
DEFAULT_MAX_WAIT = 120.0
DEFAULT_JITTER_FACTOR = 0.1
DEFAULT_BATCH_SIZE_REDUCTION_FACTOR = 0.5
DEFAULT_MIN_BATCH_SIZE = 5


def with_rate_limit_retry(
    max_retries: int = DEFAULT_MAX_RETRIES,
    min_wait: float = DEFAULT_MIN_WAIT,
    max_wait: float = DEFAULT_MAX_WAIT,
    jitter_factor: float = DEFAULT_JITTER_FACTOR
) -> Callable:
    """
    Decorator to apply retry pattern with exponential backoff for YFRateLimitError.
    
    Args:
        max_retries: Maximum number of retry attempts
        min_wait: Minimum wait time between retries in seconds
        max_wait: Maximum wait time between retries in seconds
        jitter_factor: Factor to apply random jitter to wait time
        
    Returns:
        Decorated function with retry logic
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            retries = 0
            while True:
                try:
                    return func(*args, **kwargs)
                except YFRateLimitError as e:
                    retries += 1
                    if retries > max_retries:
                        logger.error(f"Maximum retries ({max_retries}) exceeded for {func.__name__}")
                        raise
                    
                    # Calculate wait time with exponential backoff and jitter
                    wait_time = min(max_wait, min_wait * (2 ** (retries - 1)))
                    jitter = random.uniform(-jitter_factor * wait_time, jitter_factor * wait_time)
                    wait_time = max(min_wait, wait_time + jitter)
                    
                    logger.warning(
                        f"Rate limit exceeded in {func.__name__}. "
                        f"Retrying in {wait_time:.2f}s (attempt {retries}/{max_retries})"
                    )
                    time.sleep(wait_time)
        return wrapper
    return decorator


class YFinanceWrapper:
    """
    Wrapper for yfinance API with enhanced error handling.
    
    This class provides methods that wrap common yfinance operations
    with retry logic and circuit breaker pattern to handle rate limiting.
    """
    
    def __init__(
        self,
        max_retries: int = DEFAULT_MAX_RETRIES,
        min_wait: float = DEFAULT_MIN_WAIT,
        max_wait: float = DEFAULT_MAX_WAIT,
        jitter_factor: float = DEFAULT_JITTER_FACTOR,
        batch_size_reduction_factor: float = DEFAULT_BATCH_SIZE_REDUCTION_FACTOR,
        min_batch_size: int = DEFAULT_MIN_BATCH_SIZE
    ):
        """
        Initialize the wrapper with retry settings.
        
        Args:
            max_retries: Maximum number of retry attempts
            min_wait: Minimum wait time between retries in seconds
            max_wait: Maximum wait time between retries in seconds
            jitter_factor: Factor to apply random jitter to wait time
            batch_size_reduction_factor: Factor to reduce batch size by when rate limited
            min_batch_size: Minimum batch size to use
        """
        self.max_retries = max_retries
        self.min_wait = min_wait
        self.max_wait = max_wait
        self.jitter_factor = jitter_factor
        self.batch_size_reduction_factor = batch_size_reduction_factor
        self.min_batch_size = min_batch_size
        
        # Track rate limiting occurrences
        self.rate_limit_count = 0
        self.last_rate_limit_time = None
        self.current_optimal_batch_size = None

    @with_rate_limit_retry()
    def get_ticker_info(self, ticker_symbol: str) -> Dict[str, Any]:
        """
        Get info for a single ticker with retry logic.
        
        Args:
            ticker_symbol: Ticker symbol
            
        Returns:
            Dict containing ticker info
        """
        ticker = yf.Ticker(ticker_symbol)
        try:
            ticker_info = ticker.info
            return ticker_info
        except Exception as e:
            logger.error(f"Error fetching info for {ticker_symbol}: {str(e)}")
            return None
    
    @with_rate_limit_retry()
    def get_ticker_history(
        self,
        ticker_symbol: str,
        period: str = "1mo",
        interval: str = "1d",
        **kwargs
    ) -> pd.DataFrame:
        """
        Get price history for a single ticker with retry logic.
        
        Args:
            ticker_symbol: Ticker symbol
            period: Period to fetch (e.g., "1d", "1mo", "1y")
            interval: Interval between data points (e.g., "1m", "1h", "1d")
            **kwargs: Additional arguments to pass to ticker.history()
            
        Returns:
            DataFrame containing price history
        """
        ticker = yf.Ticker(ticker_symbol)
        try:
            history = ticker.history(period=period, interval=interval, **kwargs)
            return history
        except Exception as e:
            logger.error(f"Error fetching history for {ticker_symbol}: {str(e)}")
            return pd.DataFrame()
    
    def get_tickers_batch(
        self,
        ticker_symbols: List[str],
        batch_size: Optional[int] = None
    ) -> yf.Tickers:
        """
        Get multiple tickers as a batch with retry logic.
        
        Args:
            ticker_symbols: List of ticker symbols
            batch_size: Optional batch size override
            
        Returns:
            yf.Tickers object containing the requested tickers
        """
        # Use tracked optimal batch size if available and not overridden
        if batch_size is None and self.current_optimal_batch_size is not None:
            batch_size = self.current_optimal_batch_size
        
        # Apply the retry decorator dynamically
        @with_rate_limit_retry(
            max_retries=self.max_retries,
            min_wait=self.min_wait,
            max_wait=self.max_wait,
            jitter_factor=self.jitter_factor
        )
        def _get_tickers_batch(symbols: List[str]) -> yf.Tickers:
            try:
                return yf.Tickers(" ".join(symbols))
            except YFRateLimitError:
                # Update rate limit tracking
                self.rate_limit_count += 1
                self.last_rate_limit_time = time.time()
                
                # Reduce optimal batch size if this was using the current optimal
                if batch_size == self.current_optimal_batch_size or self.current_optimal_batch_size is None:
                    if self.current_optimal_batch_size is None:
                        # First rate limit, set to half of current batch
                        self.current_optimal_batch_size = max(
                            self.min_batch_size,
                            int(len(symbols) * self.batch_size_reduction_factor)
                        )
                    else:
                        # Further reduce the optimal batch size
                        self.current_optimal_batch_size = max(
                            self.min_batch_size,
                            int(self.current_optimal_batch_size * self.batch_size_reduction_factor)
                        )
                    
                    logger.warning(
                        f"Rate limit hit. Reducing optimal batch size to {self.current_optimal_batch_size}"
                    )
                
                # Re-raise to let the retry decorator handle it
                raise
        
        return _get_tickers_batch(ticker_symbols)
    
    def process_tickers_in_adaptive_batches(
        self,
        ticker_symbols: List[str],
        process_func: Callable[[yf.Tickers, List[str]], Any],
        initial_batch_size: int = 25,
        delay_between_batches: float = 1.0
    ) -> List[Any]:
        """
        Process tickers in adaptive batches with rate limit handling.
        
        Args:
            ticker_symbols: List of ticker symbols to process
            process_func: Function to process each batch of tickers
                          Should accept (tickers, batch_symbols) as arguments
            initial_batch_size: Initial batch size to use
            delay_between_batches: Delay between batches in seconds
            
        Returns:
            List of results from processing each batch
        """
        results = []
        
        # Set initial optimal batch size if not already set
        if self.current_optimal_batch_size is None:
            self.current_optimal_batch_size = initial_batch_size
        
        # Process tickers in batches
        for i in range(0, len(ticker_symbols), self.current_optimal_batch_size):
            batch = ticker_symbols[i:i+self.current_optimal_batch_size]
            logger.info(f"Processing batch {i//self.current_optimal_batch_size + 1}/"
                       f"{(len(ticker_symbols) + self.current_optimal_batch_size - 1)//self.current_optimal_batch_size} "
                       f"with {len(batch)} tickers (optimal batch size: {self.current_optimal_batch_size})")
            
            try:
                # Get tickers for the batch
                tickers_data = self.get_tickers_batch(batch)
                
                # Process the batch
                batch_results = process_func(tickers_data, batch)
                results.append(batch_results)
                
                # Add delay between batches to avoid rate limiting
                if i + self.current_optimal_batch_size < len(ticker_symbols):
                    # Calculate adaptive delay based on rate limiting history
                    adaptive_delay = delay_between_batches
                    if self.rate_limit_count > 0:
                        # Increase delay if we've hit rate limits recently
                        time_since_last_limit = (time.time() - self.last_rate_limit_time) if self.last_rate_limit_time else float('inf')
                        if time_since_last_limit < 60:  # Within the last minute
                            adaptive_delay = min(delay_between_batches * 2, 5.0)  # Cap at 5 seconds
                    
                    logger.debug(f"Sleeping for {adaptive_delay:.2f} seconds")
                    time.sleep(adaptive_delay)
                
            except Exception as e:
                logger.error(f"Error processing batch: {str(e)}")
                # If we hit a rate limit, the batch size will be automatically adjusted
                # for the next iteration
        
        return results
