#!/usr/bin/env python3
"""
Script to update existing SEC filings with HTML URLs.
"""

import os
import sys
import time
import argparse
import datetime
import traceback
import requests
from bs4 import BeautifulSoup
from ratelimit import limits, sleep_and_retry
from sqlalchemy import text

from utils.logging_utils import setup_logging

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jobs.db.mysql_client import get_db_session
from jobs.db.schema import SECFiling
from config.settings import settings

logger = setup_logging(enable_colors=False)

# SEC API constants
SEC_ARCHIVE_URL = "https://www.sec.gov/Archives/edgar/data"

class SECFilingsHTMLURLUpdater:
    """
    Script to update existing SEC filings with HTML URLs.
    """
    
    def __init__(
        self,
        batch_size: int = 100,
        delay: float = 0.1,
        max_retries: int = 5,
        min_wait: float = 2.0,
        max_wait: float = 60.0
    ):
        """
        Initialize the script.
        
        Args:
            batch_size: Number of filings to process in each batch
            delay: Delay between requests in seconds
            max_retries: Maximum number of retry attempts for rate limiting
            min_wait: Minimum wait time between retries in seconds
            max_wait: Maximum wait time between retries in seconds
        """
        self.batch_size = batch_size
        self.delay = delay
        self.max_retries = max_retries
        self.min_wait = min_wait
        self.max_wait = max_wait
        
        # SEC API settings
        self.sec_organization = settings.SEC_API_ORGANIZATION
        self.sec_email = settings.SEC_API_EMAIL
        
        # Statistics
        self.processed_count = 0
        self.updated_count = 0
        self.error_count = 0
    
    def run(self):
        """
        Run the script.
        """
        logger.info("Starting SEC filings HTML URL updater")
        start_time = time.time()
        
        try:
            # Get filings without HTML URL
            with get_db_session() as session:
                # Count total filings without HTML URL
                total_count = session.query(SECFiling).filter(
                    (SECFiling.html_url.is_(None)) | 
                    (SECFiling.html_url == '')
                ).count()
                
                logger.info(f"Found {total_count} filings without HTML URL")
                
                if total_count == 0:
                    logger.info("No filings to update, exiting")
                    return True
                
                # Process filings in batches
                offset = 0
                while offset < total_count:
                    # Get batch of filings
                    filings = session.query(SECFiling).filter(
                        (SECFiling.html_url.is_(None)) | 
                        (SECFiling.html_url == '')
                    ).limit(self.batch_size).offset(offset).all()
                    
                    if not filings:
                        break
                    
                    logger.info(f"Processing batch of {len(filings)} filings (offset: {offset})")
                    
                    # Process each filing
                    for filing in filings:
                        try:
                            # Build HTML URL
                            html_url = self._build_html_filing_url(filing.cik, filing.accession_number, filing.ticker)
                            
                            if html_url:
                                # Update filing
                                filing.html_url = html_url
                                self.updated_count += 1
                            
                            self.processed_count += 1
                            
                            # Add delay between requests
                            time.sleep(self.delay)
                        except Exception as e:
                            logger.error(f"Error processing filing {filing.id}: {str(e)}")
                            logger.error(traceback.format_exc())
                            self.error_count += 1
                    
                    # Commit batch
                    session.commit()
                    
                    # Update offset
                    offset += self.batch_size
                    
                    logger.info(f"Processed {self.processed_count}/{total_count} filings, updated {self.updated_count}, errors {self.error_count}")
            
            end_time = time.time()
            logger.info(f"Script completed successfully in {end_time - start_time:.2f} seconds")
            logger.info(f"Processed {self.processed_count} filings, updated {self.updated_count}, errors {self.error_count}")
            
            return True
        except Exception as e:
            logger.error(f"Error running script: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def _build_filing_url(self, cik: str, accession_number: str) -> str:
        """
        Build URL for a filing in TXT format.
        
        Args:
            cik: CIK number
            accession_number: Accession number
            
        Returns:
            str: URL for the filing in TXT format
        """
        # Remove dashes from accession number
        accession_number_no_dashes = accession_number.replace('-', '')
        
        # Add dashes to accession number for filename
        accession_number_with_dashes = f"{accession_number_no_dashes[:10]}-{accession_number_no_dashes[10:12]}-{accession_number_no_dashes[12:]}"
        
        # Build URL
        return f"{SEC_ARCHIVE_URL}/{cik}/{accession_number_no_dashes}/{accession_number_with_dashes}.txt"
    
    @sleep_and_retry
    @limits(calls=10, period=1)
    def _build_html_filing_url(self, cik: str, accession_number: str, ticker: str) -> str:
        """
        Build URL for a filing in HTML format.
        
        Args:
            cik: CIK number
            accession_number: Accession number
            ticker: Ticker symbol
            
        Returns:
            str: URL for the filing in HTML format or None if not found
        """
        # First, try to get the HTML URL from the TXT filing
        txt_url = self._build_filing_url(cik, accession_number)
        
        headers = {
            "User-Agent": f"{self.sec_organization} {self.sec_email}",
            "Content-Type": "text/html",
        }
        
        try:
            # Get the TXT filing content
            response = requests.get(txt_url, headers=headers)
            response.raise_for_status()
            
            # Parse the content to find HTML links
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Look for HTML document links
            for a_tag in soup.find_all('a'):
                href = a_tag.get('href')
                if href and href.endswith('.htm'):
                    # Found an HTML link, construct the full URL
                    accession_number_no_dashes = accession_number.replace('-', '')
                    return f"{SEC_ARCHIVE_URL}/{cik}/{accession_number_no_dashes}/{href}"
            
            # If we couldn't find an HTML link, try to construct it based on the example
            # Example: https://www.sec.gov/Archives/edgar/data/320193/000032019323000106/aapl-20230930.htm
            # Try to construct a URL with the ticker and filing date
            accession_number_no_dashes = accession_number.replace('-', '')
            
            # Get the filing date from the accession number (first 8 digits of accession number)
            filing_year = accession_number_no_dashes[:4]
            filing_month = accession_number_no_dashes[4:6]
            filing_day = accession_number_no_dashes[6:8]
            
            # Construct a filename with ticker and date
            html_filename = f"{ticker.lower()}-{filing_year}{filing_month}{filing_day}.htm"
            
            # Build URL
            return f"{SEC_ARCHIVE_URL}/{cik}/{accession_number_no_dashes}/{html_filename}"
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching filing content for CIK {cik}, accession number {accession_number}: {str(e)}")
            return None

def parse_args():
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description='Update existing SEC filings with HTML URLs')
    parser.add_argument('--batch-size', type=int, default=100, help='Number of filings to process in each batch')
    parser.add_argument('--delay', type=float, default=0.1, help='Delay between requests in seconds')
    parser.add_argument('--max-retries', type=int, default=5, help='Maximum number of retry attempts for rate limiting')
    parser.add_argument('--min-wait', type=float, default=2.0, help='Minimum wait time between retries in seconds')
    parser.add_argument('--max-wait', type=float, default=60.0, help='Maximum wait time between retries in seconds')
    
    return parser.parse_args()

def main():
    """
    Main entry point.
    """
    args = parse_args()
    
    # Create and run the script
    updater = SECFilingsHTMLURLUpdater(
        batch_size=args.batch_size,
        delay=args.delay,
        max_retries=args.max_retries,
        min_wait=args.min_wait,
        max_wait=args.max_wait
    )
    
    success = updater.run()
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
