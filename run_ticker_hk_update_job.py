#!/usr/bin/env python3
"""
Run the Hong Kong ticker update job.

This script fetches Hong Kong stock tickers from Tushare and stores them in the database.
"""

import os
import sys
import argparse
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jobs.ticker_hk_update_job import TickerHKUpdateJob
from utils.logging_utils import setup_logging

def main():
    """
    Main entry point for the script.
    """
    parser = argparse.ArgumentParser(description="Run the Hong Kong ticker update job")
    parser.add_argument("--skip-logo", action="store_true", help="Skip logo updates")
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv()
    logger = setup_logging(enable_colors=False)
    # Set environment variable for logo skipping if specified
    if args.skip_logo:
        os.environ["SKIP_UPDATE_LOGO"] = "true"
        logger.info("Logo updates will be skipped")
    
    # Run the job
    logger.info("Starting Hong Kong ticker update job")
    job = TickerHKUpdateJob()
    success = job.run()
    
    if success:
        logger.info("Hong Kong ticker update job completed successfully")
    else:
        logger.error("Hong Kong ticker update job failed")
        
    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
