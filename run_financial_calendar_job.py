import argparse
from jobs.financial_calendar_job import FinancialCalendarJob
from config.settings import Settings
from utils.logging_utils import setup_logging  # Use the centralized logging setup

def parse_arguments():
    parser = argparse.ArgumentParser(description='Run the financial calendar job')
    parser.add_argument('--real-time', action='store_true', 
                        help='Run in real-time monitoring mode')
    parser.add_argument('--check-interval', type=int, default=None,
                        help='Interval in seconds between checks in real-time mode')
    parser.add_argument('--event-id', type=int, default=None,
                        help='ID of a specific event to monitor')
    parser.add_argument('--schedule-only', action='store_true',
                        help='Only schedule events without monitoring')
    return parser.parse_args()

def main():
    # Parse command line arguments
    args = parse_arguments()
    
    # Configure logging using centralized setup
    logger = setup_logging(enable_colors=False)
    logger.info("Starting financial calendar job")
    
    # Initialize and run job
    job = FinancialCalendarJob()
    job.run()

if __name__ == "__main__":
    main()
