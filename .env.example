# MySQL Configuration
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DATABASE=stock_data

# Job Settings
JOB_BATCH_SIZE=10
JOB_DELAY=1.0
JOB_PRICE_DAYS=30
JOB_FULL_REFRESH=false
JOB_SPECIFIC_TICKERS=AAPL,MSFT,GOOGL,AMZN,META

# Worker Settings
WORKER_THREADS=4

# Alpha Vantage API Settings
ALPHA_VANTAGE_API_KEY=your_api_key_here
ALPHA_VANTAGE_RATE_LIMIT=5  # Requests per minute
ALPHA_VANTAGE_BASE_URL=https://www.alphavantage.co/query  # Base URL for Alpha Vantage API
ALPHA_VANTAGE_LISTING_FUNCTION=LISTING_STATUS  # Function for fetching ticker lists

# Logo API Settings
LOGO_DEV_TOKEN=your_logo_dev_token_here  # Token for Logo.dev direct URL

# Google Cloud Storage Settings
GCS_LOGO_BUCKET=your_gcs_bucket_name  # GCS bucket for storing logo images

# YFinance Rate Limiting Settings
YF_MAX_RETRIES=5  # Maximum number of retry attempts for rate limiting
YF_MIN_WAIT=2.0   # Minimum wait time between retries in seconds
YF_MAX_WAIT=60.0  # Maximum wait time between retries in seconds

# Financial Calendar Settings
FINANCIAL_CALENDAR_REAL_TIME=false
FINANCIAL_CALENDAR_CHECK_INTERVAL=60  # Seconds between checks
FINANCIAL_CALENDAR_DAYS_AHEAD=7
FINANCIAL_CALENDAR_EVENT_TYPES=CPI,CORE_CPI,PPI,NFP,GDP,INTEREST_RATE
FINANCIAL_CALENDAR_EVENT_ID=  # For monitoring a specific event
FINANCIAL_CALENDAR_SCHEDULE_ONLY=false  # Only schedule events without monitoring
FINANCIAL_CALENDAR_CRON_USER=  # User for crontab operations (leave empty for current user)
