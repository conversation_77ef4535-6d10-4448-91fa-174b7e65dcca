#!/bin/bash
# <PERSON><PERSON> script to run the SEC filings job

# Set environment variables
export PYTHONPATH=/Users/<USER>/Documents/go-gitlab-repos/go-gpt-job:$PYTHONPATH

# Change to the project directory
cd /Users/<USER>/Documents/go-gitlab-repos/go-gpt-job

# Activate virtual environment if needed
# source venv/bin/activate

# Run the job
python run_sec_filings_job.py

# Exit with the same status as the job
exit $?
