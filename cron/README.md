# Cron Jobs

This directory contains scripts for running scheduled jobs.

## Stock Data Job

The `stock_data_job.sh` script is designed to be run as a cron job to fetch stock data from Yahoo Finance and store it in the MySQL database.

### Setup

1. Make sure the script is executable:
   ```bash
   chmod +x stock_data_job.sh
   ```

2. Set up a cron job to run the script daily. For example, to run the job every day at 6:00 PM (after market close):
   ```bash
   # Edit the crontab
   crontab -e
   
   # Add the following line to run the job at 6:00 PM every day
   0 18 * * * /path/to/go-gpt-job/cron/stock_data_job.sh
   ```

3. Alternatively, you can use a system-wide cron job by creating a file in `/etc/cron.d/`:
   ```bash
   # Create a new file
   sudo nano /etc/cron.d/stock_data_job
   
   # Add the following line (replace USER with your username)
   0 18 * * * USER /path/to/go-gpt-job/cron/stock_data_job.sh
   ```

### Logs

The script logs its output to the `logs/stock_data_job_YYYYMMDD.log` file, where `YYYYMMDD` is the current date. This allows you to track the job's execution and troubleshoot any issues.

### Customization

If you want to customize the job parameters (e.g., which tickers to fetch, how many days of price history to fetch), you can modify the `stock_data_job.sh` script or create a new script that calls the `jobs/stock_data_job.py` script with the desired parameters.

For example:
```bash
# Fetch data for specific tickers with 30 days of price history
python jobs/stock_data_job.py --tickers AAPL,MSFT,GOOGL --price-days 30
```

See the [Stock Data Job Documentation](../docs/stock_data_job.md) for more details on the available parameters.

## Docker Setup

If you're running the application in Docker, you can add the cron job to the container by modifying the Dockerfile:

```dockerfile
# Install cron
RUN apt-get update && apt-get install -y cron

# Copy the cron job script
COPY cron/stock_data_job.sh /app/cron/stock_data_job.sh
RUN chmod +x /app/cron/stock_data_job.sh

# Add the cron job
RUN echo "0 18 * * * /app/cron/stock_data_job.sh >> /var/log/cron.log 2>&1" | crontab -

# Start cron in the background
CMD cron && python run.py
```

Alternatively, you can use a separate container for the cron job:

```dockerfile
FROM python:3.9-slim

# Install cron
RUN apt-get update && apt-get install -y cron

# Copy the application code
COPY . /app
WORKDIR /app

# Install dependencies
RUN pip install -r requirements.txt

# Copy the cron job script
COPY cron/stock_data_job.sh /app/cron/stock_data_job.sh
RUN chmod +x /app/cron/stock_data_job.sh

# Add the cron job
RUN echo "0 18 * * * /app/cron/stock_data_job.sh >> /var/log/cron.log 2>&1" | crontab -

# Start cron
CMD cron -f
```

## Kubernetes Setup

If you're running the application in Kubernetes, you can use a CronJob resource to run the job:

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: stock-data-job
spec:
  schedule: "0 18 * * *"  # Run at 6:00 PM every day
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: stock-data-job
            image: your-image:tag
            command: ["/bin/bash", "-c", "/app/cron/stock_data_job.sh"]
            env:
            - name: MYSQL_HOST
              value: mysql-service
            - name: MYSQL_PORT
              value: "3306"
            - name: MYSQL_USER
              valueFrom:
                secretKeyRef:
                  name: mysql-credentials
                  key: username
            - name: MYSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: mysql-credentials
                  key: password
            - name: MYSQL_DATABASE
              value: stock_data
          restartPolicy: OnFailure
