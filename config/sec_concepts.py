"""
Configuration file for SEC XBRL concepts to be stored in the database.
This file defines the most common and important financial metrics from the us-gaap taxonomy.
"""

# Common financial concepts in the us-gaap taxonomy to be stored in the database
COMMON_FINANCIAL_CONCEPTS = [
    # Income Statement
    "Revenues",
    "RevenueFromContractWithCustomerExcludingAssessedTax",
    "CostOfGoodsAndServicesSold",
    "CostOfRevenue",
    "GrossProfit",

    "ResearchAndDevelopmentExpense",
    "SellingGeneralAndAdministrativeExpense",
    "SellingAndMarketingExpense",
    "GeneralAndAdministrativeExpense",

    "OperatingExpenses",
    "CostsAndExpenses",
    "InterestIncome",
    "InterestIncomeOther",

    "InterestExpense",
    "DepreciationAndAmortization",
    "DepreciationDepletionAndAmortization",
    "OperatingIncomeLoss",
    "NetIncomeLoss",

    "IncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest",
    "IncomeTaxExpenseBenefit",
    "EarningsPerShareBasic",
    "EarningsPerShareDiluted",
    
    # Balance Sheet
    "Assets",
    "Liabilities",
    "StockholdersEquity",
    "CashAndCashEquivalentsAtCarryingValue",
    "LongTermDebtCurrent",
    "LongTermDebtNonCurrent",
    "ShortTermInvestments",
    "LongTermInvestments",
    "Inventory",
    "InventoryNet",
    "Goodwill",
    "IntangibleAssetsNetExcludingGoodwill",
    "RetainedEarningsAccumulatedDeficit",
    "AccumulatedOtherComprehensiveIncomeLossNetOfTax",

    "AssetsCurrent",
    "AccountsReceivableNetCurrent",
    "AssetsNoncurrent",
    "PropertyPlantAndEquipmentNet",
    
    "LiabilitiesCurrent",
    "AccountsPayableCurrent",
    "AccruedLiabilitiesCurrent",
    "LiabilitiesNoncurrent",
    "LongTermDebt",
    
    "CommonStockParOrStatedValuePerShare",
    "CommonStockSharesAuthorized",
    "CommonStockSharesIssued",
    "CommonStockSharesOutstanding",
    
    # Cash Flow
    "NetCashProvidedByUsedInOperatingActivities",
    "NetCashProvidedByUsedInInvestingActivities",
    "NetCashProvidedByUsedInFinancingActivities",
    "CapitalExpendituresIncurredButNotYetPaid",
    "PaymentsOfDividends",
    "CashAndCashEquivalentsPeriodIncreaseDecrease",
    "CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect",
    
    # Other Key Metrics
    "CapitalExpenditure",
    "DividendsPaid",
    "ShareBasedCompensation",
    "WeightedAverageNumberOfSharesOutstandingBasic",
    "WeightedAverageNumberOfDilutedSharesOutstanding",
    
    # Additional Dividend Metrics
    "DividendRate",
    "DividendYield",
    "CommonStockDividendsPerShareDeclared",
    "CommonStockDividendsPerShareCashPaid",
    "DividendPayoutRatio",
    "CashDividendsCashPaid",
]

# DEI (Document and Entity Information) concepts to store
DEI_CONCEPTS = [
    "EntityRegistrantName",
    "EntityCentralIndexKey",
    "EntityTaxIdentificationNumber",
    "EntityAddressAddressLine1",
    "EntityAddressAddressLine2",
    "EntityAddressAddressLine3",
    "EntityAddressCityOrTown",
    "EntityAddressStateOrProvince",
    "EntityAddressCountry",
    "EntityAddressPostalZipCode",
    "EntityIncorporationStateCountryCode",
    "EntityTaxIdentificationNumber",
    "EntityFileNumber",
    "EntityFilerCategory",
    "EntityWellKnownSeasonedIssuer",
    "EntityCurrentReportingStatus",
    "EntityVoluntaryFilers",
    "EntityPublicFloat",
    "DocumentType",
    "DocumentPeriodEndDate",
    "DocumentFiscalYearFocus",
    "DocumentFiscalPeriodFocus",
]

# All concepts to store (us-gaap + dei + additional dividend concepts)
ALL_CONCEPTS_TO_STORE = COMMON_FINANCIAL_CONCEPTS + DEI_CONCEPTS + [
    # Additional dividend concepts not already in COMMON_FINANCIAL_CONCEPTS
    "DividendPaymentDate",
    "DividendDeclarationDate",
    "DividendExDate",
    "DividendRecordDate",
    "SpecialDividendPerShare",
    "StockDividendsPerShare",
    "PreferredStockDividendsPerShareDeclared",
    "PreferredStockDividendRate",
]
