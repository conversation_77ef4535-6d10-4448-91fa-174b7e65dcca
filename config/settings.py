import os
from dotenv import load_dotenv
from urllib.parse import quote_plus
import yfinance as yf
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from utils.logging_utils import setup_logging

# Load environment variables from .env file
load_dotenv()
logger = setup_logging(enable_colors=False)

class Settings:
    # MySQL settings
    MYSQL_HOST = os.getenv("MYSQL_HOST", "localhost")
    MYSQL_PORT = int(os.getenv("MYSQL_PORT", 3306))
    MYSQL_USER = os.getenv("MYSQL_USER", "root")
    MYSQL_PASSWORD = quote_plus(str(os.getenv("MYSQL_PASSWORD", "password")))
    MYSQL_DATABASE = os.getenv("MYSQL_DATABASE", "stock_data")

    MYSQL_URI = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}"
    
    # Database connection pool settings
    DB_POOL_SIZE = 200
    DB_MAX_OVERFLOW = 300
    DB_POOL_TIMEOUT = 600
    DB_POOL_RECYCLE = 3600
    
    # Job settings
    JOB_NAME = os.getenv("JOB_NAME")
    JOB_BATCH_SIZE = int(os.getenv("JOB_BATCH_SIZE", 50))
    JOB_DELAY = float(os.getenv("JOB_DELAY", 1.0))
    JOB_PRICE_DAYS = int(os.getenv("JOB_PRICE_DAYS", 30))
    JOB_FULL_REFRESH = os.getenv("JOB_FULL_REFRESH", "false").lower() == "true"
    JOB_SPECIFIC_TICKERS = os.getenv("JOB_SPECIFIC_TICKERS", "").split(",") if os.getenv("JOB_SPECIFIC_TICKERS") else None
    ROOT_JOB_NAME = os.getenv("ROOT_JOB_NAME")
    # Edit job->Container->Security->Service account
    CREATE_TRIGGER_SERVICE_ACCOUNT = os.getenv("CREATE_TRIGGER_SERVICE_ACCOUNT")
    
    # Worker settings
    WORKER_THREADS = int(os.getenv("WORKER_THREADS", 4))

    # Alpha Vantage settings
    ALPHA_VANTAGE_API_KEY = os.getenv("ALPHA_VANTAGE_API_KEY")
    ALPHA_VANTAGE_RATE_LIMIT = int(os.getenv("ALPHA_VANTAGE_RATE_LIMIT", 5))  # Requests per minute
    ALPHA_VANTAGE_BASE_URL = "https://www.alphavantage.co/query"
    ALPHA_VANTAGE_FUNCTION = "ECONOMIC_CALENDAR"
    ALPHA_VANTAGE_LISTING_FUNCTION = "LISTING_STATUS"
    
    # Financial Calendar settings
    FINANCIAL_CALENDAR_REAL_TIME = os.getenv("FINANCIAL_CALENDAR_REAL_TIME", "false").lower() == "true"
    FINANCIAL_CALENDAR_CHECK_INTERVAL = int(os.getenv("FINANCIAL_CALENDAR_CHECK_INTERVAL", 60))  # Seconds between checks
    FINANCIAL_CALENDAR_DAYS_AHEAD = int(os.getenv("FINANCIAL_CALENDAR_DAYS_AHEAD", 7))
    FINANCIAL_CALENDAR_EVENT_TYPES = os.getenv("FINANCIAL_CALENDAR_EVENT_TYPES", "CPI,CORE_CPI,PPI,NFP,GDP,INTEREST_RATE").split(",")
    FINANCIAL_CALENDAR_EVENT_ID = int(os.getenv("FINANCIAL_CALENDAR_EVENT_ID")) if os.getenv("FINANCIAL_CALENDAR_EVENT_ID") else None
    FINANCIAL_CALENDAR_SCHEDULE_ONLY = os.getenv("FINANCIAL_CALENDAR_SCHEDULE_ONLY", "false").lower() == "true"
    FINANCIAL_CALENDAR_CRON_USER = os.getenv("FINANCIAL_CALENDAR_CRON_USER")  # User for crontab operations
    FINANCIAL_CALENDAR_SOURCE = os.getenv("FINANCIAL_CALENDAR_SOURCE", "alpha_vantage")
    FINANCIAL_CALENDAR_NOTIFICATION_FOR_DEBUG = os.getenv("FINANCIAL_CALENDAR_NOTIFICATION_FOR_DEBUG", "false").lower() == "true"
    FINANCIAL_CALENDAR_DISABLED_SUMMARY = os.getenv("FINANCIAL_CALENDAR_DISABLED_SUMMARY", "false").lower() == "true"

    # OpenAI settings
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

    # Vector settings
    VECTOR_NAME = os.getenv("VECTOR_NAME", "test_vector")
    
    # SEC API settings
    SEC_API_ORGANIZATION = os.getenv("SEC_API_ORGANIZATION", "ADDX")
    SEC_API_EMAIL = os.getenv("SEC_API_EMAIL", "<EMAIL>")
    SEC_API_RATE_LIMIT = int(os.getenv("SEC_API_RATE_LIMIT", 10))  # Requests per second
    SEC_FILING_TYPES = os.getenv("SEC_FILING_TYPES", "10-K,10-Q,8-K").split(",")
    SEC_FILING_SECTIONS = os.getenv("SEC_FILING_SECTIONS", "RISK_FACTORS,MD_AND_A,BUSINESS,FINANCIAL_STATEMENTS,LEGAL_PROCEEDINGS,PROPERTIES").split(",")
    SEC_FILINGS_YEARS = int(os.getenv("SEC_FILINGS_YEARS", 5))  # Number of years of filings to fetch
    SEC_FILING_TICKERS = os.getenv("SEC_FILING_TICKERS")
    SEC_BATCH_SIZE = int(os.getenv("SEC_BATCH_SIZE", 50))  # Number of tickers to process in each batch
    SEC_DELAY = float(os.getenv("SEC_DELAY", 2.0))  # Delay between batches in seconds
    
    # SEC XBRL API settings
    SEC_XBRL_API_URL = "https://data.sec.gov/api/xbrl/companyfacts"
    SEC_CIK_LOOKUP_URL = os.getenv("SEC_CIK_LOOKUP_URL", "https://www.sec.gov/files/company_tickers.json")    
    SEC_XBRL_CONCEPTS = os.getenv("SEC_XBRL_CONCEPTS", "Revenue,NetIncomeLoss,Assets,Liabilities,StockholdersEquity,EarningsPerShareBasic,EarningsPerShareDiluted,CashAndCashEquivalentsAtCarryingValue,OperatingIncomeLoss,GrossProfit").split(",")
    SEC_XBRL_TAXONOMIES = os.getenv("SEC_XBRL_TAXONOMIES", "us-gaap,dei").split(",")
    SEC_XBRL_BATCH_SIZE = int(os.getenv("SEC_XBRL_BATCH_SIZE", 5))  # Number of tickers to process in each batch
    SEC_XBRL_DELAY = float(os.getenv("SEC_XBRL_DELAY", 2.0))  # Delay between batches in seconds
    SEC_XBRL_YEARS = int(os.getenv("SEC_XBRL_YEARS", 5))  # Number of years of data to fetch

    # Redis settings
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))

    # chrome settings
    CHROME_ON_HOST = os.getenv("CHROME_ON_HOST", 'false')
    CHROME_DRIVER_PATH = os.getenv("CHROME_DRIVER_PATH", '/usr/local/bin/chromedriver')

    # Google Cloud Pub/Sub settings
    ENABLE_PUBSUB = os.getenv('ENABLE_PUBSUB', 'false').lower() == 'true'
    PUBSUB_FINANCIAL_CALENDAR_TOPIC = os.getenv('PUBSUB_FINANCIAL_CALENDAR_TOPIC', 'gpt.financial.calendar.events')
    PUBSUB_EARNINGS_CALENDAR_EVENT_TOPIC = os.getenv('PUBSUB_EARNINGS_CALENDAR_EVENT_TOPIC', 'gpt.earnings.calendar.events')
    
    # Google Cloud Storage settings
    GCS_LOGO_BUCKET = os.getenv('GCS_LOGO_BUCKET', 'images.dev.addxgo.io')
    GCS_LOGO_DIR = os.getenv('GCS_LOGO_DIR', 'tickers/logos')
    SKIP_UPDATE_LOGO = os.getenv('SKIP_UPDATE_LOGO', 'false').lower() == 'true'

# Create a settings instance to be imported by other modules
settings = Settings()

# Export individual variables for backward compatibility
MYSQL_HOST = settings.MYSQL_HOST
MYSQL_PORT = settings.MYSQL_PORT
MYSQL_USER = settings.MYSQL_USER
MYSQL_PASSWORD = settings.MYSQL_PASSWORD
MYSQL_DATABASE = settings.MYSQL_DATABASE
MYSQL_URI = settings.MYSQL_URI
JOB_BATCH_SIZE = settings.JOB_BATCH_SIZE
JOB_DELAY = settings.JOB_DELAY
JOB_PRICE_DAYS = settings.JOB_PRICE_DAYS
JOB_FULL_REFRESH = settings.JOB_FULL_REFRESH
JOB_SPECIFIC_TICKERS = settings.JOB_SPECIFIC_TICKERS
WORKER_THREADS = settings.WORKER_THREADS
ALPHA_VANTAGE_API_KEY = settings.ALPHA_VANTAGE_API_KEY
ALPHA_VANTAGE_RATE_LIMIT = settings.ALPHA_VANTAGE_RATE_LIMIT
ALPHA_VANTAGE_BASE_URL = settings.ALPHA_VANTAGE_BASE_URL
ALPHA_VANTAGE_FUNCTION = settings.ALPHA_VANTAGE_FUNCTION
ALPHA_VANTAGE_LISTING_FUNCTION = settings.ALPHA_VANTAGE_LISTING_FUNCTION

logger.info(f"settings: {settings.__class__.__dict__}")
print(yf.__version__)

def create_database():
    try:
        # Create engine without database name
        base_uri = f"mysql+pymysql://{settings.MYSQL_USER}:{settings.MYSQL_PASSWORD}@{settings.MYSQL_HOST}:{settings.MYSQL_PORT}"
        engine = create_engine(base_uri)
        
        # Create database if it doesn't exist
        with engine.connect() as conn:
            conn.execute(text(f"CREATE DATABASE IF NOT EXISTS {settings.MYSQL_DATABASE}"))
            
    except SQLAlchemyError as err:
        print(f"Database creation error: {err}")
        raise

if settings.JOB_NAME != 'vector':
    create_database()
