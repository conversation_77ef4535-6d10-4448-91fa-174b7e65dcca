#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the SEC filings job.
"""

import os
import sys
import argparse
from jobs.sec_filings_job import SECFilingsJob, parse_args
from config.settings import settings

def main():
    """
    Main entry point.
    """
    # Parse command line arguments
    args = parse_args()
    
    # Parse tickers if provided
    specific_tickers = args.tickers.split(',') if args.tickers else None
    
    # Create and run the job
    job = SECFilingsJob(
        full_refresh=args.full_refresh,
        specific_tickers=specific_tickers,
        batch_size=args.batch_size,
        delay=args.delay,
        years=args.years,
        max_retries=args.max_retries,
        min_wait=args.min_wait,
        max_wait=args.max_wait
    )
    
    success = job.run()
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
