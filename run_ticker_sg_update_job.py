#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the Singapore ticker update job with configurable options.

This script provides a convenient way to run the Singapore ticker update job
with various configuration options. It reads Singapore stock data from SGX JSON
file and updates the all_tickers_sg database table.

Usage:
    python run_ticker_sg_update_job.py [options]

Examples:
    # Run with logo updates (default)
    python run_ticker_sg_update_job.py

    # Skip logo updates for faster execution
    python run_ticker_sg_update_job.py --skip-logo

    # Run in verbose mode
    python run_ticker_sg_update_job.py --verbose
"""

import argparse
import os
import sys
from dotenv import load_dotenv

from utils.logging_utils import setup_logging

# Add the current directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jobs.ticker_sg_update_job import TickerSGUpdateJob

def main():
    """
    Main entry point for the Singapore ticker update job.
    """
    # Set up argument parser
    parser = argparse.ArgumentParser(
        description="Run the Singapore ticker update job",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                    # Run with default settings
  %(prog)s --skip-logo        # Skip logo downloads
  %(prog)s --verbose          # Enable verbose logging

Data Source:
  The job reads Singapore stock data from jobs/tickers_sg/sgx.json
  and saves it to the all_tickers_sg database table.

Logo Processing:
  By default, the job will download logos from logo.dev API and
  upload them to Google Cloud Storage. Use --skip-logo to disable
  this feature for faster execution.
        """
    )

    parser.add_argument(
        "--skip-logo",
        action="store_true",
        help="Skip logo downloads and processing"
    )

    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Enable verbose logging output"
    )

    args = parser.parse_args()

    # Load environment variables
    load_dotenv()

    # Set up logging with appropriate level
    logger = setup_logging(enable_colors=True)

    if args.verbose:
        logger.info("Verbose logging enabled")

    # Display job information
    logger.info("=" * 60)
    logger.info("Singapore Ticker Update Job")
    logger.info("=" * 60)
    logger.info(f"Data source: jobs/tickers_sg/sgx.json")
    logger.info(f"Target table: all_tickers_sg")
    logger.info(f"Skip logo updates: {args.skip_logo}")
    logger.info("=" * 60)

    # Set environment variable for logo skipping if specified
    if args.skip_logo:
        os.environ["SKIP_UPDATE_LOGO"] = "true"
        logger.info("Logo updates will be skipped for faster execution")

    # Validate prerequisites
    if not _validate_prerequisites():
        logger.error("Prerequisites validation failed")
        sys.exit(1)

    # Run the actual job
    success = _run_actual_job(args.skip_logo)

    # Display results and exit
    _display_results(success)
    sys.exit(0 if success else 1)

def _validate_prerequisites():
    """
    Validate that all prerequisites are met before running the job.

    Returns:
        bool: True if all prerequisites are met, False otherwise
    """
    logger = setup_logging(enable_colors=True)

    # Check if SGX JSON file exists
    sgx_file_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
        'jobs', 'tickers_sg', 'sgx.json'
    )

    if not os.path.exists(sgx_file_path):
        logger.error(f"SGX JSON file not found: {sgx_file_path}")
        logger.error("Please ensure the Singapore ticker data file exists")
        return False

    logger.info(f"✅ SGX JSON file found: {sgx_file_path}")

    # Check database connection (this will be done by the job itself)
    logger.info("✅ Prerequisites validation passed")
    return True

def _run_actual_job(skip_logo):
    """
    Run the actual Singapore ticker update job.

    Args:
        skip_logo (bool): Whether to skip logo processing

    Returns:
        bool: True if job completed successfully
    """
    logger = setup_logging(enable_colors=True)

    try:
        logger.info("Starting Singapore ticker update job...")

        # Create and run the job
        job = TickerSGUpdateJob(skip_update_logo=skip_logo)
        job.run()

        logger.info("✅ Singapore ticker update job completed successfully")
        logger.info(f"📊 Processed {job.processed_count} tickers")

        return True

    except Exception as e:
        logger.error(f"❌ Singapore ticker update job failed: {str(e)}")
        return False

def _display_results(success):
    """
    Display the final results of the job execution.

    Args:
        success (bool): Whether the job completed successfully
    """
    logger = setup_logging(enable_colors=True)

    logger.info("=" * 60)
    if success:
        logger.info("🎉 JOB COMPLETED SUCCESSFULLY")
    else:
        logger.error("💥 JOB FAILED")
    logger.info("=" * 60)

if __name__ == '__main__':
    main()
