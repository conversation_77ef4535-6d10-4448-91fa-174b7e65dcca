#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the ticker update job with configurable options.
"""

import argparse
import os
import sys
from dotenv import load_dotenv

from utils.logging_utils import setup_logging

# Add the current directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jobs.ticker_update_job import TickerUpdateJob

def main():
    """
    Main entry point.
    """
    # Set up argument parser
    parser = argparse.ArgumentParser(description="Run the ticker update job")
    parser.add_argument("--skip-logo", action="store_true", help="Skip logo updates")
    parser.add_argument("--use-tushare", action="store_true", help="Use Tushare API as data source")
    args = parser.parse_args()

    # Load environment variables
    load_dotenv()
    logger = setup_logging(enable_colors=False)

    # Set environment variable for logo skipping if specified
    if args.skip_logo:
        os.environ["SKIP_UPDATE_LOGO"] = "true"
        logger.info("Logo updates will be skipped")

    # Check Tushare token if using Tushare
    if args.use_tushare:
        tushare_token = os.getenv('TUSHARE_TOKEN')
        if not tushare_token:
            logger.error("TUSHARE_TOKEN environment variable is not set")
            logger.info("Please set your Tushare token: export TUSHARE_TOKEN='your_token_here'")
            logger.info("Get your token from: https://tushare.pro/")
            sys.exit(1)

        logger.info("Using Tushare API as data source")
    else:
        logger.info("Using JSON files as data source")

    # Create and run the job
    job = TickerUpdateJob(use_tushare=args.use_tushare, skip_update_logo=args.skip_logo)

    success = job.run()

    if success:
        logger.info("Ticker update job completed successfully")
        logger.info(f"Processed {job.processed_count} tickers")
        if job.error_count > 0:
            logger.warning(f"Encountered {job.error_count} errors")
    else:
        logger.error("Ticker update job failed")

    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
