#!/usr/bin/env python3
"""
Job to fetch Hong Kong stock market data and store it in MySQL database.
"""

import os
import sys
import time
import logging
import argparse
import datetime
import traceback
import io
import requests
from typing import List, Dict, Any, Optional, Set, Tuple
import pandas as pd
import yfinance as yf
from sqlalchemy.exc import SQLAlchemyError
import json
from yfinance.exceptions import YFRateLimitError

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.db.mysql_client import MySQLClient, get_db_session
from jobs.db.schema import AllTickerHK, StockHK, StockHKPrice, StockHKFundamental, JobRun
from jobs.db.migrations.migrations import apply_migrations
from utils.logging_utils import logger
from utils.yfinance_wrapper import YFinanceWrapper
from utils.ticker_utils import convert_hk_ticker_to_yf
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', f'stock_hk_data_job_{datetime.datetime.now().strftime("%Y%m%d")}.log'))
    ]
)

class StockHKDataJob:
    """
    Job to fetch Hong Kong stock market data and store it in MySQL database.
    """

    def __init__(
        self,
        full_refresh: bool = False,
        specific_tickers: Optional[List[str]] = None,
        batch_size: int = 25,
        delay: float = 1.0,
        price_days: int = 30,
        max_retries: int = 5,
        min_wait: float = 60.0,
        max_wait: float = 120.0
    ):
        """
        Initialize the job.

        Args:
            full_refresh: Whether to refresh all data
            specific_tickers: List of specific tickers to process
            batch_size: Number of stocks to process in each batch
            delay: Delay between batches in seconds
            price_days: Number of days of price history to fetch
            max_retries: Maximum number of retry attempts for rate limiting
            min_wait: Minimum wait time between retries in seconds
            max_wait: Maximum wait time between retries in seconds
        """
        self.full_refresh = full_refresh
        self.specific_tickers = specific_tickers
        self.batch_size = batch_size
        self.delay = delay
        self.price_days = price_days
        self.job_run_id = None  # Store ID instead of instance
        self.processed_count = 0
        self.error_count = 0
        self.batch_counter = 0  # Add batch counter

        # Initialize YFinance wrapper with retry settings
        self.yf_wrapper = YFinanceWrapper(
            max_retries=max_retries,
            min_wait=min_wait,
            max_wait=max_wait,
            batch_size_reduction_factor=0.5,
            min_batch_size=5
        )

        # Initialize database
        self._init_database()



    def _init_database(self):
        """
        Initialize the database.
        """
        logger.info("Initializing database")
        try:
            # Apply migrations instead of creating tables directly
            apply_migrations()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise

    def run(self):
        """
        Run the job.
        """
        logger.info("Starting Hong Kong stock data job")
        start_time = time.time()

        try:
            # Create job run record and store its ID
            with get_db_session() as session:
                job_run = JobRun.create_job_run(session, "stock_hk_data_job")
                session.flush()  # Ensure we have the ID
                self.job_run_id = job_run.id
                session.commit()
                logger.info(f"Created job run record with ID: {self.job_run_id}")

            # Get list of tickers
            with get_db_session() as session:
                tickers = self._get_tickers(session)
            logger.info(f"Found {len(tickers)} Hong Kong tickers to process")

            # Process tickers in batches
            self._process_tickers(tickers)

            # Mark job as completed using a fresh session
            with get_db_session() as session:
                job_run = session.query(JobRun).get(self.job_run_id)
                job_run.complete(session, self.processed_count)

            end_time = time.time()
            logger.info(f"Job completed successfully in {end_time - start_time:.2f} seconds")
            logger.info(f"Processed {self.processed_count} stocks with {self.error_count} errors")

            return True
        except Exception as e:
            logger.error(f"Error running job: {str(e)}")
            logger.error(traceback.format_exc())

            # Mark job as failed using a fresh session
            if self.job_run_id:
                with get_db_session() as session:
                    job_run = session.query(JobRun).get(self.job_run_id)
                    if job_run:
                        job_run.fail(session, str(e), self.processed_count)

            return False

    def _get_tickers(self, session) -> List[str]:
        """
        Get list of Hong Kong tickers from the database.

        Args:
            session: SQLAlchemy session

        Returns:
            List[str]: List of Hong Kong tickers
        """
        if self.specific_tickers:
            # Filter by specific tickers if provided
            return self.specific_tickers

        # Get all tickers from the database
        return [row[0] for row in session.query(AllTickerHK.symbol).all()]

    def _get_ticker_detail(self, session, symbol: str) -> AllTickerHK:
        """
        Get ticker detail from the database.

        Args:
            session: SQLAlchemy session
            symbol: Ticker symbol

        Returns:
            AllTickerHK: Ticker detail
        """
        return session.query(AllTickerHK).filter(AllTickerHK.symbol == symbol).first()

    def _process_tickers(self, tickers: List[str]):
        """
        Process tickers in batches using the YFinanceWrapper.

        Args:
            tickers: List of tickers to process
        """
        logger.info(f"Processing {len(tickers)} tickers with initial batch size of {self.batch_size}")

        # Define a function to process each batch that will be passed to the wrapper
        def process_batch_func(tickers_data, batch_symbols):
            batch_results = []
            for yf_ticker_symbol in batch_symbols:
                try:
                    # batch_symbols contains already converted YF ticker symbols
                    # Map back to original ticker symbol
                    original_ticker_symbol = ticker_mapping.get(yf_ticker_symbol, yf_ticker_symbol)

                    # Get the ticker from the batch
                    ticker = tickers_data.tickers.get(yf_ticker_symbol)

                    if ticker:
                        # Process the ticker
                        self._process_ticker(original_ticker_symbol, ticker, yf_ticker_symbol)

                        # Increment processed count
                        self.processed_count += 1
                        batch_results.append(original_ticker_symbol)
                    else:
                        logger.warning(f"Ticker {yf_ticker_symbol} not found in batch data")
                        self.error_count += 1
                except Exception as e:
                    logger.error(f"Error processing ticker {yf_ticker_symbol}: {str(e)}")
                    logger.error(traceback.format_exc())
                    self.error_count += 1

            # Increment batch counter
            self.batch_counter += 1

            return batch_results

        # Convert Hong Kong tickers to Yahoo Finance format for batch processing
        yf_tickers = []
        ticker_mapping = {}  # Map YF tickers back to original tickers

        for ticker in tickers:
            yf_ticker = convert_hk_ticker_to_yf(ticker)

            yf_tickers.append(yf_ticker)
            ticker_mapping[yf_ticker] = ticker

        # Use the wrapper to process tickers in adaptive batches
        try:
            results = self.yf_wrapper.process_tickers_in_adaptive_batches(
                yf_tickers,
                process_batch_func,
                initial_batch_size=self.batch_size,
                delay_between_batches=self.delay
            )

            # Log the final optimal batch size for future reference
            if self.yf_wrapper.current_optimal_batch_size != self.batch_size:
                logger.info(f"Adjusted optimal batch size to {self.yf_wrapper.current_optimal_batch_size} due to rate limiting")

        except Exception as e:
            logger.error(f"Error processing tickers: {str(e)}")
            logger.error(traceback.format_exc())
            self.error_count += len(tickers)

    def _process_ticker(self, ticker_symbol: str, ticker: yf.Ticker, yf_ticker_symbol: str, max_retries: int = 5):
        """
        Process a single ticker.

        Args:
            ticker_symbol: Original ticker symbol (e.g., "00700.HK")
            ticker: yfinance Ticker object
            yf_ticker_symbol: Yahoo Finance ticker symbol (e.g., "700.HK")
            max_retries: Maximum number of retry attempts for fetching ticker info
        """
        logger.debug(f"Processing ticker: {ticker_symbol} (YF: {yf_ticker_symbol})")

        try:
            info = None
            retry_count = 0
            retry_delay = 60  # Initial delay in seconds

            while info is None and retry_count <= max_retries:
                try:
                    # Get stock info
                    info = self.yf_wrapper.get_ticker_info(yf_ticker_symbol)

                    # If we've retried and succeeded, log it
                    if retry_count > 0 and info:
                        logger.info(f"Successfully retrieved ticker {yf_ticker_symbol} info after {retry_count} retries")

                except Exception as e:
                    logger.warning(f"Attempt {retry_count + 1}/{max_retries + 1} to get ticker {yf_ticker_symbol} info failed: {str(e)}")

                # If still no info, retry with exponential backoff unless we've hit max retries
                if info is None:
                    retry_count += 1
                    if retry_count <= max_retries:
                        wait_time = retry_delay * (2 ** (retry_count - 1))  # Exponential backoff
                        logger.info(f"No info available for ticker {yf_ticker_symbol}, retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})")
                        time.sleep(wait_time)
                    else:
                        logger.warning(f"No info available for ticker {yf_ticker_symbol} after {max_retries + 1} attempts, skipping")

            # Skip if no info available after all retries
            if not info:
                logger.warning(f"No info available for ticker {yf_ticker_symbol} after all retries, skipping")
                return

            # Store basic stock info
            self._store_stock_info(ticker_symbol, info)

            # Store price history
            self._store_price_history(ticker_symbol, ticker)

            # Store fundamental data
            self._store_fundamental_data(ticker_symbol, info)

            logger.debug(f"Successfully processed ticker: {ticker_symbol}")
        except Exception as e:
            logger.error(f"Error processing ticker {ticker_symbol}: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def _store_stock_info(self, ticker_symbol: str, info: Dict[str, Any]):
        """Store basic stock info in the database."""
        logger.debug(f"Storing basic info for ticker: {ticker_symbol}")

        def convert_value(value: Any) -> Any:
            """Convert values to appropriate format for database storage."""
            if value is None:
                return None
            if isinstance(value, (list, dict)):
                return json.dumps(value)
            if isinstance(value, bool):
                return 1 if value else 0
            if isinstance(value, (int, float)):
                return str(value)
            return value

        try:
            with get_db_session() as session:
                # Get ticker detail
                ticker_detail = self._get_ticker_detail(session, ticker_symbol)

                existing_stock = session.query(StockHK).filter(StockHK.symbol == ticker_symbol).first()

                # Convert all values to appropriate format before storing
                stock_data = {k: convert_value(v) for k, v in {
                    # Primary identifiers
                    'symbol': ticker_symbol,
                    'name': info.get('shortName', info.get('longName', ticker_symbol)),
                    'last_updated': datetime.datetime.now(),

                    # Company information
                    'address1': info.get('address1'),
                    'city': info.get('city'),
                    'state': info.get('state'),
                    'zip': info.get('zip'),
                    'country': 'Hong Kong',
                    'phone': info.get('phone'),
                    'website': info.get('website'),

                    # Industry classification
                    'industry': info.get('industry'),
                    'industry_key': info.get('industryKey'),
                    'industry_disp': info.get('industryDisp'),
                    'sector': info.get('sector'),
                    'sector_key': info.get('sectorKey'),
                    'sector_disp': info.get('sectorDisp'),

                    # Company details
                    'long_business_summary': info.get('longBusinessSummary') if info.get('fullTimeEmployees') is not None else None,
                    'full_time_employees': str(info.get('fullTimeEmployees')) if info.get('fullTimeEmployees') is not None else None,
                    'company_officers': json.dumps(info.get('companyOfficers')) if info.get('companyOfficers') is not None else None,

                    # Risk metrics
                    'audit_risk': info.get('auditRisk'),
                    'board_risk': info.get('boardRisk'),
                    'compensation_risk': info.get('compensationRisk'),
                    'shareholder_rights_risk': info.get('shareHolderRightsRisk'),
                    'overall_risk': info.get('overallRisk'),

                    # Governance and IR
                    'governance_epoch_date': info.get('governanceEpochDate'),
                    'compensation_as_of_epoch_date': info.get('compensationAsOfEpochDate'),
                    'ir_website': info.get('irWebsite'),

                    # Market data
                    'max_age': info.get('maxAge'),
                    'price_hint': info.get('priceHint'),
                    'previous_close': info.get('previousClose'),
                    'open_price': info.get('open'),
                    'day_low': info.get('dayLow'),
                    'day_high': info.get('dayHigh'),
                    'regular_market_previous_close': info.get('regularMarketPreviousClose'),
                    'regular_market_open': info.get('regularMarketOpen'),
                    'regular_market_day_low': info.get('regularMarketDayLow'),
                    'regular_market_day_high': info.get('regularMarketDayHigh'),
                    'regular_market_price': info.get('regularMarketPrice'),
                    'regular_market_volume': info.get('regularMarketVolume'),
                    'regular_market_change': info.get('regularMarketChange'),
                    'regular_market_change_percent': info.get('regularMarketChangePercent'),
                    'regular_market_time': info.get('regularMarketTime'),

                    # Pre/Post market
                    'pre_market_price': info.get('preMarketPrice'),
                    'pre_market_change': info.get('preMarketChange'),
                    'pre_market_change_percent': info.get('preMarketChangePercent'),
                    'post_market_time': info.get('postMarketTime'),

                    # Dividend information
                    'dividend_rate': info.get('dividendRate'),
                    'dividend_yield': info.get('dividendYield'),
                    'ex_dividend_date': info.get('exDividendDate'),
                    'dividend_date': info.get('dividendDate'),
                    'payout_ratio': info.get('payoutRatio'),
                    'five_year_avg_dividend_yield': info.get('fiveYearAvgDividendYield'),
                    'trailing_annual_dividend_rate': info.get('trailingAnnualDividendRate'),
                    'trailing_annual_dividend_yield': info.get('trailingAnnualDividendYield'),
                    'last_dividend_value': info.get('lastDividendValue'),
                    'last_dividend_date': info.get('lastDividendDate'),

                    # Trading metrics
                    'beta': info.get('beta'),
                    'volume': info.get('volume'),
                    'average_volume': info.get('averageVolume'),
                    'average_daily_volume_10_day': info.get('averageDailyVolume10Day'),
                    'bid': info.get('bid'),
                    'ask': info.get('ask'),
                    'bid_size': info.get('bidSize'),
                    'ask_size': info.get('askSize'),

                    # Market cap and shares
                    'market_cap': info.get('marketCap'),
                    'enterprise_value': info.get('enterpriseValue'),
                    'shares_outstanding': info.get('sharesOutstanding'),
                    'float_shares': info.get('floatShares'),
                    'implied_shares_outstanding': info.get('impliedSharesOutstanding'),

                    # Price metrics
                    'fifty_two_week_low': info.get('fiftyTwoWeekLow'),
                    'fifty_two_week_high': info.get('fiftyTwoWeekHigh'),
                    'fifty_two_week_low_change': info.get('fiftyTwoWeekLowChange'),
                    'fifty_two_week_low_change_percent': info.get('fiftyTwoWeekLowChangePercent'),
                    'fifty_two_week_range': info.get('fiftyTwoWeekRange'),
                    'fifty_two_week_high_change': info.get('fiftyTwoWeekHighChange'),
                    'fifty_two_week_high_change_percent': info.get('fiftyTwoWeekHighChangePercent'),
                    'fifty_two_week_change_percent': info.get('fiftyTwoWeekChangePercent'),
                    'fifty_day_average': info.get('fiftyDayAverage'),
                    'fifty_day_average_change': info.get('fiftyDayAverageChange'),
                    'two_hundred_day_average': info.get('twoHundredDayAverage'),
                    'two_hundred_day_average_change': info.get('twoHundredDayAverageChange'),

                    # Financial ratios
                    'trailing_pe': info.get('trailingPE'),
                    'forward_pe': info.get('forwardPE'),
                    'price_to_book': info.get('priceToBook'),
                    'price_to_sales_trailing_12_months': info.get('priceToSalesTrailing12Months'),
                    'price_eps_current_year': info.get('priceEpsCurrentYear'),

                    # Short interest
                    'shares_short': info.get('sharesShort'),
                    'short_ratio': info.get('shortRatio'),
                    'shares_short_prior_month': info.get('sharesShortPriorMonth'),
                    'shares_short_previous_month_date': info.get('sharesShortPreviousMonthDate'),
                    'date_short_interest': info.get('dateShortInterest'),
                    'shares_percent_shares_out': info.get('sharesPercentSharesOut'),
                    'held_percent_insiders': info.get('heldPercentInsiders'),
                    'held_percent_institutions': info.get('heldPercentInstitutions'),
                    'short_percent_of_float': info.get('shortPercentOfFloat'),

                    # Financial metrics
                    'book_value': info.get('bookValue'),
                    'earnings_quarterly_growth': info.get('earningsQuarterlyGrowth'),
                    'net_income_to_common': info.get('netIncomeToCommon'),
                    'trailing_eps': info.get('trailingEps'),
                    'forward_eps': info.get('forwardEps'),
                    'eps_trailing_twelve_months': info.get('epsTrailingTwelveMonths'),
                    'eps_forward': info.get('epsForward'),
                    'eps_current_year': info.get('epsCurrentYear'),
                    'enterprise_to_revenue': info.get('enterpriseToRevenue'),
                    'enterprise_to_ebitda': info.get('enterpriseToEbitda'),

                     # Financial statements
                    'total_cash': info.get('totalCash'),
                    'total_debt': info.get('totalDebt'),
                    'quick_ratio': info.get('quickRatio'),
                    'current_ratio': info.get('currentRatio'),
                    'total_revenue': info.get('totalRevenue'),
                    'debt_to_equity': info.get('debtToEquity'),
                    'revenue_per_share': info.get('revenuePerShare'),
                    'return_on_assets': info.get('returnOnAssets'),
                    'return_on_equity': info.get('returnOnEquity'),
                    'gross_profits': info.get('grossProfits'),
                    'free_cashflow': info.get('freeCashflow'),
                    'operating_cashflow': info.get('operatingCashflow'),
                    'earnings_growth': info.get('earningsGrowth'),
                    'revenue_growth': info.get('revenueGrowth'),
                    'gross_margins': info.get('grossMargins'),
                    'ebitda_margins': info.get('ebitdaMargins'),
                    'operating_margins': info.get('operatingMargins'),
                    'ebitda': info.get('ebitda'),
                    'total_cash_per_share': info.get('totalCashPerShare'),

                    # Exchange information
                    'exchange': info.get('exchange', 'HKSE'),
                    'full_exchange_name': info.get('fullExchangeName'),
                    'exchange_timezone_name': info.get('exchangeTimezoneName'),
                    'exchange_timezone_short_name': info.get('exchangeTimezoneShortName'),
                    'gmt_offset_milliseconds': info.get('gmtOffSetMilliseconds'),

                    # Market analysis
                    'current_price': info.get('currentPrice'),
                    'target_high_price': info.get('targetHighPrice'),
                    'target_low_price': info.get('targetLowPrice'),
                    'target_mean_price': info.get('targetMeanPrice'),
                    'recommendation_mean': info.get('recommendationMean'),
                    'recommendation_key': info.get('recommendationKey'),
                    'number_of_analyst_opinions': info.get('numberOfAnalystOpinions'),
                    'average_analyst_rating': info.get('averageAnalystRating'),

                    # Trading status
                    'market_state': info.get('marketState'),
                    'quote_source_name': info.get('quoteSourceName'),
                    'tradeable': info.get('tradeable'),
                    'crypto_tradeable': info.get('cryptoTradeable'),
                    'currency': info.get('currency'),
                    'exchange_data_delayed_by': info.get('exchangeDataDelayedBy'),

                    # Other fields
                    'executive_team': json.dumps(info.get('executiveTeam')) if info.get('executiveTeam') is not None else None,
                    'quote_type': info.get('quoteType'),
                    'language': info.get('language'),
                    'region': info.get('region'),
                    'market': info.get('market'),
                    'type_disp': info.get('typeDisp'),
                    'source_interval': info.get('sourceInterval'),
                    'message_board_id': info.get('messageBoardId'),
                    'custom_price_alert_confidence': info.get('customPriceAlertConfidence'),
                    'corporate_actions': info.get('corporateActions'),
                    'financial_currency': info.get('financialCurrency'),

                    # Timestamps
                    'last_fiscal_year_end': info.get('lastFiscalYearEnd'),
                    'next_fiscal_year_end': info.get('nextFiscalYearEnd'),
                    'most_recent_quarter': info.get('mostRecentQuarter'),
                    'last_split_date': info.get('lastSplitDate'),
                    'last_split_factor': info.get('lastSplitFactor'),
                    'first_trade_date_milliseconds': info.get('firstTradeDateMilliseconds'),
                    'earnings_timestamp': info.get('earningsTimestamp'),
                    'earnings_timestamp_start': info.get('earningsTimestampStart'),
                    'earnings_timestamp_end': info.get('earningsTimestampEnd'),
                    'earnings_call_timestamp_start': info.get('earningsCallTimestampStart'),
                    'earnings_call_timestamp_end': info.get('earningsCallTimestampEnd'),
                    'is_earnings_date_estimate': info.get('isEarningsDateEstimate'),
                    'esg_populated': info.get('esgPopulated'),
                    'has_pre_post_market_data': info.get('hasPrePostMarketData'),
                }.items()}

                if existing_stock and not self.full_refresh:
                    logger.debug(f"Stock {ticker_symbol} already exists, updating")
                    # Update existing stock with all fields
                    for key, value in stock_data.items():
                        setattr(existing_stock, key, value)
                else:
                    logger.debug(f"Creating new stock record for {ticker_symbol}")
                    # Create new stock
                    stock = StockHK(**stock_data)
                    session.add(stock)

                session.commit()
                logger.debug(f"Successfully stored basic info for ticker: {ticker_symbol}")
        except SQLAlchemyError as e:
            logger.error(f"Database error storing stock info for {ticker_symbol}: {str(e)}")
            raise

    def _store_price_history(self, ticker_symbol: str, ticker: yf.Ticker):
        """Store price history in the database."""
        logger.debug(f"Storing price history for ticker: {ticker_symbol}")

        try:
            # Get price history for the last N days
            end_date = datetime.datetime.now()
            start_date = end_date - datetime.timedelta(days=self.price_days)

            # Fetch history with retry logic
            try:
                # Convert ticker symbol for Yahoo Finance if needed
                yf_ticker_symbol = convert_hk_ticker_to_yf(ticker_symbol)

                history = self.yf_wrapper.get_ticker_history(
                    yf_ticker_symbol,
                    start=start_date,
                    end=end_date
                )
            except Exception as e:
                logger.error(f"Error fetching history for {ticker_symbol}: {str(e)}")
                # Fallback to direct ticker.history if wrapper fails
                history = ticker.history(start=start_date, end=end_date)

            # Skip if no history available
            if history.empty:
                logger.warning(f"No price history available for ticker {ticker_symbol}")
                return

            with get_db_session() as session:
                # Process each day
                for date, row in history.iterrows():
                    # Convert date to datetime.date
                    date_only = date.date()

                    # Handle NaN values by converting them to None
                    price_data = {
                        'symbol': ticker_symbol,
                        'date': date_only,
                        'open': float(row['Open']) if pd.notna(row.get('Open')) else None,
                        'high': float(row['High']) if pd.notna(row.get('High')) else None,
                        'low': float(row['Low']) if pd.notna(row.get('Low')) else None,
                        'close': float(row['Close']) if pd.notna(row.get('Close')) else None,
                        'volume': int(row['Volume']) if pd.notna(row.get('Volume')) else None
                    }

                    # Check if price already exists for this date
                    existing_price = session.query(StockHKPrice).filter(
                        StockHKPrice.symbol == ticker_symbol,
                        StockHKPrice.date == date_only
                    ).first()

                    if existing_price and not self.full_refresh:
                        logger.debug(f"Price for {ticker_symbol} on {date_only} already exists, updating")
                        # Update existing price
                        for key, value in price_data.items():
                            if key not in ('symbol', 'date'):  # Skip primary key fields
                                setattr(existing_price, key, value)
                    else:
                        logger.debug(f"Creating new price record for {ticker_symbol} on {date_only}")
                        # Create new price record
                        price = StockHKPrice(**price_data)
                        session.add(price)

                session.commit()
                logger.debug(f"Successfully stored price history for ticker: {ticker_symbol}")
        except SQLAlchemyError as e:
            logger.error(f"Database error storing price history for {ticker_symbol}: {str(e)}")
            raise

    def _clean_numeric_value(self, value: Any) -> Optional[float]:
        """
        Clean numeric values by handling infinity, NaN and converting to proper type.

        Args:
            value: Value to clean

        Returns:
            Optional[float]: Cleaned value or None if invalid
        """
        if value is None:
            return None
        try:
            float_val = float(value)
            if float_val in (float('inf'), float('-inf')) or pd.isna(float_val):
                return None
            return float_val
        except (ValueError, TypeError):
            return None

    def _store_fundamental_data(self, ticker_symbol: str, info: Dict[str, Any]):
        """Store fundamental data in the database."""
        logger.debug(f"Storing fundamental data for ticker: {ticker_symbol}")

        try:
            with get_db_session() as session:
                today = datetime.date.today()

                existing_fundamental = session.query(StockHKFundamental).filter(
                    StockHKFundamental.symbol == ticker_symbol,
                    StockHKFundamental.date == today
                ).first()

                if existing_fundamental and not self.full_refresh:
                    logger.debug(f"Fundamental data for {ticker_symbol} on {today} already exists, updating")

                    existing_fundamental.dividend_rate = self._clean_numeric_value(info.get('dividendRate'))
                    existing_fundamental.dividend_yield = self._clean_numeric_value(info.get('dividendYield', 0) * 100 if info.get('dividendYield') else None)
                    existing_fundamental.pe_ratio = self._clean_numeric_value(info.get('trailingPE'))
                    existing_fundamental.eps = self._clean_numeric_value(info.get('trailingEps'))
                    existing_fundamental.beta = self._clean_numeric_value(info.get('beta'))
                    existing_fundamental.fifty_two_week_high = self._clean_numeric_value(info.get('fiftyTwoWeekHigh'))
                    existing_fundamental.fifty_two_week_low = self._clean_numeric_value(info.get('fiftyTwoWeekLow'))
                else:
                    logger.debug(f"Creating new fundamental data record for {ticker_symbol} on {today}")

                    fundamental = StockHKFundamental(
                        symbol=ticker_symbol,
                        date=today,
                        dividend_rate=self._clean_numeric_value(info.get('dividendRate')),
                        dividend_yield=self._clean_numeric_value(info.get('dividendYield', 0) * 100 if info.get('dividendYield') else None),
                        pe_ratio=self._clean_numeric_value(info.get('trailingPE')),
                        eps=self._clean_numeric_value(info.get('trailingEps')),
                        beta=self._clean_numeric_value(info.get('beta')),
                        fifty_two_week_high=self._clean_numeric_value(info.get('fiftyTwoWeekHigh')),
                        fifty_two_week_low=self._clean_numeric_value(info.get('fiftyTwoWeekLow'))
                    )
                    session.add(fundamental)

                session.commit()
                logger.debug(f"Successfully stored fundamental data for ticker: {ticker_symbol}")
        except SQLAlchemyError as e:
            logger.error(f"Database error storing fundamental data for {ticker_symbol}: {str(e)}")
            raise

def parse_args():
    """
    Parse command line arguments.

    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description='Fetch Hong Kong stock market data and store it in MySQL database')
    parser.add_argument('--full-refresh', action='store_true', help='Force refresh of all data')
    parser.add_argument('--tickers', type=str, default=settings.JOB_SPECIFIC_TICKERS, help='Comma-separated list of specific tickers to process')
    parser.add_argument('--batch-size', type=int, default=settings.JOB_BATCH_SIZE, help='Number of stocks to process in each batch')
    parser.add_argument('--delay', type=float, default=settings.JOB_DELAY, help='Delay between batches in seconds')
    parser.add_argument('--price-days', type=int, default=settings.JOB_PRICE_DAYS, help='Number of days of price history to fetch')
    parser.add_argument('--max-retries', type=int, default=5, help='Maximum number of retry attempts for rate limiting')
    parser.add_argument('--min-wait', type=float, default=60.0, help='Minimum wait time between retries in seconds')
    parser.add_argument('--max-wait', type=float, default=120.0, help='Maximum wait time between retries in seconds')

    return parser.parse_args()

def main():
    """
    Main entry point.
    """
    args = parse_args()

    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)

    # Parse tickers if provided
    specific_tickers = args.tickers.split(',') if args.tickers else None

    # Create and run the job
    job = StockHKDataJob(
        full_refresh=args.full_refresh,
        specific_tickers=specific_tickers,
        batch_size=args.batch_size,
        delay=args.delay,
        price_days=args.price_days,
        max_retries=args.max_retries,
        min_wait=args.min_wait,
        max_wait=args.max_wait
    )

    success = job.run()

    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
