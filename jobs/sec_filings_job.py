#!/usr/bin/env python3
"""
Job to fetch SEC filings data for US listed stocks and store it in MySQL database.
"""

import os
import sys
import time
import logging
import argparse
import datetime
import traceback
import re
import json
import concurrent.futures
from typing import List, Dict, Any, Optional, Set, Tuple
import requests
from bs4 import BeautifulSoup
from sqlalchemy.exc import SQLAlchemyError
from ratelimit import limits, sleep_and_retry
from sec_cik_mapper import StockMapper
from pathlib import Path
import html2text  # Import html2text at the module level
import pdfkit  # Add this import (requires `pip install pdfkit` and `wkhtmltopdf` installed)
from openai import OpenAI

from jobs.sec_company_facts_job import fetch_all_ciks, get_all_tickers, process_single_ticker

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.db.mysql_client import MySQLClient, get_db_session
from jobs.db.schema import Base, Stock, SECFiling, SECFilingSection, JobRun, SP500Ticker, Nasdaq100Ticker, NasdaqCompositeTicker, DowJonesTicker, Russell2000Ticker
from jobs.db.migrations.migrations import apply_migrations
from utils.logging_utils import logger
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('sec_filings_job.log')
    ]
)

# SEC API constants
SEC_ARCHIVE_URL = "https://www.sec.gov/Archives/edgar/data"
SEC_SEARCH_URL = "http://www.sec.gov/cgi-bin/browse-edgar"
SEC_SUBMISSIONS_URL = "https://data.sec.gov/submissions"

# Database tables related to SEC data:
# - sec_filings: Stores metadata about SEC filings (10-K, 10-Q, etc.) including filing date, type, and URL
# - sec_filing_sections: Stores the content of specific sections extracted from SEC filings (Risk Factors, MD&A, etc.)
# - sec_company_facts: Stores structured financial data points extracted from company filings (revenue, assets, etc.)
# - sec_company_fact_metadata: Stores metadata about the financial data points (units, taxonomy info, etc.)

# Define section names
SECTION_MAPPING = {
    "RISK_FACTORS": ["Risk Factors", "Item 1A", "Item 1A.", "ITEM 1A"],
    "MD_AND_A": ["Management's Discussion and Analysis", "Item 7", "Item 7.", "ITEM 7", "Management's Discussion"],
    "BUSINESS": ["Business", "Item 1", "Item 1.", "ITEM 1"],
    "FINANCIAL_STATEMENTS": ["Financial Statements", "Item 8", "Item 8.", "ITEM 8"],
    "LEGAL_PROCEEDINGS": ["Legal Proceedings", "Item 3", "Item 3.", "ITEM 3"],
    "PROPERTIES": ["Properties", "Item 2", "Item 2.", "ITEM 2"]
}

class SECFilingsJob:
    """
    Job to fetch SEC filings data for US listed stocks and store it in MySQL database.
    """
    
    def __init__(
        self,
        full_refresh: bool = False,
        specific_tickers: Optional[List[str]] = None,
        batch_size: int = 5,
        delay: float = 2.0,
        years: int = 5,
        max_retries: int = 5,
        min_wait: float = 2.0,
        max_wait: float = 60.0
    ):
        """
        Initialize the job.
        
        Args:
            full_refresh: Whether to refresh all data
            specific_tickers: List of specific tickers to process
            batch_size: Number of stocks to process in each batch
            delay: Delay between batches in seconds
            years: Number of years of filings to fetch
            max_retries: Maximum number of retry attempts for rate limiting
            min_wait: Minimum wait time between retries in seconds
            max_wait: Maximum wait time between retries in seconds
        """
        self.full_refresh = full_refresh
        self.specific_tickers = specific_tickers
        self.batch_size = batch_size
        self.delay = delay
        self.years = years
        self.max_retries = max_retries
        self.min_wait = min_wait
        self.max_wait = max_wait
        self.job_run_id = None
        self.processed_count = 0
        self.error_count = 0
        self.batch_counter = 0
        
        # SEC API settings
        self.sec_organization = settings.SEC_API_ORGANIZATION
        self.sec_email = settings.SEC_API_EMAIL
        self.sec_filing_types = settings.SEC_FILING_TYPES
        self.sec_filing_sections = settings.SEC_FILING_SECTIONS
        
        # Initialize database
        self._init_database()
        
        # Initialize index tickers
        with get_db_session() as session:
            self.sp500_tickers = set(ticker[0] for ticker in session.query(SP500Ticker.ticker).all())
            self.nasdaq100_tickers = set(ticker[0] for ticker in session.query(Nasdaq100Ticker.ticker).all())
            self.nasdaq_composite_tickers = set(ticker[0] for ticker in session.query(NasdaqCompositeTicker.ticker).all())
            self.dow_tickers = set(ticker[0] for ticker in session.query(DowJonesTicker.ticker).all())
            self.russell2000_tickers = set(ticker[0] for ticker in session.query(Russell2000Ticker.ticker).all())

        # Initialize OpenAI API
        if settings and hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
            self.openai_client = OpenAI(api_key=settings.OPENAI_API_KEY)
            self.enable_openai = True
            logger.info("OpenAI API key configured. Html upload will be enabled.")
        else:
            self.enable_openai = False
            logger.info("OpenAI API key not configured. Html upload will be disabled.")
    
    def _init_database(self):
        """
        Initialize the database.
        """
        logger.info("Initializing database")
        try:
            # Apply migrations instead of creating tables directly
            apply_migrations()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise
    
    def run(self):
        """
        Run the job.
        """
        logger.info("Starting SEC filings job")
        start_time = time.time()
        
        try:
            # Create job run record and store its ID
            with get_db_session() as session:
                job_run = JobRun.create_job_run(session, "sec_filings_job")
                session.flush()  # Ensure we have the ID
                self.job_run_id = job_run.id
                session.commit()
                logger.info(f"Created job run record with ID: {self.job_run_id}")
            
            # Get list of tickers
            if self.specific_tickers:
                logger.info(f"Using specific tickers from args: {self.specific_tickers}")
                tickers = self.specific_tickers
            else:
                with get_db_session() as session:
                    tickers = get_all_tickers(session)
                logger.info(f"Found {len(tickers)} tickers to process")
            
            # Process tickers in batches
            self._process_tickers(tickers)
            
            # Mark job as completed using a fresh session
            with get_db_session() as session:
                job_run = session.query(JobRun).get(self.job_run_id)
                job_run.complete(session, self.processed_count)
            
            end_time = time.time()
            logger.info(f"Job completed successfully in {end_time - start_time:.2f} seconds")
            logger.info(f"Processed {self.processed_count} stocks with {self.error_count} errors")
            
            return True
        except Exception as e:
            logger.error(f"Error running job: {str(e)}")
            logger.error(traceback.format_exc())
            
            # Mark job as failed using a fresh session
            if self.job_run_id:
                with get_db_session() as session:
                    job_run = session.query(JobRun).get(self.job_run_id)
                    if job_run:
                        job_run.fail(session, str(e), self.processed_count)
            
            return False
    
    def _process_tickers(self, tickers: List[str]):
        """
        Process tickers concurrently using ThreadPoolExecutor.
        
        Args:
            tickers: List of tickers to process
        """
        logger.info(f"Processing {len(tickers)} tickers concurrently with max workers: {min(10, self.batch_size * 2)}")
        
        # Initialize counters for tracking progress
        processed = 0
        total = len(tickers)
        
        # Process tickers in batches but concurrently within each batch
        for i in range(0, len(tickers), self.batch_size):
            batch = tickers[i:i+self.batch_size]
            logger.info(f"Processing batch {i//self.batch_size + 1}/{(len(tickers) + self.batch_size - 1)//self.batch_size}: {batch}")
            
            # Use a ThreadPoolExecutor to process tickers concurrently
            with concurrent.futures.ThreadPoolExecutor(max_workers=min(len(batch), 20)) as executor:
                # Submit all tickers in the batch to the executor
                future_to_ticker = {executor.submit(self._process_ticker, ticker): ticker for ticker in batch}
                
                # Process results as they complete
                for future in concurrent.futures.as_completed(future_to_ticker):
                    ticker = future_to_ticker[future]
                    try:
                        # Get the result (will re-raise any exception from the thread)
                        future.result()
                        self.processed_count += 1
                        processed += 1
                        logger.info(f"Progress: {processed}/{total} tickers processed")
                    except Exception as e:
                        logger.error(f"Error processing ticker {ticker}: {str(e)}")
                        logger.error(traceback.format_exc())
                        self.error_count += 1
            
            self.batch_counter += 1
            
            # Add delay between batches to avoid rate limiting
            if i + self.batch_size < len(tickers):
                logger.debug(f"Sleeping for {self.delay} seconds between batches")
                time.sleep(self.delay)
    
    def _process_batch(self, batch: List[str]):
        """
        Process a batch of tickers.
        
        Args:
            batch: List of tickers to process
        """
        # Process each ticker in the batch
        for ticker in batch:
            try:
                self._process_ticker(ticker)
                self.processed_count += 1
            except Exception as e:
                logger.error(f"Error processing ticker {ticker}: {str(e)}")
                logger.error(traceback.format_exc())
                self.error_count += 1
    
    def _process_ticker(self, ticker: str):
        """
        Process a single ticker.
        
        Args:
            ticker: Ticker symbol
        """
        logger.info(f"Processing ticker: {ticker}")
        
        try:
            # Get CIK number for the ticker
            cik = self._get_cik_by_ticker(ticker)
            if not cik:
                logger.warning(f"Could not find CIK for ticker {ticker}")
                return
            
            logger.info(f"Found CIK {cik} for ticker {ticker}")
            
            # Get filings for the ticker
            filings = self._get_filings_by_cik(cik)
            if not filings:
                logger.warning(f"No filings found for ticker {ticker} (CIK: {cik})")
                return
            
            logger.info(f"Found {len(filings)} filings for ticker {ticker}")
            
            # Process each filing
            for filing in filings:
                try:
                    self._process_filing(ticker, cik, filing)
                except Exception as e:
                    logger.error(f"Error processing filing for ticker {ticker}: {str(e)}")
                    logger.error(traceback.format_exc())
            
            logger.info(f"Successfully processed ticker: {ticker}")
        except Exception as e:
            logger.error(f"Error processing ticker {ticker}: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    
    def _process_filing(self, ticker: str, cik: str, filing: Dict[str, Any]):
        """
        Process a single filing.
        
        Args:
            ticker: Ticker symbol
            cik: CIK number
            filing: Filing data
        """
        accession_number = filing['accessionNumber']
        filing_type = filing['form']
        filing_date = filing['filingDate']
        report_date = filing.get('reportDate', filing_date)
        index_url = filing.get('index_url')
        html_url = filing.get('html_url')
        txt_url = filing.get('txt_url')
        
        logger.info(f"Processing filing: {ticker} - {filing_type} - {filing_date}")
        
        # Check if filing already exists in database
        with get_db_session() as session:
            existing_filing = session.query(SECFiling).filter(
                SECFiling.ticker == ticker,
                SECFiling.accession_number == accession_number
            ).first()
            
            if existing_filing and not self.full_refresh:
                if existing_filing.is_processed:
                    logger.info(f"Filing {ticker} - {filing_type} - {filing_date} already processed, skipping")
                    return
                if filing_type == '20-F':
                    fiscal_year = datetime.datetime.strptime(report_date, '%Y-%m-%d').year
                    existing_filing.fiscal_year = fiscal_year
                    existing_filing.is_processed = True
                    session.commit()
                    logger.info(f"Filing {existing_filing.id} {existing_filing.ticker} - {filing_type} upadted fiscal year")
            else:
                # Create new filing record
                logger.info(f"Creating new filing record for {ticker} - {filing_type} - {filing_date}")
                
                # Extract fiscal year and quarter
                fiscal_year = None
                fiscal_quarter = None
                if filing_type == '10-K':
                    fiscal_year = datetime.datetime.strptime(report_date, '%Y-%m-%d').year
                elif filing_type == '10-Q':
                    report_date_obj = datetime.datetime.strptime(report_date, '%Y-%m-%d')
                    fiscal_year = report_date_obj.year
                    # Approximate fiscal quarter based on report date
                    month = report_date_obj.month
                    if month <= 3:
                        fiscal_quarter = 1
                    elif month <= 6:
                        fiscal_quarter = 2
                    elif month <= 9:
                        fiscal_quarter = 3
                    else:
                        fiscal_quarter = 4
                elif filing_type == '20-F':
                    fiscal_year = datetime.datetime.strptime(report_date, '%Y-%m-%d').year
                elif filing_type == '8-K':
                    # Only check recent 8-K filings (within the past week)
                    # filing_date_obj = datetime.datetime.strptime(filing_date, '%Y-%m-%d')
                    # one_week_ago = datetime.datetime.now() - datetime.timedelta(days=7)
                    
                    # financial = False
                    # if filing_date_obj >= one_week_ago:
                    #     financial = self.gpt4o_search_contains_financial_data(txt_url)
                    # if financial:
                    #     report_date_obj = datetime.datetime.strptime(report_date, '%Y-%m-%d')
                    #     fiscal_year = report_date_obj.year
                    #     # Approximate fiscal quarter based on report date
                    #     month = report_date_obj.month
                    #     if month <= 3:
                    #         fiscal_year -= 1
                    #         fiscal_quarter = 4
                    #     elif month <= 6:
                    #         fiscal_quarter = 1
                    #     elif month <= 9:
                    #         fiscal_quarter = 2
                    #     else:
                    #         fiscal_quarter = 3
                    pass


                openai_file_id = None
                # if html_url:
                #     pdf_path = self._download_html_as_pdf(html_url, ticker, accession_number, filing_type)
                #     if not self.enable_openai:
                #         logger.warning("OpenAI integration not enabled. Cannot upload file.")
                #     elif pdf_path:
                #         openai_file_id = self._upload_pdf_to_openai(pdf_path)
                
                # Create new filing record
                new_filing = SECFiling(
                    ticker=ticker,
                    cik=cik,
                    accession_number=accession_number,
                    filing_type=filing_type,
                    filing_date=datetime.datetime.strptime(filing_date, '%Y-%m-%d').date(),
                    report_date=datetime.datetime.strptime(report_date, '%Y-%m-%d').date() if report_date else None,
                    fiscal_year=fiscal_year,
                    fiscal_quarter=fiscal_quarter,
                    url=txt_url,
                    html_url=html_url,
                    index_url=index_url,
                    is_processed=True,
                    openai_file_id=openai_file_id  # Save file_id to db
                )
                
                session.add(new_filing)
                session.commit()
                
                existing_filing = new_filing

                if filing_type == '10-K' or filing_type == '20-F':
                    try:
                        process_single_ticker(ticker)
                        logger.info(f"Process ticker {ticker} company facts")
                    except Exception as e:
                        logger.error(f"Error processing ticker {ticker} company facts: {str(e)}")
                        logger.error(traceback.format_exc())
            
    def _download_html_as_pdf(self, html_url: str, ticker: str, accession_number: str, filing_type: str) -> Optional[str]:
        """
        Download HTML from html_url and save as PDF in /tmp/yyyy-mm-dd directory.
        Returns the PDF file path if successful, else None.
        """
        import os
        import datetime

        today_str = datetime.datetime.now().strftime("%Y-%m-%d")
        out_dir = f"/tmp/{today_str}"
        os.makedirs(out_dir, exist_ok=True)

        # Download HTML content (streaming for large files)
        headers = {
            "User-Agent": f"{self.sec_organization} {self.sec_email}",
        }
        response = requests.get(html_url, headers=headers, stream=True)
        response.raise_for_status()
        html_bytes = b""
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                html_bytes += chunk
        html_content = html_bytes.decode(response.encoding or "utf-8", errors="replace")

        # Prepare PDF filename
        safe_ticker = ticker.replace("/", "_")
        safe_accession = accession_number.replace("/", "_")
        safe_type = filing_type.replace("/", "_")
        pdf_filename = f"{safe_ticker}_{safe_accession}_{safe_type}.pdf"
        pdf_path = os.path.join(out_dir, pdf_filename)

        # Convert HTML to PDF
        options = {
            "disable-external-links": None,
            "disable-local-file-access": None,
            "no-images": None,
            "load-error-handling": "ignore",
            "load-media-error-handling": "ignore",
        }
        pdfkit.from_string(html_content, pdf_path, options=options)
        logger.info(f"Saved PDF to {pdf_path}")

        # Check file exists and is non-empty
        if os.path.isfile(pdf_path) and os.path.getsize(pdf_path) > 0:
            return pdf_path
        else:
            logger.error(f"PDF file {pdf_path} not created or empty.")
            return None

    def _upload_pdf_to_openai(self, pdf_path: str) -> Optional[str]:
        """
        Uploads the PDF to OpenAI and returns the file_id.
        """
        try:
            with open(pdf_path, "rb") as f:
                response = self.openai_client.files.create(
                    file=f,
                    purpose="assistants"
                )
            file_id = response.id if hasattr(response, "id") else response["id"]
            logger.info(f"Uploaded {pdf_path} to OpenAI. file_id={file_id}")
            return file_id
        except Exception as e:
            logger.error(f"Failed to upload {pdf_path} to OpenAI: {e}")
            return None
    
    @sleep_and_retry
    @limits(calls=10, period=1)
    def _get_cik_by_ticker(self, ticker: str) -> Optional[str]:
        """
        Get CIK number for a ticker.
        
        Args:
            ticker: Ticker symbol
            
        Returns:
            str: CIK number or None if not found
        """
        mapper = StockMapper()
        cik = mapper.ticker_to_cik.get(ticker)
        return cik
        
    @sleep_and_retry
    @limits(calls=10, period=1)
    def _get_filings_by_cik(self, cik: str) -> List[Dict[str, Any]]:
        """
        Get filings for a CIK number.
        
        Args:
            cik: CIK number
            
        Returns:
            List[Dict[str, Any]]: List of filings
        """
        json_name = f"CIK{cik.zfill(10)}.json"
        url = f"{SEC_SUBMISSIONS_URL}/{json_name}"
        
        headers = {
            "User-Agent": f"{self.sec_organization} {self.sec_email}",
            "Content-Type": "application/json",
        }
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            content = json.loads(response.content)
            recent_forms = content["filings"]["recent"]
            
            # Filter filings by type and date
            filings = []
            for i in range(len(recent_forms["accessionNumber"])):
                form = recent_forms["form"][i]
                filing_date = recent_forms["filingDate"][i]
                
                # Check if form type is in the list of filing types to fetch
                if form in self.sec_filing_types:
                    # Check if filing date is within the specified number of years
                    filing_date_obj = datetime.datetime.strptime(filing_date, '%Y-%m-%d')
                    years_ago = datetime.datetime.now() - datetime.timedelta(days=365 * self.years)
                    
                    if filing_date_obj >= years_ago:
                        accession_number = recent_forms["accessionNumber"][i]
                        # primaryDocument = recent_forms["primaryDocument"][i]
                        # if primaryDocument != 'mcd-20250501.htm':
                        #     continue
                        #
                        # filing_date_obj = datetime.datetime.strptime(filing_date, '%Y-%m-%d')
                        # one_week_ago = datetime.datetime.now() - datetime.timedelta(days=7)
                        # if filing_date_obj < one_week_ago:
                        #     continue

                        filings.append({
                            "accessionNumber": accession_number,
                            "form": form,
                            "filingDate": filing_date,
                            "reportDate": recent_forms.get("reportDate", [None] * len(recent_forms["accessionNumber"]))[i],
                            "index_url": self._build_filing_index_url(cik, accession_number),
                            "txt_url": self._build_filing_txt_url(cik, accession_number),
                            "html_url": self._build_filing_html_url(cik, accession_number),
                        })
            
            return filings
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching filings for CIK {cik}: {str(e)}")
            return []
    
    def _build_filing_url(self, cik: str, accession_number: str) -> str:
        """
        Build URL for a filing in TXT format.
        
        Args:
            cik: CIK number
            accession_number: Accession number
            
        Returns:
            str: URL for the filing in TXT format
        """
        # Remove dashes from accession number
        accession_number_no_dashes = accession_number.replace('-', '')
        
        # Add dashes to accession number for filename
        accession_number_with_dashes = f"{accession_number_no_dashes[:10]}-{accession_number_no_dashes[10:12]}-{accession_number_no_dashes[12:]}"
        
        # Build URL
        return f"{SEC_ARCHIVE_URL}/{cik}/{accession_number_no_dashes}/{accession_number_with_dashes}.txt"

    def _build_filing_index_url(self, cik: str, accession_number: str) -> str:
        cik = str(int(cik))
        accession_number_no_dashes = accession_number.replace('-', '')
        return f"{SEC_ARCHIVE_URL}/{cik}/{accession_number_no_dashes}/index.json"
    
    def _build_filing_txt_url(self, cik: str, accession_number: str) -> str:
        cik = str(int(cik))
        accession_number_no_dashes = accession_number.replace('-', '')
        accession_number_with_dashes = f"{accession_number_no_dashes[:10]}-{accession_number_no_dashes[10:12]}-{accession_number_no_dashes[12:]}"
        return f"{SEC_ARCHIVE_URL}/{cik}/{accession_number_no_dashes}/{accession_number_with_dashes}.txt"
    
    def _build_filing_html_url(self, cik: str, accession_number: str) -> str:
        cik = str(int(cik))
        accession_number_no_dashes = accession_number.replace('-', '')
        accession_number_with_dashes = f"{accession_number_no_dashes[:10]}-{accession_number_no_dashes[10:12]}-{accession_number_no_dashes[12:]}"
        return f"{SEC_ARCHIVE_URL}/{cik}/{accession_number_no_dashes}/{accession_number_with_dashes}-index.htm"
    

    def gpt4o_search_contains_financial_data(self, link):
        try:
            from openai import OpenAI

            client = OpenAI(
                api_key=os.getenv('OPENAI_API_KEY'),
                base_url=os.getenv('OPENAI_API_URL')
            )

            prompt = f"""
You are given a URL to an SEC filing text document.

URL: {link}

Please perform the following steps:

1. Download and extract the full text content from the URL.
2. Remove all HTML, XML, or SGML tags from the text to obtain clean, human-readable content.
3. Analyze the cleaned content to determine whether it contains **formal financial statement data**, defined as including sections such as:
   - Consolidated Balance Sheet
   - Consolidated Statements of Operations or Income
   - Consolidated Statements of Cash Flows
   - Consolidated Statements of Shareholders’ Equity
   These sections typically contain structured accounting terms, standardized GAAP/IFRS labels, and numerical tabular data that describe the company’s financial performance.
4. Do not consider exhibits, corporate charters, board resolutions, certifications, legal disclaimers, company bylaws, or corporate governance documents as financial statements.
5. If the document contains financial statements, set `"financial"` to `true`. Otherwise, set it to `false`.
Return the result strictly as a JSON object with the following format: {{"financial": true or false}}

IMPORTANT:
- Output ONLY the JSON object, as plain text.
- Do NOT include code block markers like triple backticks.
- Do NOT use Markdown formatting.
- Do NOT include any explanation, commentary, or surrounding text.
"""
            
            # Create a completion with web search enabled
            completion = client.chat.completions.create(
                model="gpt-4o-search-preview",
                web_search_options={},
                messages=[{
                    "role": "user",
                    "content": prompt
                }],
            )
            
            # Extract search results from the response
            content = completion.choices[0].message.content
            logger.info(f"GPT-4o search response: {content}")

            cleaned_content = re.sub(r"^```(?:json)?\s*|\s*```$", "", content.strip())
            result = json.loads(cleaned_content)
            return result.get("financial", False)
        except Exception as e:
            logger.error(f"Error performing GPT-4o search: {str(e)}")
            return False
        
    
    @sleep_and_retry
    @limits(calls=10, period=1)
    def _build_html_filing_url(self, cik: str, accession_number: str, ticker: str) -> Optional[str]:
        """
        Build URL for a filing in HTML format.
        
        Args:
            cik: CIK number
            accession_number: Accession number
            ticker: Ticker symbol
            
        Returns:
            Optional[str]: URL for the filing in HTML format or None if not found
        """
        # First, try to get the HTML URL from the TXT filing
        try:
            from secedgar.core.rest import _get_lookup_dict
            import requests
            user_agent = f"{self.sec_organization} {self.sec_email}"
            lookup_dict = _get_lookup_dict(lookups=[ticker], user_agent=user_agent)
            for lookup, cik in lookup_dict.items():
                # print(("{0}/CIK{1}.json").format(SEC_SUBMISSIONS_URL, cik.zfill(10)))
                resp = requests.get("{0}/CIK{1}.json".format(SEC_SUBMISSIONS_URL, cik.zfill(10)),
                                    headers={"user-agent": user_agent})
                resp_json = resp.json()

                for i, accessionNumber in enumerate(resp_json['filings']['recent']['accessionNumber']):
                    if accessionNumber == accession_number:
                        accession_number_no_dashes = accession_number.replace('-', '')
                        url = f"{SEC_ARCHIVE_URL}/{resp_json['cik']}/{accession_number_no_dashes}/{resp_json['filings']['recent']['primaryDocument'][i]}"
                        return url
                logger.info(f"Cannot find filing content for CIK {cik}, accession number {accession_number}")
                return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching filing content for CIK {cik}, accession number {accession_number}: {str(e)}")
            return None
    
    @sleep_and_retry
    @limits(calls=10, period=1)
    def _get_filing_content(self, cik: str, accession_number: str) -> str:
        """
        Get content of a filing.
        
        Args:
            cik: CIK number
            accession_number: Accession number
            
        Returns:
            str: Filing content
        """
        url = self._build_filing_url(cik, accession_number)
        
        headers = {
            "User-Agent": f"{self.sec_organization} {self.sec_email}",
            "Content-Type": "text/html",
        }
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            return response.text
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching filing content for CIK {cik}, accession number {accession_number}: {str(e)}")
            raise
    
    def _extract_sections(self, filing_content: str) -> Dict[str, str]:
        """
        Extract sections from filing content.
        
        Args:
            filing_content: Filing content
            
        Returns:
            Dict[str, str]: Dictionary of section name to section text
        """
        sections = {}
        
        # Only extract sections that are in the list of sections to extract
        for section_name in self.sec_filing_sections:
            if section_name not in SECTION_MAPPING:
                logger.warning(f"Unknown section name: {section_name}")
                continue
            
            section_text = self._extract_section(filing_content, section_name)
            if section_text:
                sections[section_name] = section_text
        
        return sections
    
    def _extract_section(self, filing_content: str, section_name: str) -> Optional[str]:
        """
        Extract a section from filing content.
        
        Args:
            filing_content: Filing content
            section_name: Section name
            
        Returns:
            Optional[str]: Section text or None if not found
        """
        if section_name not in SECTION_MAPPING:
            return None
        
        section_patterns = SECTION_MAPPING[section_name]
        
        # Try each pattern
        for pattern in section_patterns:
            # Create regex pattern to match section
            # Look for the section header followed by text until the next section header
            regex_pattern = f"(?i)({pattern}.*?)(?:Item\s+\d|ITEM\s+\d|PART\s+\d|$)"
            
            # Find all matches
            matches = re.findall(regex_pattern, filing_content, re.DOTALL)
            
            if matches:
                # Return the first match with HTML tags removed
                section_text = matches[0].strip()
                return self._strip_html_tags(section_text)
        
        return None
        
    def _strip_html_tags(self, html: str) -> str:
        """
        Remove HTML tags from text.
        
        Args:
            html: Text containing HTML tags
            
        Returns:
            str: Plain text with HTML tags removed
        """
        # Use the imported html2text module
        text = html2text.html2text(html)
        return text.strip()

def parse_args():
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description='Fetch SEC filings data for US listed stocks and store it in MySQL database')
    parser.add_argument('--full-refresh', action='store_true', help='Force refresh of all data')
    parser.add_argument('--tickers', type=str, default=settings.SEC_FILING_TICKERS, help='Comma-separated list of specific tickers to process')
    parser.add_argument('--batch-size', type=int, default=settings.SEC_BATCH_SIZE, help='Number of stocks to process in each batch')
    parser.add_argument('--delay', type=float, default=settings.SEC_DELAY, help='Delay between batches in seconds')
    parser.add_argument('--years', type=int, default=settings.SEC_FILINGS_YEARS, help='Number of years of filings to fetch')
    parser.add_argument('--max-retries', type=int, default=5, help='Maximum number of retry attempts for rate limiting')
    parser.add_argument('--min-wait', type=float, default=2.0, help='Minimum wait time between retries in seconds')
    parser.add_argument('--max-wait', type=float, default=60.0, help='Maximum wait time between retries in seconds')
    
    return parser.parse_args()

def main():
    """
    Main entry point.
    """
    args = parse_args()
    
    # Parse tickers if provided
    specific_tickers = args.tickers.split(',') if args.tickers else None
    
    # Create and run the job
    job = SECFilingsJob(
        full_refresh=args.full_refresh,
        specific_tickers=specific_tickers,
        batch_size=args.batch_size,
        delay=args.delay,
        years=args.years,
        max_retries=args.max_retries,
        min_wait=args.min_wait,
        max_wait=args.max_wait
    )
    
    success = job.run()
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
