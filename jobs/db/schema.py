"""
Database schema for stock data, financial calendar events, SEC filings, and SEC company facts.
"""

import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, Float, Date, DateTime, BigInteger, ForeignKey, UniqueConstraint, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

# Create a base class for SQLAlchemy models
Base = declarative_base()

class Stock(Base):
    """
    Stock model representing basic stock information.
    """
    __tablename__ = "stocks"

    # Primary identifiers
    ticker = Column(String(20), primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    indexes = Column(String(255), nullable=True)

    # Company information
    address1 = Column(Text, nullable=True)
    city = Column(Text, nullable=True)
    state = Column(Text, nullable=True)
    zip = Column(Text, nullable=True)
    country = Column(String(50), nullable=True, index=True)
    phone = Column(Text, nullable=True)
    website = Column(Text, nullable=True)

    # Industry classification
    industry = Column(String(100), nullable=True, index=True)
    industry_key = Column(Text, nullable=True)
    industry_disp = Column(Text, nullable=True)
    sector = Column(String(100), nullable=True, index=True)
    sector_key = Column(Text, nullable=True)
    sector_disp = Column(Text, nullable=True)

    # Company details
    long_business_summary = Column(Text, nullable=True)
    full_time_employees = Column(Text, nullable=True)
    company_officers = Column(Text, nullable=True)

    # Risk metrics
    audit_risk = Column(Text, nullable=True)
    board_risk = Column(Text, nullable=True)
    compensation_risk = Column(Text, nullable=True)
    shareholder_rights_risk = Column(Text, nullable=True)
    overall_risk = Column(Text, nullable=True)

    # Governance and IR
    governance_epoch_date = Column(Text, nullable=True)
    compensation_as_of_epoch_date = Column(Text, nullable=True)
    ir_website = Column(Text, nullable=True)

    # Market data
    max_age = Column(Text, nullable=True)
    price_hint = Column(Text, nullable=True)
    previous_close = Column(Text, nullable=True)
    open_price = Column(Text, nullable=True)
    day_low = Column(Text, nullable=True)
    day_high = Column(Text, nullable=True)
    regular_market_previous_close = Column(Text, nullable=True)
    regular_market_open = Column(Text, nullable=True)
    regular_market_day_low = Column(Text, nullable=True)
    regular_market_day_high = Column(Text, nullable=True)
    regular_market_price = Column(Text, nullable=True)
    regular_market_volume = Column(Text, nullable=True)
    regular_market_change = Column(Text, nullable=True)
    regular_market_change_percent = Column(Text, nullable=True)
    regular_market_time = Column(Text, nullable=True)

    # Pre/Post market
    pre_market_price = Column(Text, nullable=True)
    pre_market_change = Column(Text, nullable=True)
    pre_market_change_percent = Column(Text, nullable=True)
    post_market_time = Column(Text, nullable=True)

    # Dividend information
    dividend_rate = Column(Text, nullable=True)
    dividend_yield = Column(Text, nullable=True)
    ex_dividend_date = Column(Text, nullable=True)
    dividend_date = Column(Text, nullable=True)
    payout_ratio = Column(Text, nullable=True)
    five_year_avg_dividend_yield = Column(Text, nullable=True)
    trailing_annual_dividend_rate = Column(Text, nullable=True)
    trailing_annual_dividend_yield = Column(Text, nullable=True)
    last_dividend_value = Column(Text, nullable=True)
    last_dividend_date = Column(Text, nullable=True)

    # Trading metrics
    beta = Column(Text, nullable=True)
    volume = Column(Text, nullable=True)
    average_volume = Column(Text, nullable=True)
    average_daily_volume_10_day = Column(Text, nullable=True)
    bid = Column(Text, nullable=True)
    ask = Column(Text, nullable=True)
    bid_size = Column(Text, nullable=True)
    ask_size = Column(Text, nullable=True)

    # Market cap and shares
    market_cap = Column(Text, nullable=True)
    enterprise_value = Column(Text, nullable=True)
    shares_outstanding = Column(Text, nullable=True)
    float_shares = Column(Text, nullable=True)
    implied_shares_outstanding = Column(Text, nullable=True)

    # Price metrics
    fifty_two_week_low = Column(Text, nullable=True)
    fifty_two_week_high = Column(Text, nullable=True)
    fifty_two_week_low_change = Column(Text, nullable=True)
    fifty_two_week_low_change_percent = Column(Text, nullable=True)
    fifty_two_week_range = Column(Text, nullable=True)
    fifty_two_week_high_change = Column(Text, nullable=True)
    fifty_two_week_high_change_percent = Column(Text, nullable=True)
    fifty_two_week_change_percent = Column(Text, nullable=True)
    fifty_day_average = Column(Text, nullable=True)
    fifty_day_average_change = Column(Text, nullable=True)
    two_hundred_day_average = Column(Text, nullable=True)
    two_hundred_day_average_change = Column(Text, nullable=True)

    # Financial ratios
    trailing_pe = Column(Text, nullable=True)
    forward_pe = Column(Text, nullable=True)
    price_to_book = Column(Text, nullable=True)
    price_to_sales_trailing_12_months = Column(Text, nullable=True)
    price_eps_current_year = Column(Text, nullable=True)

    # Short interest
    shares_short = Column(Text, nullable=True)
    short_ratio = Column(Text, nullable=True)
    shares_short_prior_month = Column(Text, nullable=True)
    shares_short_previous_month_date = Column(Text, nullable=True)
    date_short_interest = Column(Text, nullable=True)
    shares_percent_shares_out = Column(Text, nullable=True)
    held_percent_insiders = Column(Text, nullable=True)
    held_percent_institutions = Column(Text, nullable=True)
    short_percent_of_float = Column(Text, nullable=True)

    # Financial metrics
    book_value = Column(Text, nullable=True)
    earnings_quarterly_growth = Column(Text, nullable=True)
    net_income_to_common = Column(Text, nullable=True)
    trailing_eps = Column(Text, nullable=True)
    forward_eps = Column(Text, nullable=True)
    eps_trailing_twelve_months = Column(Text, nullable=True)
    eps_forward = Column(Text, nullable=True)
    eps_current_year = Column(Text, nullable=True)
    enterprise_to_revenue = Column(Text, nullable=True)
    enterprise_to_ebitda = Column(Text, nullable=True)

    # Financial statements
    total_cash = Column(Text, nullable=True)
    total_debt = Column(Text, nullable=True)
    quick_ratio = Column(Text, nullable=True)
    current_ratio = Column(Text, nullable=True)
    total_revenue = Column(Text, nullable=True)
    debt_to_equity = Column(Text, nullable=True)
    revenue_per_share = Column(Text, nullable=True)
    return_on_assets = Column(Text, nullable=True)
    return_on_equity = Column(Text, nullable=True)
    gross_profits = Column(Text, nullable=True)
    free_cashflow = Column(Text, nullable=True)
    operating_cashflow = Column(Text, nullable=True)
    earnings_growth = Column(Text, nullable=True)
    revenue_growth = Column(Text, nullable=True)
    gross_margins = Column(Text, nullable=True)
    ebitda_margins = Column(Text, nullable=True)
    operating_margins = Column(Text, nullable=True)
    ebitda = Column(Text, nullable=True)
    total_cash_per_share = Column(Text, nullable=True)

    # Market analysis
    current_price = Column(Text, nullable=True)
    target_high_price = Column(Text, nullable=True)
    target_low_price = Column(Text, nullable=True)
    target_mean_price = Column(Text, nullable=True)
    recommendation_mean = Column(Text, nullable=True)
    recommendation_key = Column(Text, nullable=True)
    number_of_analyst_opinions = Column(Text, nullable=True)
    average_analyst_rating = Column(Text, nullable=True)

    # Exchange information
    exchange = Column(String(50), nullable=True, index=True)
    full_exchange_name = Column(Text, nullable=True)
    exchange_timezone_name = Column(Text, nullable=True)
    exchange_timezone_short_name = Column(Text, nullable=True)
    gmt_offset_milliseconds = Column(Text, nullable=True)

    # Trading status
    market_state = Column(Text, nullable=True)
    quote_source_name = Column(Text, nullable=True)
    tradeable = Column(Text, nullable=True)
    crypto_tradeable = Column(Text, nullable=True)
    currency = Column(Text, nullable=True)
    exchange_data_delayed_by = Column(Text, nullable=True)

    # Other fields
    executive_team = Column(Text, nullable=True)
    quote_type = Column(Text, nullable=True)
    language = Column(Text, nullable=True)
    region = Column(Text, nullable=True)
    market = Column(Text, nullable=True)
    type_disp = Column(Text, nullable=True)
    source_interval = Column(Text, nullable=True)
    message_board_id = Column(Text, nullable=True)
    custom_price_alert_confidence = Column(Text, nullable=True)
    corporate_actions = Column(Text, nullable=True)
    financial_currency = Column(Text, nullable=True)

    # Timestamps
    last_fiscal_year_end = Column(Text, nullable=True)
    next_fiscal_year_end = Column(Text, nullable=True)
    most_recent_quarter = Column(Text, nullable=True)
    last_split_date = Column(Text, nullable=True)
    last_split_factor = Column(Text, nullable=True)
    first_trade_date_milliseconds = Column(Text, nullable=True)
    earnings_timestamp = Column(Text, nullable=True)
    earnings_timestamp_start = Column(Text, nullable=True)
    earnings_timestamp_end = Column(Text, nullable=True)
    earnings_call_timestamp_start = Column(Text, nullable=True)
    earnings_call_timestamp_end = Column(Text, nullable=True)
    is_earnings_date_estimate = Column(Text, nullable=True)
    esg_populated = Column(Text, nullable=True)
    has_pre_post_market_data = Column(Text, nullable=True)

    def __repr__(self):
        return f"<Stock(ticker='{self.ticker}', name='{self.name}')>"

class StockPrice(Base):
    """
    Stock price model representing daily price data.
    """
    __tablename__ = "stock_prices"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    ticker = Column(String(20), ForeignKey("stocks.ticker"), nullable=False, index=True)
    date = Column(Date, nullable=False, index=True)
    open = Column(Float, nullable=True)
    high = Column(Float, nullable=True)
    low = Column(Float, nullable=True)
    close = Column(Float, nullable=True)
    volume = Column(BigInteger, nullable=True)

    # Ensure ticker and date combination is unique
    __table_args__ = (UniqueConstraint('ticker', 'date', name='_ticker_date_uc'),)

    def __repr__(self):
        return f"<StockPrice(ticker='{self.ticker}', date='{self.date}', close={self.close})>"

class StockFundamental(Base):
    """
    Stock fundamental model representing fundamental data.
    """
    __tablename__ = "stock_fundamentals"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    ticker = Column(String(20), ForeignKey("stocks.ticker"), nullable=False, index=True)
    date = Column(Date, nullable=False, index=True)
    dividend_rate = Column(Float, nullable=True)
    dividend_yield = Column(Float, nullable=True)
    pe_ratio = Column(Float, nullable=True)
    eps = Column(Float, nullable=True)
    beta = Column(Float, nullable=True)
    fifty_two_week_high = Column(Float, nullable=True)
    fifty_two_week_low = Column(Float, nullable=True)

    # Ensure ticker and date combination is unique
    __table_args__ = (UniqueConstraint('ticker', 'date', name='_ticker_date_uc'),)

    def __repr__(self):
        return f"<StockFundamental(ticker='{self.ticker}', date='{self.date}')>"

class FinancialCalendarEvent(Base):
    """
    Financial calendar event model representing economic events like CPI, PPI, etc.
    """
    __tablename__ = "financial_calendar_events"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    event_name = Column(String(255), nullable=False, index=True)
    event_type = Column(String(100), nullable=False, index=True)  # e.g., 'CPI', 'PPI', 'NFP'
    country = Column(String(50), nullable=False, index=True)
    date = Column(Date, nullable=False, index=True)
    time = Column(String(50), nullable=True)  # Time of day if available
    date_time = Column(DateTime)  # New field
    actual = Column(String(50), nullable=True)  # Changed from Float to String
    forecast = Column(String(50), nullable=True)  # Changed from Float to String
    previous = Column(String(50), nullable=True)  # Changed from Float to String
    impact = Column(String(20), nullable=True, index=True)  # 'High', 'Medium', 'Low'
    description = Column(Text, nullable=True)
    is_released = Column(Boolean, default=False, nullable=False)
    is_completed = Column(Boolean, default=False, nullable=False) # Job completed or not
    summary = Column(Text, nullable=True)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    retry_count = Column(Integer, default=0, nullable=False)
    is_enabled_notification = Column(Boolean, default=False, nullable=False)
    is_sent_notification = Column(Boolean, default=False, nullable=False)

    # Update unique constraint to use date, country and event_name
    __table_args__ = (UniqueConstraint('date', 'country', 'event_name', name='_date_country_event_name'),)

    def __repr__(self):
        return f"<FinancialCalendarEvent(event_type='{self.event_type}', country='{self.country}', date='{self.date}')>"

class DataSource(Base):
    """
    Data source model for tracking different data providers.
    """
    __tablename__ = "data_sources"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, index=True)  # e.g., 'Alpha Vantage', 'Trading Economics'
    type = Column(String(50), nullable=False, default="API")  # e.g., 'API', 'Database', 'File'
    url = Column(String(255), nullable=True)  # Base URL for the data source
    api_key = Column(String(255), nullable=True)  # API key for accessing the data source
    priority = Column(Integer, nullable=False, default=1)  # Lower number = higher priority
    is_active = Column(Boolean, default=True, nullable=False)
    last_checked = Column(DateTime, nullable=True)
    last_used = Column(DateTime, nullable=True)
    error_count = Column(Integer, default=0, nullable=False)

    def __repr__(self):
        return f"<DataSource(id={self.id}, name='{self.name}', priority={self.priority})>"

class EventMonitoringStatus(Base):
    """
    Tracks the monitoring status of specific economic events.
    """
    __tablename__ = "event_monitoring_status"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    event_id = Column(BigInteger, ForeignKey("financial_calendar_events.id"), nullable=False, index=True)
    monitoring_start = Column(DateTime, nullable=False, default=func.now())
    monitoring_end = Column(DateTime, nullable=True)
    status = Column(String(20), nullable=False, index=True)  # 'pending', 'monitoring', 'completed'
    last_checked = Column(DateTime, nullable=True)
    next_check = Column(DateTime, nullable=True)
    check_frequency = Column(Integer, nullable=False, default=300)  # Seconds between checks

    def __repr__(self):
        return f"<EventMonitoringStatus(event_id={self.event_id}, status='{self.status}')>"

class JobRun(Base):
    """
    Job run model for tracking job execution.
    """
    __tablename__ = "job_runs"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    job_name = Column(String(100), nullable=False, index=True)
    start_time = Column(DateTime, nullable=False, default=func.now())
    end_time = Column(DateTime, nullable=True)
    status = Column(String(20), nullable=False, index=True)  # 'running', 'completed', 'failed'
    records_processed = Column(Integer, nullable=True)
    error_message = Column(Text, nullable=True)

    def __repr__(self):
        return f"<JobRun(id={self.id}, job_name='{self.job_name}', status='{self.status}')>"

    @classmethod
    def create_job_run(cls, session, job_name: str) -> "JobRun":
        """
        Create a new job run record.

        Args:
            session: SQLAlchemy session
            job_name: Name of the job

        Returns:
            JobRun: The created job run record
        """
        job_run = cls(
            job_name=job_name,
            status="running"
        )
        session.add(job_run)
        session.commit()
        return job_run

    def complete(self, session, records_processed: Optional[int] = None):
        """
        Mark the job run as completed.

        Args:
            session: SQLAlchemy session
            records_processed: Number of records processed
        """
        self.status = "completed"
        self.end_time = datetime.datetime.now()
        self.records_processed = records_processed
        session.commit()

    def fail(self, session, error_message: str, records_processed: Optional[int] = None):
        """
        Mark the job run as failed.

        Args:
            session: SQLAlchemy session
            error_message: Error message
            records_processed: Number of records processed
        """
        self.status = "failed"
        self.end_time = datetime.datetime.now()
        self.error_message = error_message
        self.records_processed = records_processed
        session.commit()

class DelistTicker(Base):
    __tablename__ = 'delist_tickers'

    ticker = Column(String(32), primary_key=True)
    created_at = Column(DateTime, default=datetime.datetime.now)

class SP500Ticker(Base):
    __tablename__ = 'sp_500_tickers'

    ticker = Column(String(32), primary_key=True)

class Nasdaq100Ticker(Base):
    __tablename__ = 'nasdap_100_tickers'

    ticker = Column(String(32), primary_key=True)

class NasdaqCompositeTicker(Base):
    __tablename__ = 'nasdaq_composite_tickers'

    ticker = Column(String(32), primary_key=True)

class DowJonesTicker(Base):
    __tablename__ = 'dow_jones_tickers'

    ticker = Column(String(32), primary_key=True)

class Russell2000Ticker(Base):
    __tablename__ = 'russell_2000_tickers'

    ticker = Column(String(32), primary_key=True)

class AllTicker(Base):
    __tablename__ = 'all_tickers'

    ticker = Column(String(32), primary_key=True)
    prev_ticker = Column(String(32))
    country = Column(String(100))
    exchange = Column(String(100))
    name = Column(String(200))
    logo = Column(String(200))
    classify = Column(String(50))  # Stock classification (ADR/GDR/EQ)
    list_date = Column(String(20))  # Listing date
    delist_date = Column(String(20))  # Delisting date
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    is_sp_500 = Column(Boolean, default=False)
    is_nasdap_100 = Column(Boolean, default=False)
    is_nasdaq_composite = Column(Boolean, default=False)
    is_dow_jones = Column(Boolean, default=False)
    is_russell_2000 = Column(Boolean, default=False)
    is_facts_processed = Column(Boolean, default=False)

class AllTickerHK(Base):
    """
    Hong Kong stock tickers model.
    """
    __tablename__ = 'all_tickers_hk'

    symbol = Column(String(32), primary_key=True)
    name = Column(String(200))  # English company name (primary)
    fullname = Column(String(255))
    cnname = Column(String(255))  # Chinese company name (secondary)
    cn_spell = Column(String(100))
    market = Column(String(100))
    list_status = Column(String(10))
    list_date = Column(String(20))
    delist_date = Column(String(20))
    trade_unit = Column(Float)
    isin = Column(String(50))
    curr_type = Column(String(10))
    logo = Column(String(200))
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AllTickerHK(symbol='{self.symbol}', name='{self.name}')>"

class SECJobProgress(Base):
    """
    SEC job progress tracking model for resuming jobs after interruption.
    """
    __tablename__ = "sec_job_progress"

    job_name = Column(String(255), primary_key=True)
    ticker = Column(String(20), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    def __repr__(self):
        return f"<SECJobProgress(job_name='{self.job_name}', ticker='{self.ticker}')>"

class SECFiling(Base):
    """
    SEC filing model representing metadata about SEC filings.
    """
    __tablename__ = "sec_filings"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    ticker = Column(String(20), ForeignKey("stocks.ticker"), nullable=False, index=True)
    cik = Column(String(20), nullable=False, index=True)
    accession_number = Column(String(20), nullable=False, index=True)
    filing_type = Column(String(20), nullable=False, index=True)  # e.g., '10-K', '10-Q'
    filing_date = Column(Date, nullable=False, index=True)
    report_date = Column(Date, nullable=True, index=True)
    fiscal_year = Column(Integer, nullable=True)
    fiscal_quarter = Column(Integer, nullable=True)
    url = Column(Text, nullable=True)  # TXT URL
    html_url = Column(Text, nullable=True)  # HTML URL
    index_url = Column(Text, nullable=True)  # index URL
    is_processed = Column(Boolean, default=False, nullable=False)
    is_financial_data = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    openai_file_id = Column(String(50), nullable=True)

    # Ensure ticker, accession_number combination is unique
    __table_args__ = (UniqueConstraint('ticker', 'accession_number', name='_ticker_accession_uc'),)

    def __repr__(self):
        return f"<SECFiling(ticker='{self.ticker}', filing_type='{self.filing_type}', filing_date='{self.filing_date}')>"

class SECFilingSection(Base):
    """
    SEC filing section model representing sections of SEC filings.
    """
    __tablename__ = "sec_filing_sections"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    filing_id = Column(BigInteger, ForeignKey("sec_filings.id"), nullable=False, index=True)
    section_name = Column(String(100), nullable=False, index=True)  # e.g., 'RISK_FACTORS', 'MD_AND_A'
    section_text = Column(Text, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Ensure filing_id, section_name combination is unique
    __table_args__ = (UniqueConstraint('filing_id', 'section_name', name='_filing_section_uc'),)

    def __repr__(self):
        return f"<SECFilingSection(filing_id={self.filing_id}, section_name='{self.section_name}')>"

class SECCompanyFactMetadata(Base):
    """
    SEC company fact metadata model representing metadata about the facts collection.
    """
    __tablename__ = "sec_company_fact_metadata"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    ticker = Column(String(20), ForeignKey("stocks.ticker"), nullable=False, index=True)
    cik = Column(String(20), nullable=False, index=True)
    entity_name = Column(String(255), nullable=True)
    version = Column(String(50), nullable=True)  # API version
    taxonomy_version = Column(String(50), nullable=True)  # Taxonomy version
    is_processed = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Ensure ticker is unique
    __table_args__ = (UniqueConstraint('ticker', name='_ticker_uc'),)

    def __repr__(self):
        return f"<SECCompanyFactMetadata(ticker='{self.ticker}', cik='{self.cik}')>"

class SECCompanyFact(Base):
    """
    SEC company fact model representing individual financial facts.
    """
    __tablename__ = "sec_company_facts"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    metadata_id = Column(BigInteger, ForeignKey("sec_company_fact_metadata.id"), nullable=False, index=True)
    taxonomy = Column(String(50), nullable=False, index=True)  # e.g., 'us-gaap', 'dei'
    concept = Column(String(100), nullable=False, index=True)  # e.g., 'Revenue', 'NetIncome'
    label = Column(String(255), nullable=True)  # Human-readable label
    description = Column(Text, nullable=True)  # Description of the concept
    value = Column(String(255), nullable=True)  # Value as string to handle different types
    value_numeric = Column(Float, nullable=True)  # Numeric value if applicable
    unit = Column(String(50), nullable=True)  # e.g., 'USD', 'shares'
    filing_date = Column(Date, nullable=True, index=True)  # Date of the filing
    end_date = Column(Date, nullable=True, index=True)  # End date of the period
    start_date = Column(Date, nullable=True)  # Start date of the period if applicable
    fiscal_year = Column(Integer, nullable=True)  # Fiscal year
    fiscal_period = Column(String(20), nullable=True)  # e.g., 'FY', 'Q1', 'Q2', 'Q3', 'Q4'
    form = Column(String(20), nullable=True, index=True)  # e.g., '10-K', '10-Q'
    frame = Column(String(50), nullable=True)  # XBRL context reference
    accession_number = Column(String(20), nullable=True)  # SEC accession number
    filing_url = Column(Text, nullable=True)  # URL to the filing
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Ensure metadata_id, taxonomy, concept, end_date, form combination is unique
    __table_args__ = (UniqueConstraint('metadata_id', 'taxonomy', 'concept', 'end_date', 'form', name='_fact_uc'),)

    def __repr__(self):
        return f"<SECCompanyFact(taxonomy='{self.taxonomy}', concept='{self.concept}', end_date='{self.end_date}')>"

class EarningsCalendarEvent(Base):
    __tablename__ = 'earnings_calendar_events'

    id = Column(Integer, primary_key=True)
    company = Column(String(255), nullable=False)
    company_name = Column(String(255), nullable=False)
    ticker = Column(String(32), nullable=False)
    eps_forecast = Column(String(50), nullable=True)
    revenue_forecast = Column(String(50), nullable=True)
    eps_actual = Column(String(50), nullable=True)
    revenue_actual = Column(String(50), nullable=True)
    market_cap = Column(Text, nullable=True)
    release_date = Column(DateTime, default=func.now())
    release_time = Column(Integer, nullable=True) # 0: unknown, 1: pre-market, 2: after-market
    is_filing_processed = Column(Boolean, default=0, nullable=True)

class StockHK(Base):
    """
    Hong Kong stock model representing basic stock information.
    """
    __tablename__ = "stock_hk"

    # Primary identifiers
    symbol = Column(String(32), primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())

    # Company information
    address1 = Column(Text, nullable=True)
    city = Column(Text, nullable=True)
    state = Column(Text, nullable=True)
    zip = Column(Text, nullable=True)
    country = Column(String(50), nullable=True, default='Hong Kong', index=True)
    phone = Column(Text, nullable=True)
    website = Column(Text, nullable=True)

    # Industry classification
    industry = Column(String(100), nullable=True, index=True)
    industry_key = Column(Text, nullable=True)
    industry_disp = Column(Text, nullable=True)
    sector = Column(String(100), nullable=True, index=True)
    sector_key = Column(Text, nullable=True)
    sector_disp = Column(Text, nullable=True)

    # Company details
    long_business_summary = Column(Text, nullable=True)
    full_time_employees = Column(Text, nullable=True)
    company_officers = Column(Text, nullable=True)

    # Risk metrics
    audit_risk = Column(Text, nullable=True)
    board_risk = Column(Text, nullable=True)
    compensation_risk = Column(Text, nullable=True)
    shareholder_rights_risk = Column(Text, nullable=True)
    overall_risk = Column(Text, nullable=True)

    # Governance and IR
    governance_epoch_date = Column(Text, nullable=True)
    compensation_as_of_epoch_date = Column(Text, nullable=True)
    ir_website = Column(Text, nullable=True)

    # Market data
    max_age = Column(Text, nullable=True)
    price_hint = Column(Text, nullable=True)
    previous_close = Column(Text, nullable=True)
    open_price = Column(Text, nullable=True)
    day_low = Column(Text, nullable=True)
    day_high = Column(Text, nullable=True)
    regular_market_previous_close = Column(Text, nullable=True)
    regular_market_open = Column(Text, nullable=True)
    regular_market_day_low = Column(Text, nullable=True)
    regular_market_day_high = Column(Text, nullable=True)
    regular_market_price = Column(Text, nullable=True)
    regular_market_volume = Column(Text, nullable=True)
    regular_market_change = Column(Text, nullable=True)
    regular_market_change_percent = Column(Text, nullable=True)
    regular_market_time = Column(Text, nullable=True)

    # Pre/Post market
    pre_market_price = Column(Text, nullable=True)
    pre_market_change = Column(Text, nullable=True)
    pre_market_change_percent = Column(Text, nullable=True)
    post_market_time = Column(Text, nullable=True)

    # Dividend information
    dividend_rate = Column(Text, nullable=True)
    dividend_yield = Column(Text, nullable=True)
    ex_dividend_date = Column(Text, nullable=True)
    dividend_date = Column(Text, nullable=True)
    payout_ratio = Column(Text, nullable=True)
    five_year_avg_dividend_yield = Column(Text, nullable=True)
    trailing_annual_dividend_rate = Column(Text, nullable=True)
    trailing_annual_dividend_yield = Column(Text, nullable=True)
    last_dividend_value = Column(Text, nullable=True)
    last_dividend_date = Column(Text, nullable=True)

    # Trading metrics
    beta = Column(Text, nullable=True)
    volume = Column(Text, nullable=True)
    average_volume = Column(Text, nullable=True)
    average_daily_volume_10_day = Column(Text, nullable=True)
    bid = Column(Text, nullable=True)
    ask = Column(Text, nullable=True)
    bid_size = Column(Text, nullable=True)
    ask_size = Column(Text, nullable=True)

    # Market cap and shares
    market_cap = Column(Text, nullable=True)
    enterprise_value = Column(Text, nullable=True)
    shares_outstanding = Column(Text, nullable=True)
    float_shares = Column(Text, nullable=True)
    implied_shares_outstanding = Column(Text, nullable=True)

    # Price metrics
    fifty_two_week_low = Column(Text, nullable=True)
    fifty_two_week_high = Column(Text, nullable=True)
    fifty_two_week_low_change = Column(Text, nullable=True)
    fifty_two_week_low_change_percent = Column(Text, nullable=True)
    fifty_two_week_range = Column(Text, nullable=True)
    fifty_two_week_high_change = Column(Text, nullable=True)
    fifty_two_week_high_change_percent = Column(Text, nullable=True)
    fifty_two_week_change_percent = Column(Text, nullable=True)
    fifty_day_average = Column(Text, nullable=True)
    fifty_day_average_change = Column(Text, nullable=True)
    two_hundred_day_average = Column(Text, nullable=True)
    two_hundred_day_average_change = Column(Text, nullable=True)

    # Financial ratios
    trailing_pe = Column(Text, nullable=True)
    forward_pe = Column(Text, nullable=True)
    price_to_book = Column(Text, nullable=True)
    price_to_sales_trailing_12_months = Column(Text, nullable=True)
    price_eps_current_year = Column(Text, nullable=True)

    # Short interest
    shares_short = Column(Text, nullable=True)
    short_ratio = Column(Text, nullable=True)
    shares_short_prior_month = Column(Text, nullable=True)
    shares_short_previous_month_date = Column(Text, nullable=True)
    date_short_interest = Column(Text, nullable=True)
    shares_percent_shares_out = Column(Text, nullable=True)
    held_percent_insiders = Column(Text, nullable=True)
    held_percent_institutions = Column(Text, nullable=True)
    short_percent_of_float = Column(Text, nullable=True)

    # Financial metrics
    book_value = Column(Text, nullable=True)
    earnings_quarterly_growth = Column(Text, nullable=True)
    net_income_to_common = Column(Text, nullable=True)
    trailing_eps = Column(Text, nullable=True)
    forward_eps = Column(Text, nullable=True)
    eps_trailing_twelve_months = Column(Text, nullable=True)
    eps_forward = Column(Text, nullable=True)
    eps_current_year = Column(Text, nullable=True)
    enterprise_to_revenue = Column(Text, nullable=True)
    enterprise_to_ebitda = Column(Text, nullable=True)

    # Financial statements
    total_cash = Column(Text, nullable=True)
    total_debt = Column(Text, nullable=True)
    quick_ratio = Column(Text, nullable=True)
    current_ratio = Column(Text, nullable=True)
    total_revenue = Column(Text, nullable=True)
    debt_to_equity = Column(Text, nullable=True)
    revenue_per_share = Column(Text, nullable=True)
    return_on_assets = Column(Text, nullable=True)
    return_on_equity = Column(Text, nullable=True)
    gross_profits = Column(Text, nullable=True)
    free_cashflow = Column(Text, nullable=True)
    operating_cashflow = Column(Text, nullable=True)
    earnings_growth = Column(Text, nullable=True)
    revenue_growth = Column(Text, nullable=True)
    gross_margins = Column(Text, nullable=True)
    ebitda_margins = Column(Text, nullable=True)
    operating_margins = Column(Text, nullable=True)
    ebitda = Column(Text, nullable=True)
    total_cash_per_share = Column(Text, nullable=True)

    # Exchange information
    exchange = Column(String(50), nullable=True, default='HKSE', index=True)
    full_exchange_name = Column(Text, nullable=True)
    exchange_timezone_name = Column(Text, nullable=True)
    exchange_timezone_short_name = Column(Text, nullable=True)
    gmt_offset_milliseconds = Column(Text, nullable=True)

    # Market analysis
    current_price = Column(Text, nullable=True)
    target_high_price = Column(Text, nullable=True)
    target_low_price = Column(Text, nullable=True)
    target_mean_price = Column(Text, nullable=True)
    recommendation_mean = Column(Text, nullable=True)
    recommendation_key = Column(Text, nullable=True)
    number_of_analyst_opinions = Column(Text, nullable=True)
    average_analyst_rating = Column(Text, nullable=True)

    # Trading status
    market_state = Column(Text, nullable=True)
    quote_source_name = Column(Text, nullable=True)
    tradeable = Column(Text, nullable=True)
    crypto_tradeable = Column(Text, nullable=True)
    currency = Column(Text, nullable=True)
    exchange_data_delayed_by = Column(Text, nullable=True)

    # Other fields
    executive_team = Column(Text, nullable=True)
    quote_type = Column(Text, nullable=True)
    language = Column(Text, nullable=True)
    region = Column(Text, nullable=True)
    market = Column(Text, nullable=True)
    type_disp = Column(Text, nullable=True)
    source_interval = Column(Text, nullable=True)
    message_board_id = Column(Text, nullable=True)
    custom_price_alert_confidence = Column(Text, nullable=True)
    corporate_actions = Column(Text, nullable=True)
    financial_currency = Column(Text, nullable=True)

    # Timestamps
    last_fiscal_year_end = Column(Text, nullable=True)
    next_fiscal_year_end = Column(Text, nullable=True)
    most_recent_quarter = Column(Text, nullable=True)
    last_split_date = Column(Text, nullable=True)
    last_split_factor = Column(Text, nullable=True)
    first_trade_date_milliseconds = Column(Text, nullable=True)
    earnings_timestamp = Column(Text, nullable=True)
    earnings_timestamp_start = Column(Text, nullable=True)
    earnings_timestamp_end = Column(Text, nullable=True)
    earnings_call_timestamp_start = Column(Text, nullable=True)
    earnings_call_timestamp_end = Column(Text, nullable=True)
    is_earnings_date_estimate = Column(Text, nullable=True)
    esg_populated = Column(Text, nullable=True)
    has_pre_post_market_data = Column(Text, nullable=True)

    def __repr__(self):
        return f"<StockHK(symbol='{self.symbol}', name='{self.name}')>"

class StockHKPrice(Base):
    """
    Hong Kong stock price model representing daily price data.
    """
    __tablename__ = "stock_hk_price"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    symbol = Column(String(32), ForeignKey("stock_hk.symbol"), nullable=False, index=True)
    date = Column(Date, nullable=False, index=True)
    open = Column(Float, nullable=True)
    high = Column(Float, nullable=True)
    low = Column(Float, nullable=True)
    close = Column(Float, nullable=True)
    volume = Column(BigInteger, nullable=True)

    # Ensure symbol and date combination is unique
    __table_args__ = (UniqueConstraint('symbol', 'date', name='_symbol_date_uc'),)

    def __repr__(self):
        return f"<StockHKPrice(symbol='{self.symbol}', date='{self.date}', close={self.close})>"

class StockHKFundamental(Base):
    """
    Hong Kong stock fundamental model representing fundamental data.
    """
    __tablename__ = "stock_hk_fundamentals"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    symbol = Column(String(32), ForeignKey("stock_hk.symbol"), nullable=False, index=True)
    date = Column(Date, nullable=False, index=True)
    dividend_rate = Column(Float, nullable=True)
    dividend_yield = Column(Float, nullable=True)
    pe_ratio = Column(Float, nullable=True)
    eps = Column(Float, nullable=True)
    beta = Column(Float, nullable=True)
    fifty_two_week_high = Column(Float, nullable=True)
    fifty_two_week_low = Column(Float, nullable=True)

    # Ensure symbol and date combination is unique
    __table_args__ = (UniqueConstraint('symbol', 'date', name='_symbol_date_uc'),)

    def __repr__(self):
        return f"<StockHKFundamental(symbol='{self.symbol}', date='{self.date}')>"

class AllTickerSG(Base):
    """
    Singapore stock tickers model.
    """
    __tablename__ = 'all_tickers_sg'

    symbol = Column(String(32), primary_key=True)
    name = Column(String(200))
    country = Column(String(50), default='Singapore')
    currency = Column(String(10))
    exchange = Column(String(100))
    isin = Column(String(50))
    logo = Column(String(200))
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AllTickerSG(symbol='{self.symbol}', name='{self.name}')>"

class AllTickerJP(Base):
    """
    Japan stock tickers model.
    """
    __tablename__ = 'all_tickers_jp'

    symbol = Column(String(32), primary_key=True)
    name = Column(String(200))
    country = Column(String(50), default='Japan')
    currency = Column(String(10))
    exchange = Column(String(100))
    isin = Column(String(50))
    logo = Column(String(200))
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AllTickerJP(symbol='{self.symbol}', name='{self.name}')>"

class AllTickerFR(Base):
    """
    France stock tickers model.
    """
    __tablename__ = 'all_tickers_fr'

    symbol = Column(String(32), primary_key=True)
    name = Column(String(200))
    country = Column(String(50), default='France')
    currency = Column(String(10))
    exchange = Column(String(100))
    isin = Column(String(50))
    logo = Column(String(200))
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AllTickerFR(symbol='{self.symbol}', name='{self.name}')>"

class AllTickerGE(Base):
    """
    Germany stock tickers model.
    """
    __tablename__ = 'all_tickers_ge'

    symbol = Column(String(32), primary_key=True)
    name = Column(String(200))
    country = Column(String(50), default='Germany')
    currency = Column(String(10))
    exchange = Column(String(100))
    isin = Column(String(50))
    logo = Column(String(200))
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AllTickerGE(symbol='{self.symbol}', name='{self.name}')>"

class AllTickerKR(Base):
    """
    Korea stock tickers model.
    """
    __tablename__ = 'all_tickers_kr'

    symbol = Column(String(32), primary_key=True)
    name = Column(String(200))
    country = Column(String(50), default='Korea')
    currency = Column(String(10))
    exchange = Column(String(100))
    isin = Column(String(50))
    logo = Column(String(200))
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AllTickerKR(symbol='{self.symbol}', name='{self.name}')>"

class AllTickerMY(Base):
    """
    Malaysia stock tickers model.
    """
    __tablename__ = 'all_tickers_my'

    symbol = Column(String(32), primary_key=True)
    name = Column(String(200))
    country = Column(String(50), default='Malaysia')
    currency = Column(String(10))
    exchange = Column(String(100))
    isin = Column(String(50))
    logo = Column(String(200))
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AllTickerMY(symbol='{self.symbol}', name='{self.name}')>"

class AllTickerUK(Base):
    """
    United Kingdom stock tickers model.
    """
    __tablename__ = 'all_tickers_uk'

    symbol = Column(String(32), primary_key=True)
    name = Column(String(200))
    country = Column(String(50), default='United Kingdom')
    currency = Column(String(10))
    exchange = Column(String(100))
    isin = Column(String(50))
    logo = Column(String(200))
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AllTickerUK(symbol='{self.symbol}', name='{self.name}')>"

class AllTickerCA(Base):
    """
    Canada stock tickers model.
    """
    __tablename__ = 'all_tickers_ca'

    symbol = Column(String(32), primary_key=True)
    name = Column(String(200))
    country = Column(String(50), default='Canada')
    currency = Column(String(10))
    exchange = Column(String(100))
    isin = Column(String(50))
    logo = Column(String(200))
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AllTickerCA(symbol='{self.symbol}', name='{self.name}')>"

class AllTickerTH(Base):
    """
    Thailand stock tickers model.
    """
    __tablename__ = 'all_tickers_th'

    symbol = Column(String(32), primary_key=True)
    name = Column(String(200))
    country = Column(String(50), default='Thailand')
    currency = Column(String(10))
    exchange = Column(String(100))
    isin = Column(String(50))
    logo = Column(String(200))
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AllTickerTH(symbol='{self.symbol}', name='{self.name}')>"

class AllTickerTW(Base):
    """
    Taiwan stock tickers model.
    """
    __tablename__ = 'all_tickers_tw'

    symbol = Column(String(32), primary_key=True)
    name = Column(String(200))
    country = Column(String(50), default='Taiwan')
    currency = Column(String(10))
    exchange = Column(String(100))
    isin = Column(String(50))
    logo = Column(String(200))
    yf_symbol = Column(String(32))  # Yahoo Finance compatible symbol format
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<AllTickerTW(symbol='{self.symbol}', name='{self.name}')>"
