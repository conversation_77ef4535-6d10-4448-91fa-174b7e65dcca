-- Add yf_symbol field to all regional ticker tables and update symbol processing
-- Migration created: 2025-05-29 10:00

-- Add yf_symbol field to all_tickers table
ALTER TABLE all_tickers
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add yf_symbol field to all_tickers_hk table
ALTER TABLE all_tickers_hk
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add yf_symbol field to all_tickers_sg table
ALTER TABLE all_tickers_sg
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add yf_symbol field to all_tickers_kr table
ALTER TABLE all_tickers_kr
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add yf_symbol field to all_tickers_jp table
ALTER TABLE all_tickers_jp
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add yf_symbol field to all_tickers_tw table
ALTER TABLE all_tickers_tw
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add yf_symbol field to all_tickers_ca table
ALTER TABLE all_tickers_ca
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add yf_symbol field to all_tickers_fr table
ALTER TABLE all_tickers_fr
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add yf_symbol field to all_tickers_ge table
ALTER TABLE all_tickers_ge
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add yf_symbol field to all_tickers_my table
ALTER TABLE all_tickers_my
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add yf_symbol field to all_tickers_th table
ALTER TABLE all_tickers_th
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add yf_symbol field to all_tickers_uk table
ALTER TABLE all_tickers_uk
ADD COLUMN `yf_symbol` VARCHAR(32) DEFAULT NULL COMMENT 'Yahoo Finance compatible symbol format';

-- Add indexes for yf_symbol fields for better query performance
ALTER TABLE all_tickers ADD INDEX `idx_yf_symbol` (`yf_symbol`);
ALTER TABLE all_tickers_hk ADD INDEX `idx_yf_symbol` (`yf_symbol`);
ALTER TABLE all_tickers_sg ADD INDEX `idx_yf_symbol` (`yf_symbol`);
ALTER TABLE all_tickers_kr ADD INDEX `idx_yf_symbol` (`yf_symbol`);
ALTER TABLE all_tickers_jp ADD INDEX `idx_yf_symbol` (`yf_symbol`);
ALTER TABLE all_tickers_tw ADD INDEX `idx_yf_symbol` (`yf_symbol`);
ALTER TABLE all_tickers_ca ADD INDEX `idx_yf_symbol` (`yf_symbol`);
ALTER TABLE all_tickers_fr ADD INDEX `idx_yf_symbol` (`yf_symbol`);
ALTER TABLE all_tickers_ge ADD INDEX `idx_yf_symbol` (`yf_symbol`);
ALTER TABLE all_tickers_my ADD INDEX `idx_yf_symbol` (`yf_symbol`);
ALTER TABLE all_tickers_th ADD INDEX `idx_yf_symbol` (`yf_symbol`);
ALTER TABLE all_tickers_uk ADD INDEX `idx_yf_symbol` (`yf_symbol`);
