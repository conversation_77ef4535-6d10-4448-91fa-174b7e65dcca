-- Create stock_hk table for Hong Kong stocks
CREATE TABLE IF NOT EXISTS stock_hk (
  -- Primary identifiers
  `ts_code` varchar(32) NOT NULL,
  `name` varchar(255) NOT NULL,
  `last_updated` datetime DEFAULT NULL,
  
  -- Company information
  `address1` text DEFAULT NULL,
  `city` text DEFAULT NULL,
  `state` text DEFAULT NULL,
  `zip` text DEFAULT NULL,
  `country` varchar(50) DEFAULT 'Hong Kong',
  `phone` text DEFAULT NULL,
  `website` text DEFAULT NULL,
  
  -- Industry classification
  `sector` varchar(100) DEFAULT NULL,
  `sector_key` text DEFAULT NULL,
  `sector_disp` text DEFAULT NULL,
  `industry` varchar(100) DEFAULT NULL,
  `industry_key` text DEFAULT NULL,
  `industry_disp` text DEFAULT NULL,
  
  -- Company details
  `long_business_summary` text DEFAULT NULL,
  `full_time_employees` text DEFAULT NULL,
  `company_officers` text DEFAULT NULL,
  
  -- Risk metrics
  `audit_risk` text DEFAULT NULL,
  `board_risk` text DEFAULT NULL,
  `compensation_risk` text DEFAULT NULL,
  `shareholder_rights_risk` text DEFAULT NULL,
  `overall_risk` text DEFAULT NULL,
  
  -- Governance and IR
  `governance_epoch_date` text DEFAULT NULL,
  `compensation_as_of_epoch_date` text DEFAULT NULL,
  `ir_website` text DEFAULT NULL,
  
  -- Market data
  `max_age` text DEFAULT NULL,
  `price_hint` text DEFAULT NULL,
  `previous_close` text DEFAULT NULL,
  `open_price` text DEFAULT NULL,
  `day_low` text DEFAULT NULL,
  `day_high` text DEFAULT NULL,
  `regular_market_previous_close` text DEFAULT NULL,
  `regular_market_open` text DEFAULT NULL,
  `regular_market_day_low` text DEFAULT NULL,
  `regular_market_day_high` text DEFAULT NULL,
  `regular_market_price` text DEFAULT NULL,
  `regular_market_volume` text DEFAULT NULL,
  `regular_market_change` text DEFAULT NULL,
  `regular_market_change_percent` text DEFAULT NULL,
  `regular_market_time` text DEFAULT NULL,
  
  -- Pre/Post market
  `pre_market_price` text DEFAULT NULL,
  `pre_market_change` text DEFAULT NULL,
  `pre_market_change_percent` text DEFAULT NULL,
  `post_market_time` text DEFAULT NULL,
  
  -- Dividend information
  `dividend_rate` text DEFAULT NULL,
  `dividend_yield` text DEFAULT NULL,
  `ex_dividend_date` text DEFAULT NULL,
  `dividend_date` text DEFAULT NULL,
  `payout_ratio` text DEFAULT NULL,
  `five_year_avg_dividend_yield` text DEFAULT NULL,
  `trailing_annual_dividend_rate` text DEFAULT NULL,
  `trailing_annual_dividend_yield` text DEFAULT NULL,
  `last_dividend_value` text DEFAULT NULL,
  `last_dividend_date` text DEFAULT NULL,
  
  -- Trading metrics
  `beta` text DEFAULT NULL,
  `volume` text DEFAULT NULL,
  `average_volume` text DEFAULT NULL,
  `average_daily_volume_10_day` text DEFAULT NULL,
  `bid` text DEFAULT NULL,
  `ask` text DEFAULT NULL,
  `bid_size` text DEFAULT NULL,
  `ask_size` text DEFAULT NULL,
  
  -- Market cap and shares
  `market_cap` text DEFAULT NULL,
  `enterprise_value` text DEFAULT NULL,
  `shares_outstanding` text DEFAULT NULL,
  `float_shares` text DEFAULT NULL,
  `implied_shares_outstanding` text DEFAULT NULL,
  
  -- Price metrics
  `fifty_two_week_low` text DEFAULT NULL,
  `fifty_two_week_high` text DEFAULT NULL,
  `fifty_two_week_low_change` text DEFAULT NULL,
  `fifty_two_week_low_change_percent` text DEFAULT NULL,
  `fifty_two_week_range` text DEFAULT NULL,
  `fifty_two_week_high_change` text DEFAULT NULL,
  `fifty_two_week_high_change_percent` text DEFAULT NULL,
  `fifty_two_week_change_percent` text DEFAULT NULL,
  `fifty_day_average` text DEFAULT NULL,
  `fifty_day_average_change` text DEFAULT NULL,
  `two_hundred_day_average` text DEFAULT NULL,
  `two_hundred_day_average_change` text DEFAULT NULL,
  
  -- Financial ratios
  `trailing_pe` text DEFAULT NULL,
  `forward_pe` text DEFAULT NULL,
  `price_to_book` text DEFAULT NULL,
  `price_to_sales_trailing_12_months` text DEFAULT NULL,
  `price_eps_current_year` text DEFAULT NULL,

  -- Short interest
  `shares_short` text DEFAULT NULL,
  `short_ratio` text DEFAULT NULL,
  `shares_short_prior_month` text DEFAULT NULL,
  `shares_short_previous_month_date` text DEFAULT NULL,
  `date_short_interest` text DEFAULT NULL,
  `shares_percent_shares_out` text DEFAULT NULL,
  `held_percent_insiders` text DEFAULT NULL,
  `held_percent_institutions` text DEFAULT NULL,
  `short_percent_of_float` text DEFAULT NULL,
  
  -- Financial metrics
  `book_value` text DEFAULT NULL,
  `earnings_quarterly_growth` text DEFAULT NULL,
  `net_income_to_common` text DEFAULT NULL,
  `trailing_eps` text DEFAULT NULL,
  `forward_eps` text DEFAULT NULL,
  `eps_trailing_twelve_months` text DEFAULT NULL,
  `eps_forward` text DEFAULT NULL,
  `eps_current_year` text DEFAULT NULL,
  `enterprise_to_revenue` text DEFAULT NULL,
  `enterprise_to_ebitda` text DEFAULT NULL,

  -- Financial statements
  `total_cash` text DEFAULT NULL,
  `total_debt` text DEFAULT NULL,
  `quick_ratio` text DEFAULT NULL,
  `current_ratio` text DEFAULT NULL,
  `total_revenue` text DEFAULT NULL,
  `debt_to_equity` text DEFAULT NULL,
  `revenue_per_share` text DEFAULT NULL,
  `return_on_assets` text DEFAULT NULL,
  `return_on_equity` text DEFAULT NULL,
  `gross_profits` text DEFAULT NULL,
  `free_cashflow` text DEFAULT NULL,
  `operating_cashflow` text DEFAULT NULL,
  `earnings_growth` text DEFAULT NULL,
  `revenue_growth` text DEFAULT NULL,
  `gross_margins` text DEFAULT NULL,
  `ebitda_margins` text DEFAULT NULL,
  `operating_margins` text DEFAULT NULL,
  `ebitda` text DEFAULT NULL,
  `total_cash_per_share` text DEFAULT NULL,

  -- Exchange information
  `exchange` varchar(50) DEFAULT 'HKSE',
  `full_exchange_name` text DEFAULT NULL,
  `exchange_timezone_name` text DEFAULT NULL,
  `exchange_timezone_short_name` text DEFAULT NULL,
  `gmt_offset_milliseconds` text DEFAULT NULL,

  -- Market analysis
  `current_price` text DEFAULT NULL,
  `target_high_price` text DEFAULT NULL,
  `target_low_price` text DEFAULT NULL,
  `target_mean_price` text DEFAULT NULL,
  `recommendation_mean` text DEFAULT NULL,
  `recommendation_key` text DEFAULT NULL,
  `number_of_analyst_opinions` text DEFAULT NULL,
  `average_analyst_rating` text DEFAULT NULL,
  
  -- Trading status
  `market_state` text DEFAULT NULL,
  `quote_source_name` text DEFAULT NULL,
  `tradeable` text DEFAULT NULL,
  `crypto_tradeable` text DEFAULT NULL,
  `currency` text DEFAULT NULL,
  `exchange_data_delayed_by` text DEFAULT NULL,
  
  -- Other fields
  `executive_team` text DEFAULT NULL,
  `quote_type` text DEFAULT NULL,
  `language` text DEFAULT NULL,
  `region` text DEFAULT NULL,
  `market` text DEFAULT NULL,
  `type_disp` text DEFAULT NULL,
  `source_interval` text DEFAULT NULL,
  `message_board_id` text DEFAULT NULL,
  `custom_price_alert_confidence` text DEFAULT NULL,
  `corporate_actions` text DEFAULT NULL,
  `financial_currency` text DEFAULT NULL,
  
  -- Timestamps
  `last_fiscal_year_end` text DEFAULT NULL,
  `next_fiscal_year_end` text DEFAULT NULL,
  `most_recent_quarter` text DEFAULT NULL,
  `last_split_date` text DEFAULT NULL,
  `last_split_factor` text DEFAULT NULL,
  `first_trade_date_milliseconds` text DEFAULT NULL,
  `earnings_timestamp` text DEFAULT NULL,
  `earnings_timestamp_start` text DEFAULT NULL,
  `earnings_timestamp_end` text DEFAULT NULL,
  `earnings_call_timestamp_start` text DEFAULT NULL,
  `earnings_call_timestamp_end` text DEFAULT NULL,
  `is_earnings_date_estimate` text DEFAULT NULL,
  `esg_populated` text DEFAULT NULL,
  `has_pre_post_market_data` text DEFAULT NULL,
  
  PRIMARY KEY (`ts_code`),
  KEY `ix_stock_hk_ts_code` (`ts_code`),
  KEY `ix_stock_hk_industry` (`industry`),
  KEY `ix_stock_hk_sector` (`sector`),
  KEY `ix_stock_hk_exchange` (`exchange`),
  KEY `ix_stock_hk_country` (`country`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Create stock_hk_price table for Hong Kong stock prices
CREATE TABLE IF NOT EXISTS stock_hk_price (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ts_code` varchar(32) NOT NULL,
  `date` date NOT NULL,
  `open` float DEFAULT NULL,
  `high` float DEFAULT NULL,
  `low` float DEFAULT NULL,
  `close` float DEFAULT NULL,
  `volume` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `_ts_code_date_uc` (`ts_code`,`date`),
  KEY `ix_stock_hk_price_ts_code` (`ts_code`),
  KEY `ix_stock_hk_price_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Create stock_hk_fundamentals table for Hong Kong stock fundamentals
CREATE TABLE IF NOT EXISTS stock_hk_fundamentals (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ts_code` varchar(32) NOT NULL,
  `date` date NOT NULL,
  `dividend_rate` float DEFAULT NULL,
  `dividend_yield` float DEFAULT NULL,
  `pe_ratio` float DEFAULT NULL,
  `eps` float DEFAULT NULL,
  `beta` float DEFAULT NULL,
  `fifty_two_week_high` float DEFAULT NULL,
  `fifty_two_week_low` float DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `_ts_code_date_uc` (`ts_code`,`date`),
  KEY `ix_stock_hk_fundamentals_date` (`date`),
  KEY `ix_stock_hk_fundamentals_ts_code` (`ts_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
