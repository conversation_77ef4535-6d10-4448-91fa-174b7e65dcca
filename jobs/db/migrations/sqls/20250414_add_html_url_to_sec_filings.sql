-- Add html_url column to sec_filings table if it doesn't exist
-- This is a no-op if the column already exists (which it does in the schema)
-- but we're creating this migration to ensure it exists in all environments

-- Check if html_url column exists
SET @exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'sec_filings'
    AND column_name = 'html_url'
);

-- Add the column if it doesn't exist
SET @query = IF(
    @exists = 0,
    'ALTER TABLE sec_filings ADD COLUMN html_url TEXT NULL AFTER url',
    'SELECT "html_url column already exists in sec_filings table"'
);

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
