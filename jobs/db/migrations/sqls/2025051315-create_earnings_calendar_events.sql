CREATE TABLE `earnings_calendar_events` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company` varchar(255) NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `ticker` varchar(32) NOT NULL,
  `eps_forecast` varchar(50) DEFAULT NULL,
  `revenue_forecast` varchar(50) DEFAULT NULL,
  `eps_actual` varchar(50) DEFAULT NULL,
  `revenue_actual` varchar(50) DEFAULT NULL,
  `market_cap` text DEFAULT NULL,
  `release_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `release_time` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ix_earnings_calendar_events_ticker` (`ticker`),
  KEY `ix_earnings_calendar_events_release_date` (`release_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;