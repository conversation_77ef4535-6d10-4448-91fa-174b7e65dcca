CREATE TABLE `data_sources` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` varchar(50) NOT NULL,
  `url` varchar(255) DEFAULT NULL,
  `api_key` varchar(255) DEFAULT NULL,
  `priority` int NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `last_checked` datetime DEFAULT NULL,
  `last_used` datetime DEFAULT NULL,
  `error_count` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ix_data_sources_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `event_monitoring_status` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `event_id` bigint NOT NULL,
  `monitoring_start` datetime NOT NULL,
  `monitoring_end` datetime DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `last_checked` datetime DEFAULT NULL,
  `next_check` datetime DEFAULT NULL,
  `check_frequency` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ix_event_monitoring_status_event_id` (`event_id`),
  KEY `ix_event_monitoring_status_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `financial_calendar_events` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `event_name` varchar(255) NOT NULL,
  `event_type` varchar(100) NOT NULL,
  `country` varchar(50) NOT NULL,
  `date` date NOT NULL,
  `time` varchar(50) DEFAULT NULL,
  `date_time` datetime DEFAULT NULL,
  `actual` varchar(50) DEFAULT NULL,
  `forecast` varchar(50) DEFAULT NULL,
  `previous` varchar(50) DEFAULT NULL,
  `impact` varchar(20) DEFAULT NULL,
  `description` text,
  `is_released` tinyint(1) NOT NULL,
  `last_updated` datetime DEFAULT NULL,
  `retry_count` int DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `_date_country_event_name` (`date`,`country`,`event_name`),
  KEY `ix_financial_calendar_events_event_name` (`event_name`),
  KEY `ix_financial_calendar_events_impact` (`impact`),
  KEY `ix_financial_calendar_events_date` (`date`),
  KEY `ix_financial_calendar_events_event_type` (`event_type`),
  KEY `ix_financial_calendar_events_country` (`country`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `job_runs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `job_name` varchar(100) NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `records_processed` int DEFAULT NULL,
  `error_message` text,
  PRIMARY KEY (`id`),
  KEY `ix_job_runs_job_name` (`job_name`),
  KEY `ix_job_runs_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stock_fundamentals` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ticker` varchar(20) NOT NULL,
  `date` date NOT NULL,
  `dividend_rate` float DEFAULT NULL,
  `dividend_yield` float DEFAULT NULL,
  `pe_ratio` float DEFAULT NULL,
  `eps` float DEFAULT NULL,
  `beta` float DEFAULT NULL,
  `fifty_two_week_high` float DEFAULT NULL,
  `fifty_two_week_low` float DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `_ticker_date_uc` (`ticker`,`date`),
  KEY `ix_stock_fundamentals_date` (`date`),
  KEY `ix_stock_fundamentals_ticker` (`ticker`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stock_prices` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ticker` varchar(20) NOT NULL,
  `date` date NOT NULL,
  `open` float DEFAULT NULL,
  `high` float DEFAULT NULL,
  `low` float DEFAULT NULL,
  `close` float DEFAULT NULL,
  `volume` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `_ticker_date_uc` (`ticker`,`date`),
  KEY `ix_stock_prices_ticker` (`ticker`),
  KEY `ix_stock_prices_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `stocks` (
  -- Primary identifiers
  `ticker` varchar(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `indexes` varchar(255) DEFAULT NULL,
  `last_updated` datetime DEFAULT NULL,
  
  -- Company information
  `address1` text DEFAULT NULL,
  `city` text DEFAULT NULL,
  `state` text DEFAULT NULL,
  `zip` text DEFAULT NULL,
  `country` varchar(50) DEFAULT NULL,
  `phone` text DEFAULT NULL,
  `website` text DEFAULT NULL,
  
  -- Industry classification
  `sector` varchar(100) DEFAULT NULL,
  `sector_key` text DEFAULT NULL,
  `sector_disp` text DEFAULT NULL,
  `industry` varchar(100) DEFAULT NULL,
  `industry_key` text DEFAULT NULL,
  `industry_disp` text DEFAULT NULL,
  
  -- Company details
  `long_business_summary` text DEFAULT NULL,
  `full_time_employees` text DEFAULT NULL,
  `company_officers` text DEFAULT NULL,
  
  -- Risk metrics
  `audit_risk` text DEFAULT NULL,
  `board_risk` text DEFAULT NULL,
  `compensation_risk` text DEFAULT NULL,
  `shareholder_rights_risk` text DEFAULT NULL,
  `overall_risk` text DEFAULT NULL,
  
  -- Governance and IR
  `governance_epoch_date` text DEFAULT NULL,
  `compensation_as_of_epoch_date` text DEFAULT NULL,
  `ir_website` text DEFAULT NULL,
  
  -- Market data
  `max_age` text DEFAULT NULL,
  `price_hint` text DEFAULT NULL,
  `previous_close` text DEFAULT NULL,
  `open_price` text DEFAULT NULL,
  `day_low` text DEFAULT NULL,
  `day_high` text DEFAULT NULL,
  `regular_market_previous_close` text DEFAULT NULL,
  `regular_market_open` text DEFAULT NULL,
  `regular_market_day_low` text DEFAULT NULL,
  `regular_market_day_high` text DEFAULT NULL,
  `regular_market_price` text DEFAULT NULL,
  `regular_market_volume` text DEFAULT NULL,
  `regular_market_change` text DEFAULT NULL,
  `regular_market_change_percent` text DEFAULT NULL,
  `regular_market_time` text DEFAULT NULL,
  
  -- Pre/Post market
  `pre_market_price` text DEFAULT NULL,
  `pre_market_change` text DEFAULT NULL,
  `pre_market_change_percent` text DEFAULT NULL,
  `post_market_time` text DEFAULT NULL,
  
  -- Dividend information
  `dividend_rate` text DEFAULT NULL,
  `dividend_yield` text DEFAULT NULL,
  `ex_dividend_date` text DEFAULT NULL,
  `dividend_date` text DEFAULT NULL,
  `payout_ratio` text DEFAULT NULL,
  `five_year_avg_dividend_yield` text DEFAULT NULL,
  `trailing_annual_dividend_rate` text DEFAULT NULL,
  `trailing_annual_dividend_yield` text DEFAULT NULL,
  `last_dividend_value` text DEFAULT NULL,
  `last_dividend_date` text DEFAULT NULL,
  
  -- Trading metrics
  `beta` text DEFAULT NULL,
  `volume` text DEFAULT NULL,
  `average_volume` text DEFAULT NULL,
  `average_daily_volume_10_day` text DEFAULT NULL,
  `bid` text DEFAULT NULL,
  `ask` text DEFAULT NULL,
  `bid_size` text DEFAULT NULL,
  `ask_size` text DEFAULT NULL,
  
  -- Market cap and shares
  `market_cap` text DEFAULT NULL,
  `enterprise_value` text DEFAULT NULL,
  `shares_outstanding` text DEFAULT NULL,
  `float_shares` text DEFAULT NULL,
  `implied_shares_outstanding` text DEFAULT NULL,
  
  -- Price metrics
  `fifty_two_week_low` text DEFAULT NULL,
  `fifty_two_week_high` text DEFAULT NULL,
  `fifty_two_week_low_change` text DEFAULT NULL,
  `fifty_two_week_low_change_percent` text DEFAULT NULL,
  `fifty_two_week_range` text DEFAULT NULL,
  `fifty_two_week_high_change` text DEFAULT NULL,
  `fifty_two_week_high_change_percent` text DEFAULT NULL,
  `fifty_two_week_change_percent` text DEFAULT NULL,
  `fifty_day_average` text DEFAULT NULL,
  `fifty_day_average_change` text DEFAULT NULL,
  `two_hundred_day_average` text DEFAULT NULL,
  `two_hundred_day_average_change` text DEFAULT NULL,
  
  -- Financial ratios
  `trailing_pe` text DEFAULT NULL,
  `forward_pe` text DEFAULT NULL,
  `price_to_book` text DEFAULT NULL,
  `price_to_sales_trailing_12_months` text DEFAULT NULL,
  `price_eps_current_year` text DEFAULT NULL,

  -- Short interest
  `shares_short` text DEFAULT NULL,
  `short_ratio` text DEFAULT NULL,
  `shares_short_prior_month` text DEFAULT NULL,
  `shares_short_previous_month_date` text DEFAULT NULL,
  `date_short_interest` text DEFAULT NULL,
  `shares_percent_shares_out` text DEFAULT NULL,
  `held_percent_insiders` text DEFAULT NULL,
  `held_percent_institutions` text DEFAULT NULL,
  `short_percent_of_float` text DEFAULT NULL,
  
  -- Financial metrics
  `book_value` text DEFAULT NULL,
  `earnings_quarterly_growth` text DEFAULT NULL,
  `net_income_to_common` text DEFAULT NULL,
  `trailing_eps` text DEFAULT NULL,
  `forward_eps` text DEFAULT NULL,
  `eps_trailing_twelve_months` text DEFAULT NULL,
  `eps_forward` text DEFAULT NULL,
  `eps_current_year` text DEFAULT NULL,
  `enterprise_to_revenue` text DEFAULT NULL,
  `enterprise_to_ebitda` text DEFAULT NULL,
  
  -- Financial statements
  `total_cash` text DEFAULT NULL,
  `total_debt` text DEFAULT NULL,
  `quick_ratio` text DEFAULT NULL,
  `current_ratio` text DEFAULT NULL,
  `total_revenue` text DEFAULT NULL,
  `debt_to_equity` text DEFAULT NULL,
  `revenue_per_share` text DEFAULT NULL,
  `return_on_assets` text DEFAULT NULL,
  `return_on_equity` text DEFAULT NULL,
  `gross_profits` text DEFAULT NULL,
  `free_cashflow` text DEFAULT NULL,
  `operating_cashflow` text DEFAULT NULL,
  `earnings_growth` text DEFAULT NULL,
  `revenue_growth` text DEFAULT NULL,
  `gross_margins` text DEFAULT NULL,
  `ebitda_margins` text DEFAULT NULL,
  `operating_margins` text DEFAULT NULL,
  `ebitda` text DEFAULT NULL,
  `total_cash_per_share` text DEFAULT NULL,
  
  -- Exchange information
  `exchange` varchar(50) DEFAULT NULL,
  `full_exchange_name` text DEFAULT NULL,
  `exchange_timezone_name` text DEFAULT NULL,
  `exchange_timezone_short_name` text DEFAULT NULL,
  `gmt_offset_milliseconds` text DEFAULT NULL,
  
  -- Market analysis
  `current_price` text DEFAULT NULL,
  `target_high_price` text DEFAULT NULL,
  `target_low_price` text DEFAULT NULL,
  `target_mean_price` text DEFAULT NULL,
  `recommendation_mean` text DEFAULT NULL,
  `recommendation_key` text DEFAULT NULL,
  `number_of_analyst_opinions` text DEFAULT NULL,
  `average_analyst_rating` text DEFAULT NULL,

  -- Trading status
  `market_state` text DEFAULT NULL,
  `quote_source_name` text DEFAULT NULL,
  `tradeable` text DEFAULT NULL,
  `crypto_tradeable` text DEFAULT NULL,
  `currency` text DEFAULT NULL,
  `exchange_data_delayed_by` text DEFAULT NULL,
  
  -- Other fields
  `executive_team` text DEFAULT NULL,
  `quote_type` text DEFAULT NULL,
  `language` text DEFAULT NULL,
  `region` text DEFAULT NULL,
  `market` text DEFAULT NULL,
  `type_disp` text DEFAULT NULL,
  `source_interval` text DEFAULT NULL,
  `message_board_id` text DEFAULT NULL,
  `custom_price_alert_confidence` text DEFAULT NULL,
  `corporate_actions` text DEFAULT NULL,
  `financial_currency` text DEFAULT NULL,
  
  -- Timestamps
  `last_fiscal_year_end` text DEFAULT NULL,
  `next_fiscal_year_end` text DEFAULT NULL,
  `most_recent_quarter` text DEFAULT NULL,
  `last_split_date` text DEFAULT NULL,
  `last_split_factor` text DEFAULT NULL,
  `first_trade_date_milliseconds` text DEFAULT NULL,
  `earnings_timestamp` text DEFAULT NULL,
  `earnings_timestamp_start` text DEFAULT NULL,
  `earnings_timestamp_end` text DEFAULT NULL,
  `earnings_call_timestamp_start` text DEFAULT NULL,
  `earnings_call_timestamp_end` text DEFAULT NULL,
  `is_earnings_date_estimate` text DEFAULT NULL,
  `esg_populated` text DEFAULT NULL,
  `has_pre_post_market_data` text DEFAULT NULL,
  
  PRIMARY KEY (`ticker`),
  KEY `ix_stocks_ticker` (`ticker`),
  KEY `ix_stocks_industry` (`industry`),
  KEY `ix_stocks_sector` (`sector`),
  KEY `ix_stocks_exchange` (`exchange`),
  KEY `ix_stocks_country` (`country`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;