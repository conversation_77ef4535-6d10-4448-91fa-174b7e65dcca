-- Create all_tickers_fr table for France stocks
CREATE TABLE IF NOT EXISTS all_tickers_fr (
    `symbol`       VARCHAR(32) PRIMARY KEY,
    `name`         VARCHAR(200),
    `country`      VARCHAR(50) DEFAULT 'France',
    `currency`     VARCHAR(10),
    `exchange`     VARCHAR(100),
    `isin`         VARCHAR(50),
    `logo`         VARCHAR(200),
    `created_at`   DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at`   DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY `idx_symbol` (`symbol`),
    KEY `idx_name` (`name`),
    KEY `idx_exchange` (`exchange`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
