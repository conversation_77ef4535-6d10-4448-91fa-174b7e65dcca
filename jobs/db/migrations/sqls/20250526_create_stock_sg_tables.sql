-- Create Singapore stock tables
-- Migration: 20250526_create_stock_sg_tables.sql

-- Create main stock table for Singapore
CREATE TABLE stock_sg (
    -- Primary identifiers
    symbol VARCHAR(32) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    last_updated D<PERSON>ETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Company information
    address1 TEXT,
    city TEXT,
    state TEXT,
    zip TEXT,
    country VARCHAR(50) DEFAULT 'Singapore',
    phone TEXT,
    website TEXT,

    -- Industry classification
    industry VARCHAR(100),
    industry_key TEXT,
    industry_disp TEXT,
    sector VARCHAR(100),
    sector_key TEXT,
    sector_disp TEXT,

    -- Company details
    long_business_summary TEXT,
    full_time_employees TEXT,
    company_officers TEXT,

    -- Risk metrics
    audit_risk TEXT,
    board_risk TEXT,
    compensation_risk TEXT,
    shareholder_rights_risk TEXT,
    overall_risk TEXT,

    -- Governance and IR
    governance_epoch_date TEXT,
    compensation_as_of_epoch_date TEXT,
    ir_website TEXT,

    -- Market data
    max_age TEXT,
    price_hint TEXT,
    previous_close TEXT,
    open_price TEXT,
    day_low TEXT,
    day_high TEXT,
    regular_market_previous_close TEXT,
    regular_market_open TEXT,
    regular_market_day_low TEXT,
    regular_market_day_high TEXT,
    regular_market_price TEXT,
    regular_market_volume TEXT,
    regular_market_change TEXT,
    regular_market_change_percent TEXT,
    regular_market_time TEXT,

    -- Pre/Post market
    pre_market_price TEXT,
    pre_market_change TEXT,
    pre_market_change_percent TEXT,
    post_market_time TEXT,

    -- Dividend information
    dividend_rate TEXT,
    dividend_yield TEXT,
    ex_dividend_date TEXT,
    dividend_date TEXT,
    payout_ratio TEXT,
    five_year_avg_dividend_yield TEXT,
    trailing_annual_dividend_rate TEXT,
    trailing_annual_dividend_yield TEXT,
    last_dividend_value TEXT,
    last_dividend_date TEXT,

    -- Trading metrics
    beta TEXT,
    volume TEXT,
    average_volume TEXT,
    average_daily_volume_10_day TEXT,
    bid TEXT,
    ask TEXT,
    bid_size TEXT,
    ask_size TEXT,

    -- Market cap and shares
    market_cap TEXT,
    enterprise_value TEXT,
    shares_outstanding TEXT,
    float_shares TEXT,
    implied_shares_outstanding TEXT,

    -- Price metrics
    fifty_two_week_low TEXT,
    fifty_two_week_high TEXT,
    fifty_two_week_low_change TEXT,
    fifty_two_week_low_change_percent TEXT,
    fifty_two_week_range TEXT,
    fifty_two_week_high_change TEXT,
    fifty_two_week_high_change_percent TEXT,
    fifty_two_week_change_percent TEXT,
    fifty_day_average TEXT,
    fifty_day_average_change TEXT,
    two_hundred_day_average TEXT,
    two_hundred_day_average_change TEXT,

    -- Financial ratios
    trailing_pe TEXT,
    forward_pe TEXT,
    price_to_book TEXT,
    price_to_sales_trailing_12_months TEXT,
    price_eps_current_year TEXT,

    -- Short interest
    shares_short TEXT,
    short_ratio TEXT,
    shares_short_prior_month TEXT,
    shares_short_previous_month_date TEXT,
    date_short_interest TEXT,
    shares_percent_shares_out TEXT,
    held_percent_insiders TEXT,
    held_percent_institutions TEXT,
    short_percent_of_float TEXT,

    -- Financial metrics
    book_value TEXT,
    earnings_quarterly_growth TEXT,
    net_income_to_common TEXT,
    trailing_eps TEXT,
    forward_eps TEXT,
    eps_trailing_twelve_months TEXT,
    eps_forward TEXT,
    eps_current_year TEXT,
    enterprise_to_revenue TEXT,
    enterprise_to_ebitda TEXT,

    -- Financial statements
    total_cash TEXT,
    total_debt TEXT,
    quick_ratio TEXT,
    current_ratio TEXT,
    total_revenue TEXT,
    debt_to_equity TEXT,
    revenue_per_share TEXT,
    return_on_assets TEXT,
    return_on_equity TEXT,
    gross_profits TEXT,
    free_cashflow TEXT,
    operating_cashflow TEXT,
    earnings_growth TEXT,
    revenue_growth TEXT,
    gross_margins TEXT,
    ebitda_margins TEXT,
    operating_margins TEXT,
    ebitda TEXT,
    total_cash_per_share TEXT,

    -- Exchange information
    exchange VARCHAR(50) DEFAULT 'SGX',
    full_exchange_name TEXT,
    exchange_timezone_name TEXT,
    exchange_timezone_short_name TEXT,
    gmt_offset_milliseconds TEXT,

    -- Market analysis
    current_price TEXT,
    target_high_price TEXT,
    target_low_price TEXT,
    target_mean_price TEXT,
    recommendation_mean TEXT,
    recommendation_key TEXT,
    number_of_analyst_opinions TEXT,
    average_analyst_rating TEXT,

    -- Trading status
    market_state TEXT,
    quote_source_name TEXT,
    tradeable TEXT,
    crypto_tradeable TEXT,
    currency TEXT,
    exchange_data_delayed_by TEXT,

    -- Other fields
    executive_team TEXT,
    quote_type TEXT,
    language TEXT,
    region TEXT,
    market TEXT,
    type_disp TEXT,
    source_interval TEXT,
    message_board_id TEXT,
    custom_price_alert_confidence TEXT,
    corporate_actions TEXT,
    financial_currency TEXT,

    -- Timestamps
    last_fiscal_year_end TEXT,
    next_fiscal_year_end TEXT,
    most_recent_quarter TEXT,
    last_split_date TEXT,
    last_split_factor TEXT,
    first_trade_date_milliseconds TEXT,
    earnings_timestamp TEXT,
    earnings_timestamp_start TEXT,
    earnings_timestamp_end TEXT,
    earnings_call_timestamp_start TEXT,
    earnings_call_timestamp_end TEXT,
    is_earnings_date_estimate TEXT,
    esg_populated TEXT,
    has_pre_post_market_data TEXT,

    INDEX idx_stock_sg_symbol (symbol),
    INDEX idx_stock_sg_country (country),
    INDEX idx_stock_sg_industry (industry),
    INDEX idx_stock_sg_sector (sector),
    INDEX idx_stock_sg_exchange (exchange)
);

-- Create price table for Singapore stocks
CREATE TABLE stock_sg_price (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    symbol VARCHAR(32) NOT NULL,
    date DATE NOT NULL,
    open FLOAT,
    high FLOAT,
    low FLOAT,
    close FLOAT,
    volume BIGINT,
    
    UNIQUE KEY uk_stock_sg_price_symbol_date (symbol, date),
    INDEX idx_stock_sg_price_symbol (symbol),
    INDEX idx_stock_sg_price_date (date),
    FOREIGN KEY (symbol) REFERENCES stock_sg(symbol) ON DELETE CASCADE
);

-- Create fundamentals table for Singapore stocks
CREATE TABLE stock_sg_fundamentals (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    symbol VARCHAR(32) NOT NULL,
    date DATE NOT NULL,
    dividend_rate FLOAT,
    dividend_yield FLOAT,
    pe_ratio FLOAT,
    eps FLOAT,
    beta FLOAT,
    fifty_two_week_high FLOAT,
    fifty_two_week_low FLOAT,
    
    UNIQUE KEY uk_stock_sg_fundamentals_symbol_date (symbol, date),
    INDEX idx_stock_sg_fundamentals_symbol (symbol),
    INDEX idx_stock_sg_fundamentals_date (date),
    FOREIGN KEY (symbol) REFERENCES stock_sg(symbol) ON DELETE CASCADE
);
