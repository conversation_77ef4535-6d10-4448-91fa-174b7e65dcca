CREATE TABLE sec_filings (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `cik` VARCHAR(32) NOT NULL,
    `ticker` VARCHAR(32),
    `accession_number` VARCHAR(32) NOT NULL,
    `filing_type` VARCHAR(32) NOT NULL,
    `filing_date` DATE NOT NULL,
    `report_date` DATE,
    `fiscal_year` INT,
    `fiscal_quarter` INT,
    `url` TEXT,
    `is_processed` BOOLEAN DEFAULT FALSE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE sec_filing_sections (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `filing_id` BIGINT NOT NULL,
    `section_name` <PERSON><PERSON><PERSON><PERSON>(256),
    `section_text` LONGTEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE sec_company_fact_metadata (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `cik` VARCHAR(32) NOT NULL,
    `ticker` VARCHAR(32),
    `entity_name` VARCHAR(256),
    `taxonomy_version` VARCHAR(32),
    `version` VARCHAR(32),
    `is_processed` BOOLEAN DEFAULT FALSE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE sec_company_facts (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `metadata_id` BIGINT NOT NULL,
    `taxonomy` VARCHAR(50) NOT NULL,
    `concept` VARCHAR(100) NOT NULL,
    `label` VARCHAR(255),
    `description` TEXT,
    `value` VARCHAR(255),
    `value_numeric` FLOAT,
    `unit` VARCHAR(50),
    `filing_date` DATE,
    `end_date` DATE,
    `start_date` DATE,
    `fiscal_year` INT,
    `fiscal_period` VARCHAR(20),
    `form` VARCHAR(20),
    `frame` VARCHAR(50),
    `accession_number` VARCHAR(20),
    `filing_url` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
