CREATE TABLE delist_tickers (
    ticker VARCHAR(32) PRIMARY KEY,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    KEY `idx_ticker` (`ticker`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

INSERT IGNORE INTO delist_tickers (ticker) VALUES 
('ABST'),
('ZYNE'),
('ZTEST'),
('ZINGU'),
('ZCZZT'),
('ZBZZT'),
('ZBZX'),
('ZAZZT'),
('XPVVV'),
('XPDBW'),
('XPDB'),
('XLYO'),
('WKEYV'),
('WENAW'),
('WENA'),
('VMWa'),
('VMGAW'),
('VCXAW'),
('VCXA'),
('UTAAU'),
('UTAA'),
('USXa'),
('UNAMQ'),
('UCBI'),
('TWKS'),
('TWCBW'),
('TWCBU'),
('TWCB'),
('TLGYW'),
('THRN'),
('THCHW'),
('TBSAW'),
('TBCa'),
('TANNZ'),
('TANNL'),
('TANNI'),
('SURF'),
('SUNSV'),
('SPWR'),
('SPPI'),
('SOLOW'),
('SOLO'),
('SNREV'),
('SLVRU'),
('SLGC'),
('SKYHWS'),
('SIXa'),
('SGTX'),
('SGDVV'),
('SGBXV'),
('SDACU'),
('SCUa'),
('SCMAW'),
('SCMAU'),
('SASI'),
('SAMAU'),
('ROSS'),
('ROCAU'),
('ROCa'),
('RNLX'),
('RNERU'),
('REUN'),
('RCMa'),
('RCLFU'),
('QTNT'),
('QOMOR'),
('PXDT'),
('PTWOU'),
('PTHRW'),
('PTHR'),
('PSTX'),
('PRDS'),
('PNSTWS'),
('PLMIW'),
('PLMIU'),
('PLMI'),
('PIRS'),
('PHXM'),
('PCCTU'),
('PCCT'),
('PARAP'),
('OXUSU'),
('OTMO'),
('OSGa'),
('NXT(EXP20091224)'),
('NTBL'),
('NOVVW'),
('NOVVU'),
('NOVVR'),
('NOVV'),
('NGIO'),
('NEEPN'),
('MURFW'),
('MTCR'),
('MTAC'),
('MPRAW'),
('MPRAU'),
('MPRA'),
('MITAW'),
('MITAU'),
('MITA'),
('MGTA'),
('METCV'),
('METBV'),
('MEOA'),
('MDRRP'),
('MCACW'),
('MCACU'),
('MCACR'),
('LVACW'),
('LMNL'),
('LGVNV'),
('LCAAW'),
('LCAAU'),
('LCAA'),
('LBYAV'),
('LBPH'),
('LBBBW'),
('LBBBU'),
('LBBB'),
('KDNY'),
('JUGGW'),
('JUGG'),
('JHAA'),
('IVCPU'),
('IVCBW'),
('ITIa'),
('IQMDW'),
('INXBV'),
('INPX'),
('INBXV'),
('IMPPV'),
('IMACW'),
('ILMNV'),
('IIVI'),
('HTan'),
('HRYU'),
('HPLTW'),
('HPLTU'),
('HPLT'),
('HMACW'),
('HMACU'),
('HMACR'),
('HMAC'),
('HAIAW'),
('GSRMW'),
('GSRM'),
('GSDWW'),
('GRNAW'),
('GRNA'),
('GRIN'),
('GRCL'),
('GRALV'),
('GIWWW'),
('GENQU'),
('FXCOW'),
('FXCOR'),
('FXCO'),
('FTPAW'),
('FSNB'),
('FRRSF'),
('FRGAP'),
('FRBNW'),
('FRBNU'),
('FRBN'),
('FLJa'),
('FICVU'),
('FICV'),
('EOCW'),
('ENTXW'),
('ENCP'),
('EMBKW'),
('EMBK'),
('DSKE'),
('DNAD'),
('DNAB'),
('DISTU'),
('DICE'),
('DHCA'),
('DFFN'),
('DCPH'),
('DBTX'),
('CVIIW'),
('CVIIU'),
('CVII'),
('CSTA'),
('CPTK'),
('CPLP'),
('CPAAU'),
('CPAA'),
('CNFRL'),
('CITEW'),
('CISSV'),
('CIIGU'),
('CFMS'),
('CCVa'),
('CCTSW'),
('CCAIU'),
('CBXa'),
('BWCAU'),
('BTMDW'),
('BRLIW'),
('BRLIU'),
('BRLIR'),
('BRLI'),
('BODY'),
('BOAC'),
('BMAQU'),
('BMAC'),
('BLEUR'),
('BIOSU'),
('BGRYW'),
('BGRY'),
('BFXa'),
('BC/PC'),
('BC/PB'),
('BC/PA'),
('BBLN'),
('AXNX'),
('AVACU'),
('AURC'),
('ATIF'),
('ARTEU'),
('ARTE'),
('ARGUW'),
('ARCKW'),
('ARBGU'),
('AQNA'),
('APRN'),
('APMIU'),
('APGB'),
('AMRS'),
('AMEH'),
('AMAOW'),
('AMAO'),
('ALTIW'),
('ALORW'),
('ALORU'),
('ALOR'),
('ALKSV'),
('ADOCR'),
('ACST'),
('HAIAW'),
('GSRMW'),
('GSRM'),
('GSDWW'),
('GRNAW'),
('GRNA'),
('GRIN'),
('GRCL'),
('GRALV'),
('GIWWW'),
('FXCOW'),
('FXCOR'),
('FXCO'),
('FRRSF'),
('FRGAP'),
('FRBNW'),
('FRBNU'),
('FRBN');