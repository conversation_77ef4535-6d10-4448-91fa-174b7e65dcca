-- Create all_tickers_kr table for Korea tickers
CREATE TABLE IF NOT EXISTS all_tickers_kr (
    symbol VARCHAR(20) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    country VARCHAR(50) NOT NULL DEFAULT 'Korea',
    currency VARCHAR(10),
    exchange VARCHAR(100),
    isin VARCHAR(20),
    logo VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_exchange (exchange),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
