-- Migration to rename ts_code to symbol in Hong Kong tables
-- This migration makes the Hong Kong tables consistent with other country tables

-- Step 1: Drop unique constraints that reference ts_code
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_price'
     AND INDEX_NAME = '_ts_code_date_uc') > 0,
    'ALTER TABLE stock_hk_price DROP INDEX _ts_code_date_uc',
    'SELECT "Index _ts_code_date_uc does not exist on stock_hk_price"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_fundamentals'
     AND INDEX_NAME = '_ts_code_date_uc') > 0,
    'ALTER TABLE stock_hk_fundamentals DROP INDEX _ts_code_date_uc',
    'SELECT "Index _ts_code_date_uc does not exist on stock_hk_fundamentals"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 2: Drop indexes that reference ts_code (check if they exist)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'all_tickers_hk'
     AND INDEX_NAME = 'idx_ts_code') > 0,
    'ALTER TABLE all_tickers_hk DROP INDEX idx_ts_code',
    'SELECT "No idx_ts_code index to drop"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk'
     AND INDEX_NAME = 'ix_stock_hk_ts_code') > 0,
    'ALTER TABLE stock_hk DROP INDEX ix_stock_hk_ts_code',
    'SELECT "No ix_stock_hk_ts_code index to drop"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_price'
     AND INDEX_NAME = 'ix_stock_hk_price_ts_code') > 0,
    'ALTER TABLE stock_hk_price DROP INDEX ix_stock_hk_price_ts_code',
    'SELECT "No ix_stock_hk_price_ts_code index to drop"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_fundamentals'
     AND INDEX_NAME = 'ix_stock_hk_fundamentals_ts_code') > 0,
    'ALTER TABLE stock_hk_fundamentals DROP INDEX ix_stock_hk_fundamentals_ts_code',
    'SELECT "No ix_stock_hk_fundamentals_ts_code index to drop"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 3: Rename ts_code to symbol in all_tickers_hk table (only if ts_code exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'all_tickers_hk'
     AND COLUMN_NAME = 'ts_code') > 0,
    'ALTER TABLE all_tickers_hk CHANGE COLUMN ts_code symbol VARCHAR(32)',
    'SELECT "Column ts_code does not exist in all_tickers_hk"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 4: Rename ts_code to symbol in stock_hk table (only if ts_code exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk'
     AND COLUMN_NAME = 'ts_code') > 0,
    'ALTER TABLE stock_hk CHANGE COLUMN ts_code symbol VARCHAR(32)',
    'SELECT "Column ts_code does not exist in stock_hk"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 5: Rename ts_code to symbol in stock_hk_price table (only if ts_code exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_price'
     AND COLUMN_NAME = 'ts_code') > 0,
    'ALTER TABLE stock_hk_price CHANGE COLUMN ts_code symbol VARCHAR(32)',
    'SELECT "Column ts_code does not exist in stock_hk_price"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 6: Rename ts_code to symbol in stock_hk_fundamentals table (only if ts_code exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_fundamentals'
     AND COLUMN_NAME = 'ts_code') > 0,
    'ALTER TABLE stock_hk_fundamentals CHANGE COLUMN ts_code symbol VARCHAR(32)',
    'SELECT "Column ts_code does not exist in stock_hk_fundamentals"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 7: Recreate indexes with new column name (only if they don't exist)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'all_tickers_hk'
     AND INDEX_NAME = 'idx_symbol') = 0,
    'ALTER TABLE all_tickers_hk ADD INDEX idx_symbol (symbol)',
    'SELECT "Index idx_symbol already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk'
     AND INDEX_NAME = 'ix_stock_hk_symbol') = 0,
    'ALTER TABLE stock_hk ADD INDEX ix_stock_hk_symbol (symbol)',
    'SELECT "Index ix_stock_hk_symbol already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_price'
     AND INDEX_NAME = 'ix_stock_hk_price_symbol') = 0,
    'ALTER TABLE stock_hk_price ADD INDEX ix_stock_hk_price_symbol (symbol)',
    'SELECT "Index ix_stock_hk_price_symbol already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_fundamentals'
     AND INDEX_NAME = 'ix_stock_hk_fundamentals_symbol') = 0,
    'ALTER TABLE stock_hk_fundamentals ADD INDEX ix_stock_hk_fundamentals_symbol (symbol)',
    'SELECT "Index ix_stock_hk_fundamentals_symbol already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 8: Recreate foreign key constraints with new column name (only if they don't exist)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_price'
     AND CONSTRAINT_NAME = 'fk_stock_hk_price_symbol') = 0,
    'ALTER TABLE stock_hk_price ADD CONSTRAINT fk_stock_hk_price_symbol FOREIGN KEY (symbol) REFERENCES stock_hk(symbol)',
    'SELECT "Foreign key fk_stock_hk_price_symbol already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_fundamentals'
     AND CONSTRAINT_NAME = 'fk_stock_hk_fundamentals_symbol') = 0,
    'ALTER TABLE stock_hk_fundamentals ADD CONSTRAINT fk_stock_hk_fundamentals_symbol FOREIGN KEY (symbol) REFERENCES stock_hk(symbol)',
    'SELECT "Foreign key fk_stock_hk_fundamentals_symbol already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 9: Recreate unique constraints with new column name
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_price'
     AND INDEX_NAME = '_ts_code_date_uc') > 0,
    'ALTER TABLE stock_hk_price DROP INDEX _ts_code_date_uc',
    'SELECT "Index _ts_code_date_uc does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_price'
     AND INDEX_NAME = '_symbol_date_uc') = 0,
    'ALTER TABLE stock_hk_price ADD UNIQUE INDEX _symbol_date_uc (symbol, date)',
    'SELECT "Index _symbol_date_uc already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_fundamentals'
     AND INDEX_NAME = '_ts_code_date_uc') > 0,
    'ALTER TABLE stock_hk_fundamentals DROP INDEX _ts_code_date_uc',
    'SELECT "Index _ts_code_date_uc does not exist"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'stock_hk_fundamentals'
     AND INDEX_NAME = '_symbol_date_uc') = 0,
    'ALTER TABLE stock_hk_fundamentals ADD UNIQUE INDEX _symbol_date_uc (symbol, date)',
    'SELECT "Index _symbol_date_uc already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
