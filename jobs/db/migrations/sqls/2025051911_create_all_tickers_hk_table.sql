-- Create all_tickers_hk table for Hong Kong stocks
CREATE TABLE IF NOT EXISTS all_tickers_hk (
    `ts_code`      VARCHAR(32) PRIMARY KEY,
    `name`         <PERSON><PERSON><PERSON><PERSON>(200),
    `fullname`     <PERSON><PERSON><PERSON><PERSON>(255),
    `enname`       <PERSON><PERSON><PERSON><PERSON>(255),
    `cn_spell`     VA<PERSON><PERSON><PERSON>(100),
    `market`       VARCHAR(100),
    `list_status`  VARCHAR(10),
    `list_date`    VARCHAR(20),
    `delist_date`  VA<PERSON><PERSON><PERSON>(20),
    `trade_unit`   FLOAT,
    `isin`         VARCHAR(50),
    `curr_type`    VARCHAR(10),
    `logo`         VARCHA<PERSON>(200),
    `created_at`   DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at`   DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY `idx_ts_code` (`ts_code`),
    KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
