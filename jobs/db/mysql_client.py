"""
MySQL client for connecting to the MySQL database.
"""

import logging
from typing import Optional, Dict
from sqlalchemy import create_engine, Engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError, OperationalError
from config.settings import settings
from utils.logging_utils import setup_logging
from .schema import FinancialCalendarEvent

# Configure logging
logger = setup_logging(enable_colors=False)

# Create engine with pool settings from configuration
engine = create_engine(
    settings.MYSQL_URI,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    pool_timeout=settings.DB_POOL_TIMEOUT,
    pool_recycle=settings.DB_POOL_RECYCLE,
    pool_pre_ping=True  # Check connection before using
)

class MySQLClient:
    """
    MySQL client for connecting to the MySQL database.
    """
    
    _engine: Optional[Engine] = None
    _session_factory = None
    
    @classmethod
    def get_engine(cls) -> Engine:
        """
        Get the SQLAlchemy engine instance.
        
        Returns:
            Engine: The SQLAlchemy engine
        """
        if cls._engine is None:
            logger.info(f"Initializing MySQL engine")
            try:
                # First create engine without database name
                temp_engine = create_engine(settings.MYSQL_URI)
                
                # Create database if it doesn't exist with utf8mb4 character set and utf8mb4_0900_ai_ci collation
                with temp_engine.connect() as connection:
                    connection.execute(text(f"CREATE DATABASE IF NOT EXISTS {settings.MYSQL_DATABASE} "
                                          f"CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci"))
                    connection.execute(text(f"USE {settings.MYSQL_DATABASE}"))
                
                # Now create the real engine with the database and proper character set configurations
                cls._engine = create_engine(
                    settings.MYSQL_URI,
                    pool_size=settings.DB_POOL_SIZE,
                    max_overflow=settings.DB_MAX_OVERFLOW,
                    pool_timeout=settings.DB_POOL_TIMEOUT,
                    pool_recycle=settings.DB_POOL_RECYCLE,
                    pool_pre_ping=True,
                    connect_args={
                        'charset': 'utf8mb4',
                        'collation': 'utf8mb4_0900_ai_ci'
                    }
                )
                logger.info("MySQL engine initialized successfully with utf8mb4 character set")
            except SQLAlchemyError as e:
                logger.error(f"Error initializing MySQL engine: {str(e)}")
                raise
        
        return cls._engine

    @classmethod
    def get_session_factory(cls):
        """
        Get the SQLAlchemy session factory.
        
        Returns:
            sessionmaker: The SQLAlchemy session factory
        """
        if cls._session_factory is None:
            engine = cls.get_engine()
            cls._session_factory = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=engine
            )
            logger.debug("Session factory created")
        return cls._session_factory

    @classmethod
    def get_session(cls) -> Session:
        """
        Get a new SQLAlchemy session.
        
        Returns:
            Session: A new SQLAlchemy session
        """
        return cls.get_session_factory()()

    @classmethod
    def create_tables(cls, base):
        """
        Create all tables defined in the SQLAlchemy models.
        
        Args:
            base: The SQLAlchemy declarative base
        """
        logger.info("Creating database tables if they don't exist")
        try:
            engine = cls.get_engine()
            base.metadata.create_all(bind=engine)
            logger.info("Database tables created successfully")
        except SQLAlchemyError as e:
            logger.error(f"Error creating database tables: {str(e)}")
            raise

    def insert_financial_event(self, event: Dict):
        """
        Insert financial calendar event into database
        
        Args:
            event: Dictionary containing event data
        """
        with SessionContext() as session:
            try:
                financial_event = FinancialCalendarEvent(
                    event_name=event["event_name"],
                    event_type=event["event_type"],
                    country=event["country"],
                    date=event["date"],
                    time=event.get("time"),
                    actual=event.get("actual"),
                    forecast=event.get("forecast"),
                    previous=event.get("previous"),
                    impact=event.get("impact"),
                    description=event.get("description"),
                    is_released=event.get("is_released", False)
                )
                session.add(financial_event)
                session.commit()
                logger.debug(f"Inserted financial event: {event['event_name']}")
            except Exception as e:
                logger.error(f"Error inserting financial event: {str(e)}")
                session.rollback()
                raise

# Create a context manager for database sessions
class SessionContext:
    """
    Context manager for database sessions.
    """
    def __init__(self):
        self.session = None

    def __enter__(self) -> Session:
        self.session = MySQLClient.get_session()
        return self.session

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            if exc_type is not None:
                # An exception occurred, rollback the transaction
                logger.debug("Rolling back transaction due to exception")
                self.session.rollback()
            else:
                # No exception, commit the transaction
                try:
                    logger.debug("Committing transaction")
                    self.session.commit()
                except SQLAlchemyError as e:
                    logger.error(f"Error committing transaction: {str(e)}")
                    self.session.rollback()
                    raise
            # Close the session
            self.session.close()

def get_db_session() -> Session:
    """Get a database session."""
    return MySQLClient.get_session()
