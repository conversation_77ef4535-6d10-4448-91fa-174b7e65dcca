import os
from fastapi import FastAPI, Query, Depends, HTTPException
from dotenv import load_dotenv
from openai import OpenAI
from config.settings import settings
import uvicorn
from pydantic import BaseModel
from typing import List, Optional
from google.cloud import storage
import uuid
import redis
import hashlib
import time

# Load environment variables
load_dotenv()

app = FastAPI()

class RedisLock:
    def __init__(self, redis_client, lock_name, expire_time=600):
        self.redis = redis_client
        self.lock_name = lock_name
        self.expire_time = expire_time
        self.lock_id = str(uuid.uuid4())

    def acquire(self) -> bool:
        return self.redis.set(
            self.lock_name,
            self.lock_id,
            nx=True,
            ex=self.expire_time
        )

    def release(self) -> bool:
        # Only release if we own the lock
        script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        return bool(self.redis.eval(script, 1, self.lock_name, self.lock_id))

class VectorJob:
    def __init__(self, settings: None):
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
        self.vector_name = settings.VECTOR_NAME
        self.storage_client = storage.Client()
        # Initialize Redis client
        self.redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=0,
            decode_responses=True
        )
        # Check Redis connection
        self.check_redis_connection()

    def check_redis_connection(self):
        """Check if Redis connection is available"""
        try:
            if not self.redis_client.ping():
                raise ConnectionError("Redis ping failed")
            print("Redis connection successful")
        except redis.ConnectionError as e:
            print(f"Failed to connect to Redis: {str(e)}")
            raise ConnectionError(f"Redis connection failed: {str(e)}")
        except Exception as e:
            print(f"Unexpected error connecting to Redis: {str(e)}")
            raise
    
    def create_vector(self, name: str) -> str:
        new_vector = self.client.vector_stores.create(
            name=name,
        )
        return new_vector.id

    def get_vector_id(self, name: str) -> str:
        # List all vector stores and find the one with matching name
        vectors = self.client.vector_stores.list(limit=100)
        for vector in vectors.data:
            if vector.name == name:
                return vector.id
        
        # If not found, create a new vector store
        return self.create_vector(name)

    def list_files(self, vector_id: str) -> List[dict]:
        # List all vector stores and find the one with matching name
        files = self.client.vector_stores.files.list(vector_id, limit=100, filter='completed')
        res = []
        for file in files:
            if file.id == 'file-Cx21cKkc9Xo2YAFuFqSE3T':
                pass
            res.append({
                'id': file.id,
                'attributes': file.attributes
            })
        return res

    def remove_file(self, vector_id: str, file_id: str) -> bool:
        """Remove a file from vector store by vector_id and file_id"""
        try:
            self.client.vector_stores.files.delete(
                file_id=file_id,
                vector_store_id=vector_id
            )
            print(f"Remove vector {vector_id} file {file_id} success")
            return True
        except Exception as e:
            print(f"Failed to remove vector {vector_id} file {file_id}: {str(e)}")
            return False

    def upload_file(self, vector_id: str, filename: str, original_name: str, session_id: str) -> str:
        file_id = None
        file_path = os.path.abspath(filename)
        
        if not os.path.exists(file_path):
            print(f"[{session_id}] File not found: {file_path}")
            raise FileNotFoundError(f"File not found: {file_path}")

        # Create temp directory if it doesn't exist
        temp_dir = os.path.join('/tmp', session_id)
        os.makedirs(temp_dir, exist_ok=True)
        
        # Create symlink with original name
        original_basename = os.path.basename(original_name)
        symlink_path = os.path.join(temp_dir, original_basename)
        try:
            if os.path.exists(symlink_path):
                os.unlink(symlink_path)
            os.symlink(file_path, symlink_path)
            
            print(f"[{session_id}] Starting file upload to vector store")
            # Upload to vector store using symlink
            with open(symlink_path, "rb") as vector_file:
                file = self.client.vector_stores.files.upload(
                    vector_store_id=vector_id,
                    file=vector_file,
                )
                file_id = file.id
                print(f"[{session_id}] File uploaded successfully with ID: {file_id}")
        finally:
            # Clean up symlink and temp directory
            if os.path.exists(symlink_path):
                os.unlink(symlink_path)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)
            
        print(f"[{session_id}] Updating file {file_id} attributes")
        # Update file attributes with retry
        max_retries = 10
        retry_delay = 5  # seconds
        
        for attempt in range(max_retries):
            try:
                result = self.client.vector_stores.files.update(
                    file_id, 
                    vector_store_id=vector_id, 
                    attributes={
                        "name": original_name
                    }
                )
                if result.attributes and len(result.attributes) > 0:
                    break
            except Exception as e:
                if attempt == max_retries - 1:  # Last attempt
                    break
                print(f"[{session_id}] Attempt {attempt + 1} failed, retrying in {retry_delay}s: {str(e)}")
                time.sleep(retry_delay)
                continue

        if not result.attributes or len(result.attributes) == 0:
            print(f"[{session_id}] Failed to update file {file_id} attributes, removing file")
            ok = self.remove_file(vector_id, file_id)
            if ok:
                print(f"[{session_id}] Remove file {file_id} success")
            else:
                print(f"[{session_id}] Remove file {file_id} failed")
            raise Exception(f"Failed to update file attributes for file ID: {file_id}")
        print(f"[{session_id}] File {file_id} attributes {result.attributes} updated successfully")
        return file_id

    def check_bucket_exists(self, bucket_name: str) -> bool:
        """Check if the bucket exists"""
        try:
            self.storage_client.get_bucket(bucket_name)
            return True
        except Exception:
            return False

    def download_from_bucket(self, bucket_name: str, object_name: str, session: str = "no-session") -> tuple[str, Optional[str]]:
        """Download file from GCS bucket and return tuple of (local file path, message)"""
        print(f"[{session}] Checking bucket: {bucket_name}")
        if not self.check_bucket_exists(bucket_name):
            raise ValueError(f"Bucket does not exist: {bucket_name}")
            
        print(f"[{session}] Downloading file from bucket: {bucket_name}, object: {object_name}")
        bucket = self.storage_client.bucket(bucket_name)
        blob = bucket.blob(object_name)
        
        if not blob.exists():
            raise FileNotFoundError(f"Object not found: {object_name}")
        
        # Reload blob to get latest metadata
        blob.reload()
        print(f"[{session}] Blob metadata: {blob.metadata}")
        
        if blob.metadata and 'vector_file_id' in blob.metadata:
            return None, f"Object already processed: {object_name}"

        # Create file path in /tmp with unique name
        file_extension = os.path.splitext(object_name)[1]
        temp_filename = f"{uuid.uuid4()}{file_extension}"
        temp_path = os.path.join('/tmp', temp_filename)
        
        try:
            # Download to temp file
            blob.download_to_filename(temp_path)
            print(f"[{session}] File downloaded successfully to: {temp_path}")
            return temp_path, None
        except Exception as e:
            if os.path.exists(temp_path):
                os.unlink(temp_path)  # Clean up temp file on error
            print(f"[{session}] Failed to download file: {str(e)}")
            raise Exception(f"Failed to download file: {str(e)}")
        
    def get_metadata_from_bucket(self, bucket_name: str, object_name: str) -> dict:
        print(f"Checking bucket: {bucket_name}")
        if not self.check_bucket_exists(bucket_name):
            raise ValueError(f"Bucket does not exist: {bucket_name}")
            
        bucket = self.storage_client.bucket(bucket_name)
        blob = bucket.blob(object_name)
        
        if not blob.exists():
            raise FileNotFoundError(f"Object not found: {object_name}")
        
        blob.reload()
        return blob.metadata

    def update_blob_metadata(self, blob, metadata: dict, session_id: str, max_retries: int = 10, retry_delay: int = 5) -> bool:
        """Update blob metadata with retry logic"""
        for attempt in range(max_retries):
            try:
                blob.metadata = metadata
                blob.patch()
                
                # Verify metadata was updated correctly
                blob.reload()
                if all(blob.metadata.get(k) == v for k, v in metadata.items()):
                    return True
                    
            except Exception as e:
                if attempt == max_retries - 1:  # Last attempt
                    print(f"[{session_id}] Failed to update metadata after {max_retries} attempts: {str(e)}")
                    return False
                print(f"[{session_id}] Metadata update attempt {attempt + 1} failed, retrying in {retry_delay}s: {str(e)}")
                time.sleep(retry_delay)
        return False

class UploadRequest(BaseModel):
    class Config:
        extra = "allow"
    id: Optional[str] = None
    bucket: Optional[str] = None
    name: Optional[str] = None

class RemoveRequest(BaseModel):
    class Config:
        extra = "allow"
    metadata: Optional[dict] = None

class ListRequest(BaseModel):
    only_size: bool = Query(default=False, description="Return only the size of the list")

def run():
    try:
        vector_job = VectorJob(settings)
    except ConnectionError as e:
        print(f"Failed to initialize VectorJob: {str(e)}")
        return
    except Exception as e:
        print(f"Unexpected error during initialization: {str(e)}")
        return

    @app.post("/upload")
    async def upload(request: UploadRequest):
        session_id = str(uuid.uuid4())
        temp_file = None
        
        # Add check for object name ending with '/'
        if request.name and request.name.endswith('/'):
            print(f"[{session_id}] Skipping directory upload: {request.name}")
            return {"status": "success", "message": "Skipping directory upload", "session": session_id}
            
        lock_id = hashlib.md5(request.id.encode()).hexdigest()
        lock = RedisLock(vector_job.redis_client, f"run.job.vector.upload_lock.{lock_id}")

        if not lock.acquire():
            print(f"[{session_id}] Upload rejected - another upload in progress")
            raise HTTPException(status_code=409, detail="Another upload in progress")

        try:
            print(f"[{session_id}] Received upload request with data: {request.model_dump()}")
            if not request.bucket or not request.name:
                print(f"[{session_id}] Error: bucket and name are required")
                return {"status": "error", "message": "bucket and name are required", "session": session_id}

            # Download file from GCS
            print(f"[{session_id}] Downloading file from GCS")
            temp_file, message = vector_job.download_from_bucket(request.bucket, request.name, session_id)
            
            if message:  # File already processed
                print(f"[{session_id}] {message}")
                return {"status": "success", "message": message, "session": session_id}

            # Process the file
            vector_id = vector_job.get_vector_id(vector_job.vector_name)
            print(f"[{session_id}] Using vector store ID: {vector_id}")
            file_id = vector_job.upload_file(vector_id, temp_file, request.bucket+'/'+request.name, session_id)

            # Update GCS object metadata
            bucket = vector_job.storage_client.bucket(request.bucket)
            blob = bucket.blob(request.name)
            metadata = {
                'vector_file_id': file_id,
                'vector_id': vector_id,
                'vector_name': vector_job.vector_name,
                'session': session_id
            }
            
            if not vector_job.update_blob_metadata(blob, metadata, session_id):
                print(f"[{session_id}] Failed to update storage metadata, cleaning up vector file")
                ok = vector_job.remove_file(vector_id, file_id)
                if ok:
                    print(f"[{session_id}] Remove file {file_id} success")
                else:
                    print(f"[{session_id}] Remove file {file_id} failed")
                raise Exception(f"Failed to update storage metadata for object: {request.name}")

            # Verify metadata
            new_metadata = vector_job.get_metadata_from_bucket(request.bucket, request.name)
            if not new_metadata or 'vector_id' not in new_metadata or 'vector_file_id' not in new_metadata or 'session' not in new_metadata or new_metadata['session'] != session_id:
                ok = vector_job.remove_file(vector_id, file_id)
                if ok:
                    print(f"[{session_id}] Remove file {file_id} success")
                else:
                    print(f"[{session_id}] Remove file {file_id} failed")
                raise Exception(f"Failed to retrieve metadata for object: {request.name}")

            return {"status": "success", "metadata": metadata, "session": session_id}

        except Exception as e:
            print(f"[{session_id}] upload failed: {str(e)}")
            return {"status": "error", "message": str(e), "session": session_id}

        finally:
            # Clean up temp file
            if temp_file and os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                    print(f"[{session_id}] Temporary file cleaned up: {temp_file}")
                except Exception as e:
                    print(f"[{session_id}] Failed to cleanup temp file: {str(e)}")

            # Release the lock
            lock.release()

    @app.post("/remove")
    async def remove(request: RemoveRequest):
        try:
            print(f"Received remove request with data: {request.model_dump()}")
            if not request.metadata or 'vector_id' not in request.metadata or 'vector_file_id' not in request.metadata:
                return {"status": "error", "message": "metadata.vector_id and metadata.vector_file_id are required"}

            if vector_job.remove_file(request.metadata['vector_id'], request.metadata['vector_file_id']):
                return {"status": "success"}
            else:
                return {"status": "error", "message": "Failed to remove file"}
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    @app.get("/list")
    async def list(params: ListRequest = Depends()):
        vector_id = vector_job.get_vector_id(vector_job.vector_name)
        files = vector_job.list_files(vector_id)
        
        if params.only_size:
            return {
                "status": "success", 
                "vector_id": vector_id, 
                "vector_name": vector_job.vector_name, 
                "size": len(files)
            }
        
        return {
            "status": "success", 
            "vector_id": vector_id, 
            "vector_name": vector_job.vector_name, 
            "size": len(files),
            "files": files,
        }

    @app.delete("/clear")
    async def clear():
        vector_id = vector_job.get_vector_id(vector_job.vector_name)
        files = vector_job.list_files(vector_id)
        for file in files:
            vector_job.remove_file(vector_id, file['id'])
        return {"status": "success"}

    uvicorn.run(app, host="0.0.0.0", port=8080)
