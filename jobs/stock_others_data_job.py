"""
Stock Others Data Job

This job fetches comprehensive stock data, price history, and fundamental data for various countries using yfinance
and stores it in country-specific tables.
Supports: Singapore, Japan, France, Germany, Korea, Malaysia, United Kingdom, Thailand, Canada, Taiwan

Features:
- Fetches comprehensive stock information (company details, financials, market data)
- Fetches historical price data (OHLCV) for configurable number of days
- Fetches fundamental data (dividend rate/yield, P/E ratio, EPS, beta, 52-week high/low)
- Handles multiple exchange suffixes for countries like Korea and Taiwan
- Includes retry mechanisms for rate limiting and error handling
- Supports full refresh mode to update existing records
- Stores data in country-specific tables (e.g., stock_sg, stock_sg_price, stock_sg_fundamentals)

Usage:
    python3 run_stock_others_data_job.py <country> [--price-days DAYS] [--full-refresh]

Examples:
    python3 run_stock_others_data_job.py Singapore
    python3 run_stock_others_data_job.py Japan --price-days 60
    python3 run_stock_others_data_job.py Korea --full-refresh
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
from sqlalchemy import text
import time

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.db.mysql_client import MySQLClient, get_db_session
from utils.yfinance_wrapper import YFinanceWrapper

# Country configuration mapping
COUNTRY_CONFIG = {
    'Singapore': {
        'table_prefix': 'sg',
        'exchange_suffix': '.SI',
        'default_exchange': 'SGX',
        'currency': 'SGD',
        'timezone': 'Asia/Singapore'
    },
    'Japan': {
        'table_prefix': 'jp',
        'exchange_suffix': '.T',
        'default_exchange': 'TSE',
        'currency': 'JPY',
        'timezone': 'Asia/Tokyo'
    },
    'France': {
        'table_prefix': 'fr',
        'exchange_suffix': '.PA',
        'default_exchange': 'EPA',
        'currency': 'EUR',
        'timezone': 'Europe/Paris'
    },
    'Germany': {
        'table_prefix': 'ge',
        'exchange_suffix': '.DE',
        'default_exchange': 'XETRA',
        'currency': 'EUR',
        'timezone': 'Europe/Berlin'
    },
    'Korea': {
        'table_prefix': 'kr',
        'exchange_suffix': '',  # Will be determined dynamically between .KS and .KQ
        'multiple_suffixes': ['.KS', '.KQ'],  # Korea has two possible exchange suffixes
        'default_exchange': 'KRX',
        'currency': 'KRW',
        'timezone': 'Asia/Seoul'
    },
    'Malaysia': {
        'table_prefix': 'my',
        'exchange_suffix': '.KL',
        'default_exchange': 'KLSE',
        'currency': 'MYR',
        'timezone': 'Asia/Kuala_Lumpur'
    },
    'United Kingdom': {
        'table_prefix': 'uk',
        'exchange_suffix': '.L',
        'default_exchange': 'LSE',
        'currency': 'GBP',
        'timezone': 'Europe/London'
    },
    'Thailand': {
        'table_prefix': 'th',
        'exchange_suffix': '.BK',
        'default_exchange': 'SET',
        'currency': 'THB',
        'timezone': 'Asia/Bangkok'
    },
    'Canada': {
        'table_prefix': 'ca',
        'exchange_suffix': '.TO',
        'default_exchange': 'TSX',
        'currency': 'CAD',
        'timezone': 'America/Toronto'
    },
    'Taiwan': {
        'table_prefix': 'tw',
        'exchange_suffix': '',  # Will be determined dynamically between .TW and .TWO
        'multiple_suffixes': ['.TW', '.TWO'],  # Taiwan has two possible exchange suffixes
        'default_exchange': 'TWSE',
        'currency': 'TWD',
        'timezone': 'Asia/Taipei'
    }
}
# Retry configuration
MAX_RETRIES = 3
RETRY_DELAYS = [60, 120, 300]  # Exponential backoff: 1m, 2m, 5m
RATE_LIMIT_KEYWORDS = [
    "Too Many Requests",
    "Rate limited",
    "rate limit",
    "429",
    "quota exceeded"
]

###

class StockOthersDataJob:
    """Job to fetch and store stock data for various countries"""

    def __init__(self, country: str, price_days: int = 30, full_refresh: bool = False):
        """
        Initialize the job for a specific country

        Args:
            country: Country name (e.g., 'Singapore', 'Japan', etc.)
            price_days: Number of days of price history to fetch (default: 30)
            full_refresh: Whether to refresh all data including existing records (default: False)
        """
        if country not in COUNTRY_CONFIG:
            raise ValueError(f"Unsupported country: {country}. Supported countries: {list(COUNTRY_CONFIG.keys())}")

        self.country = country
        self.config = COUNTRY_CONFIG[country]
        self.table_prefix = self.config['table_prefix']
        self.price_days = price_days
        self.full_refresh = full_refresh

        # Setup logging
        self.logger = logging.getLogger(__name__)

        # Initialize database client
        self.db_client = MySQLClient()

        # Initialize yfinance wrapper
        self.yf_wrapper = YFinanceWrapper()

        self.logger.info(f"Initialized StockOthersDataJob for {country} (price_days={price_days}, full_refresh={full_refresh})")

    def _is_rate_limit_error(self, error_message: str) -> bool:
        """Check if error is related to rate limiting"""
        error_str = str(error_message).lower()
        return any(keyword.lower() in error_str for keyword in RATE_LIMIT_KEYWORDS)

    def _retry_operation(self, operation_func, operation_name: str, retry_stats: Dict[str, int], *args, **kwargs):
        """
        Execute an operation with retry mechanism

        Args:
            operation_func: Function to execute
            operation_name: Name of the operation for logging
            retry_stats: Dictionary to track retry statistics
            *args, **kwargs: Arguments to pass to the operation function

        Returns:
            Result of the operation or None if all retries failed
        """
        last_exception = None

        for attempt in range(MAX_RETRIES):
            try:
                self.logger.debug(f"Attempting {operation_name} (attempt {attempt + 1}/{MAX_RETRIES})")
                result = operation_func(*args, **kwargs)

                if result is not None:
                    if attempt > 0:
                        self.logger.info(f"{operation_name} succeeded on attempt {attempt + 1}")
                    return result
                else:
                    # None result is considered a failure for our use case
                    raise Exception(f"{operation_name} returned None")

            except Exception as e:
                last_exception = e
                error_message = str(e)

                # Check if this is a rate limiting error
                is_rate_limit = self._is_rate_limit_error(error_message)

                self.logger.warning(f"{operation_name} failed on attempt {attempt + 1}: {str(e)}")


                # Track retry attempts (only count actual retries, not the first attempt)
                if attempt > 0:
                    if 'fetch_stock_info' in operation_name:
                        retry_stats['total_fetch_retries'] += 1
                    elif 'save_stock_data' in operation_name:
                        retry_stats['total_save_retries'] += 1
                    elif 'price_history' in operation_name or 'store_price_history' in operation_name:
                        retry_stats['total_price_retries'] += 1
                    elif 'fundamental' in operation_name:
                        retry_stats['total_fundamental_retries'] += 1

                # If this isn't the last attempt, wait before retrying
                if attempt < MAX_RETRIES - 1:
                    if is_rate_limit:
                        # Use longer delays for rate limiting
                        delay = RETRY_DELAYS[attempt]
                        self.logger.warning(f"Rate limiting detected. Waiting {delay} seconds before retry...")
                    else:
                        # Use shorter delays for other errors
                        delay = min(30, 5 * (2 ** attempt))  # Exponential backoff capped at 30s

                    self.logger.info(f"Retrying {operation_name} in {delay} seconds...")
                    time.sleep(delay)

        # All retries failed
        self.logger.error(f"{operation_name} failed after {MAX_RETRIES} attempts. Last error: {str(last_exception)}")
        return None

    def get_ticker_symbols(self) -> List[str]:
        """
        Get ticker symbols for the country from the all_tickers table

        Returns:
            List of ticker symbols
        """
        try:
            with get_db_session() as session:
                query = text(f"SELECT yf_symbol FROM all_tickers_{self.table_prefix}")
                result = session.execute(query)

                symbols = [row[0] for row in result.fetchall()]

                if not symbols:
                    self.logger.warning(f"No active tickers found for {self.country}")
                    return []

                self.logger.info(f"Found {len(symbols)} active tickers for {self.country}")
                return symbols

        except Exception as e:
            self.logger.error(f"Error fetching ticker symbols for {self.country}: {str(e)}")
            return []

    def format_ticker_for_yfinance(self, symbol: str) -> str:
        """
        Format ticker symbol for yfinance API

        Args:
            symbol: Raw ticker symbol

        Returns:
            Formatted ticker symbol for yfinance
        """
        # Remove any existing exchange suffix
        clean_symbol = symbol.split('.')[0]

        # Special handling for countries with multiple possible suffixes (e.g., Korea, Taiwan)
        if 'multiple_suffixes' in self.config:
            # Return just the clean symbol and handle suffixes in fetch_stock_info
            return clean_symbol

        # Add the appropriate exchange suffix for other countries
        return f"{clean_symbol}{self.config['exchange_suffix']}"

    def fetch_stock_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Fetch comprehensive stock information using yfinance

        Args:
            symbol: Ticker symbol

        Returns:
            Dictionary containing stock information or None if failed
        """
        try:
            formatted_symbol = self.format_ticker_for_yfinance(symbol)

            # Special handling for countries with multiple possible suffixes (e.g., Korea, Taiwan)
            if 'multiple_suffixes' in self.config:
                # Try each suffix until one works
                for suffix in self.config['multiple_suffixes']:
                    try:
                        symbol_with_suffix = f"{formatted_symbol}{suffix}"
                        self.logger.debug(f"Trying {symbol} with suffix {suffix} (formatted: {symbol_with_suffix})")

                        # Use yfinance wrapper with rate limiting
                        ticker_data = self.yf_wrapper.get_ticker_info(symbol_with_suffix)

                        if ticker_data:
                            self.logger.info(f"Successfully fetched data for {symbol} with suffix {suffix}")
                            # Extract and format the data
                            stock_info = self.extract_stock_data(ticker_data, symbol)
                            return stock_info, ticker_data
                    except Exception as e:
                        self.logger.debug(f"Failed to fetch {symbol} with suffix {suffix}: {str(e)}")
                        continue

                # If we get here, none of the suffixes worked
                self.logger.warning(f"No data returned for {symbol} with any suffix")
                return None, None
            else:
                # Normal case for other countries
                self.logger.debug(f"Fetching info for {symbol} (formatted: {formatted_symbol})")

                # Use yfinance wrapper with rate limiting
                ticker_data = self.yf_wrapper.get_ticker_info(formatted_symbol)

                if not ticker_data:
                    self.logger.warning(f"No data returned for {symbol}")
                    return None, None

                # Extract and format the data
                stock_info = self.extract_stock_data(ticker_data, symbol)
                return stock_info, ticker_data

        except Exception as e:
            self.logger.error(f"Error fetching stock info for {symbol}: {str(e)}")
            return None, None

    def extract_stock_data(self, ticker_data: Dict[str, Any], original_symbol: str) -> Dict[str, Any]:
        """
        Extract and format stock data from yfinance response

        Args:
            ticker_data: Raw data from yfinance
            original_symbol: Original ticker symbol

        Returns:
            Formatted stock data dictionary
        """
        def safe_get(data: Dict, key: str, default: Any = None) -> Any:
            """Safely get value from dictionary, converting to string if not None"""
            value = data.get(key, default)
            return str(value) if value is not None else None

        # convert original_symbol to symbol
        # e.g. original_symbol = 000020.KS, symbol = 20
        symbol = original_symbol.split('.')[0].lstrip('0')

        # Extract basic information
        stock_data = {
            'symbol': symbol,
            'name': safe_get(ticker_data, 'longName') or safe_get(ticker_data, 'shortName', symbol),
            'last_updated': datetime.now(),

            # Company information
            'address1': safe_get(ticker_data, 'address1'),
            'city': safe_get(ticker_data, 'city'),
            'state': safe_get(ticker_data, 'state'),
            'zip': safe_get(ticker_data, 'zip'),
            'country': self.country,
            'phone': safe_get(ticker_data, 'phone'),
            'website': safe_get(ticker_data, 'website'),

            # Industry classification
            'industry': safe_get(ticker_data, 'industry'),
            'industry_key': safe_get(ticker_data, 'industryKey'),
            'industry_disp': safe_get(ticker_data, 'industryDisp'),
            'sector': safe_get(ticker_data, 'sector'),
            'sector_key': safe_get(ticker_data, 'sectorKey'),
            'sector_disp': safe_get(ticker_data, 'sectorDisp'),

            # Company details
            'long_business_summary': safe_get(ticker_data, 'longBusinessSummary'),
            'full_time_employees': safe_get(ticker_data, 'fullTimeEmployees'),
            'company_officers': safe_get(ticker_data, 'companyOfficers'),

            # Risk metrics
            'audit_risk': safe_get(ticker_data, 'auditRisk'),
            'board_risk': safe_get(ticker_data, 'boardRisk'),
            'compensation_risk': safe_get(ticker_data, 'compensationRisk'),
            'shareholder_rights_risk': safe_get(ticker_data, 'shareholderRightsRisk'),
            'overall_risk': safe_get(ticker_data, 'overallRisk'),

            # Governance and IR
            'governance_epoch_date': safe_get(ticker_data, 'governanceEpochDate'),
            'compensation_as_of_epoch_date': safe_get(ticker_data, 'compensationAsOfEpochDate'),
            'ir_website': safe_get(ticker_data, 'irWebsite'),

            # Market data
            'max_age': safe_get(ticker_data, 'maxAge'),
            'price_hint': safe_get(ticker_data, 'priceHint'),
            'previous_close': safe_get(ticker_data, 'previousClose'),
            'open_price': safe_get(ticker_data, 'open'),
            'day_low': safe_get(ticker_data, 'dayLow'),
            'day_high': safe_get(ticker_data, 'dayHigh'),
            'regular_market_previous_close': safe_get(ticker_data, 'regularMarketPreviousClose'),
            'regular_market_open': safe_get(ticker_data, 'regularMarketOpen'),
            'regular_market_day_low': safe_get(ticker_data, 'regularMarketDayLow'),
            'regular_market_day_high': safe_get(ticker_data, 'regularMarketDayHigh'),
            'regular_market_price': safe_get(ticker_data, 'regularMarketPrice'),
            'regular_market_volume': safe_get(ticker_data, 'regularMarketVolume'),
            'regular_market_change': safe_get(ticker_data, 'regularMarketChange'),
            'regular_market_change_percent': safe_get(ticker_data, 'regularMarketChangePercent'),
            'regular_market_time': safe_get(ticker_data, 'regularMarketTime'),

            # Pre/Post market
            'pre_market_price': safe_get(ticker_data, 'preMarketPrice'),
            'pre_market_change': safe_get(ticker_data, 'preMarketChange'),
            'pre_market_change_percent': safe_get(ticker_data, 'preMarketChangePercent'),
            'post_market_time': safe_get(ticker_data, 'postMarketTime'),

            # Dividend information
            'dividend_rate': safe_get(ticker_data, 'dividendRate'),
            'dividend_yield': safe_get(ticker_data, 'dividendYield'),
            'ex_dividend_date': safe_get(ticker_data, 'exDividendDate'),
            'dividend_date': safe_get(ticker_data, 'dividendDate'),
            'payout_ratio': safe_get(ticker_data, 'payoutRatio'),
            'five_year_avg_dividend_yield': safe_get(ticker_data, 'fiveYearAvgDividendYield'),
            'trailing_annual_dividend_rate': safe_get(ticker_data, 'trailingAnnualDividendRate'),
            'trailing_annual_dividend_yield': safe_get(ticker_data, 'trailingAnnualDividendYield'),
            'last_dividend_value': safe_get(ticker_data, 'lastDividendValue'),
            'last_dividend_date': safe_get(ticker_data, 'lastDividendDate'),

            # Trading metrics
            'beta': safe_get(ticker_data, 'beta'),
            'volume': safe_get(ticker_data, 'volume'),
            'average_volume': safe_get(ticker_data, 'averageVolume'),
            'average_daily_volume_10_day': safe_get(ticker_data, 'averageDailyVolume10Day'),
            'bid': safe_get(ticker_data, 'bid'),
            'ask': safe_get(ticker_data, 'ask'),
            'bid_size': safe_get(ticker_data, 'bidSize'),
            'ask_size': safe_get(ticker_data, 'askSize'),

            # Market cap and shares
            'market_cap': safe_get(ticker_data, 'marketCap'),
            'enterprise_value': safe_get(ticker_data, 'enterpriseValue'),
            'shares_outstanding': safe_get(ticker_data, 'sharesOutstanding'),
            'float_shares': safe_get(ticker_data, 'floatShares'),
            'implied_shares_outstanding': safe_get(ticker_data, 'impliedSharesOutstanding'),

            # Price metrics
            'fifty_two_week_low': safe_get(ticker_data, 'fiftyTwoWeekLow'),
            'fifty_two_week_high': safe_get(ticker_data, 'fiftyTwoWeekHigh'),
            'fifty_two_week_low_change': safe_get(ticker_data, 'fiftyTwoWeekLowChange'),
            'fifty_two_week_low_change_percent': safe_get(ticker_data, 'fiftyTwoWeekLowChangePercent'),
            'fifty_two_week_range': safe_get(ticker_data, 'fiftyTwoWeekRange'),
            'fifty_two_week_high_change': safe_get(ticker_data, 'fiftyTwoWeekHighChange'),
            'fifty_two_week_high_change_percent': safe_get(ticker_data, 'fiftyTwoWeekHighChangePercent'),
            'fifty_two_week_change_percent': safe_get(ticker_data, 'fiftyTwoWeekChangePercent'),
            'fifty_day_average': safe_get(ticker_data, 'fiftyDayAverage'),
            'fifty_day_average_change': safe_get(ticker_data, 'fiftyDayAverageChange'),
            'two_hundred_day_average': safe_get(ticker_data, 'twoHundredDayAverage'),
            'two_hundred_day_average_change': safe_get(ticker_data, 'twoHundredDayAverageChange'),

            # Financial ratios
            'trailing_pe': safe_get(ticker_data, 'trailingPE'),
            'forward_pe': safe_get(ticker_data, 'forwardPE'),
            'price_to_book': safe_get(ticker_data, 'priceToBook'),
            'price_to_sales_trailing_12_months': safe_get(ticker_data, 'priceToSalesTrailing12Months'),
            'price_eps_current_year': safe_get(ticker_data, 'priceEpsCurrentYear'),

            # Short interest
            'shares_short': safe_get(ticker_data, 'sharesShort'),
            'short_ratio': safe_get(ticker_data, 'shortRatio'),
            'shares_short_prior_month': safe_get(ticker_data, 'sharesShortPriorMonth'),
            'shares_short_previous_month_date': safe_get(ticker_data, 'sharesShortPreviousMonthDate'),
            'date_short_interest': safe_get(ticker_data, 'dateShortInterest'),
            'shares_percent_shares_out': safe_get(ticker_data, 'sharesPercentSharesOut'),
            'held_percent_insiders': safe_get(ticker_data, 'heldPercentInsiders'),
            'held_percent_institutions': safe_get(ticker_data, 'heldPercentInstitutions'),
            'short_percent_of_float': safe_get(ticker_data, 'shortPercentOfFloat'),

            # Financial metrics
            'book_value': safe_get(ticker_data, 'bookValue'),
            'earnings_quarterly_growth': safe_get(ticker_data, 'earningsQuarterlyGrowth'),
            'net_income_to_common': safe_get(ticker_data, 'netIncomeToCommon'),
            'trailing_eps': safe_get(ticker_data, 'trailingEps'),
            'forward_eps': safe_get(ticker_data, 'forwardEps'),
            'eps_trailing_twelve_months': safe_get(ticker_data, 'epsTrailingTwelveMonths'),
            'eps_forward': safe_get(ticker_data, 'epsForward'),
            'eps_current_year': safe_get(ticker_data, 'epsCurrentYear'),
            'enterprise_to_revenue': safe_get(ticker_data, 'enterpriseToRevenue'),
            'enterprise_to_ebitda': safe_get(ticker_data, 'enterpriseToEbitda'),

            # Financial statements
            'total_cash': safe_get(ticker_data, 'totalCash'),
            'total_debt': safe_get(ticker_data, 'totalDebt'),
            'quick_ratio': safe_get(ticker_data, 'quickRatio'),
            'current_ratio': safe_get(ticker_data, 'currentRatio'),
            'total_revenue': safe_get(ticker_data, 'totalRevenue'),
            'debt_to_equity': safe_get(ticker_data, 'debtToEquity'),
            'revenue_per_share': safe_get(ticker_data, 'revenuePerShare'),
            'return_on_assets': safe_get(ticker_data, 'returnOnAssets'),
            'return_on_equity': safe_get(ticker_data, 'returnOnEquity'),
            'gross_profits': safe_get(ticker_data, 'grossProfits'),
            'free_cashflow': safe_get(ticker_data, 'freeCashflow'),
            'operating_cashflow': safe_get(ticker_data, 'operatingCashflow'),
            'earnings_growth': safe_get(ticker_data, 'earningsGrowth'),
            'revenue_growth': safe_get(ticker_data, 'revenueGrowth'),
            'gross_margins': safe_get(ticker_data, 'grossMargins'),
            'ebitda_margins': safe_get(ticker_data, 'ebitdaMargins'),
            'operating_margins': safe_get(ticker_data, 'operatingMargins'),
            'ebitda': safe_get(ticker_data, 'ebitda'),
            'total_cash_per_share': safe_get(ticker_data, 'totalCashPerShare'),

            # Exchange information
            'exchange': safe_get(ticker_data, 'exchange', self.config['default_exchange']),
            'full_exchange_name': safe_get(ticker_data, 'fullExchangeName'),
            'exchange_timezone_name': safe_get(ticker_data, 'exchangeTimezoneName'),
            'exchange_timezone_short_name': safe_get(ticker_data, 'exchangeTimezoneShortName'),
            'gmt_offset_milliseconds': safe_get(ticker_data, 'gmtOffSetMilliseconds'),

            # Market analysis
            'current_price': safe_get(ticker_data, 'currentPrice'),
            'target_high_price': safe_get(ticker_data, 'targetHighPrice'),
            'target_low_price': safe_get(ticker_data, 'targetLowPrice'),
            'target_mean_price': safe_get(ticker_data, 'targetMeanPrice'),
            'recommendation_mean': safe_get(ticker_data, 'recommendationMean'),
            'recommendation_key': safe_get(ticker_data, 'recommendationKey'),
            'number_of_analyst_opinions': safe_get(ticker_data, 'numberOfAnalystOpinions'),
            'average_analyst_rating': safe_get(ticker_data, 'averageAnalystRating'),

            # Trading status
            'market_state': safe_get(ticker_data, 'marketState'),
            'quote_source_name': safe_get(ticker_data, 'quoteSourceName'),
            'tradeable': safe_get(ticker_data, 'tradeable'),
            'crypto_tradeable': safe_get(ticker_data, 'cryptoTradeable'),
            'currency': safe_get(ticker_data, 'currency', self.config['currency']),
            'exchange_data_delayed_by': safe_get(ticker_data, 'exchangeDataDelayedBy'),

            # Other fields
            'executive_team': safe_get(ticker_data, 'executiveTeam'),
            'quote_type': safe_get(ticker_data, 'quoteType'),
            'language': safe_get(ticker_data, 'language'),
            'region': safe_get(ticker_data, 'region'),
            'market': safe_get(ticker_data, 'market'),
            'type_disp': safe_get(ticker_data, 'typeDisp'),
            'source_interval': safe_get(ticker_data, 'sourceInterval'),
            'message_board_id': safe_get(ticker_data, 'messageBoardId'),
            'custom_price_alert_confidence': safe_get(ticker_data, 'customPriceAlertConfidence'),
            'corporate_actions': safe_get(ticker_data, 'corporateActions'),
            'financial_currency': safe_get(ticker_data, 'financialCurrency'),

            # Timestamps
            'last_fiscal_year_end': safe_get(ticker_data, 'lastFiscalYearEnd'),
            'next_fiscal_year_end': safe_get(ticker_data, 'nextFiscalYearEnd'),
            'most_recent_quarter': safe_get(ticker_data, 'mostRecentQuarter'),
            'last_split_date': safe_get(ticker_data, 'lastSplitDate'),
            'last_split_factor': safe_get(ticker_data, 'lastSplitFactor'),
            'first_trade_date_milliseconds': safe_get(ticker_data, 'firstTradeDateMilliseconds'),
            'earnings_timestamp': safe_get(ticker_data, 'earningsTimestamp'),
            'earnings_timestamp_start': safe_get(ticker_data, 'earningsTimestampStart'),
            'earnings_timestamp_end': safe_get(ticker_data, 'earningsTimestampEnd'),
            'earnings_call_timestamp_start': safe_get(ticker_data, 'earningsCallTimestampStart'),
            'earnings_call_timestamp_end': safe_get(ticker_data, 'earningsCallTimestampEnd'),
            'is_earnings_date_estimate': safe_get(ticker_data, 'isEarningsDateEstimate'),
            'esg_populated': safe_get(ticker_data, 'esgPopulated'),
            'has_pre_post_market_data': safe_get(ticker_data, 'hasPrePostMarketData'),
        }

        return stock_data

    def fetch_price_history(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        Fetch historical price data for a stock symbol

        Args:
            symbol: Ticker symbol

        Returns:
            DataFrame containing price history or None if failed
        """
        try:
            formatted_symbol = self.format_ticker_for_yfinance(symbol)

            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.price_days)

            # Special handling for countries with multiple possible suffixes (e.g., Korea, Taiwan)
            if 'multiple_suffixes' in self.config:
                # Try each suffix until one works
                for suffix in self.config['multiple_suffixes']:
                    try:
                        symbol_with_suffix = f"{formatted_symbol}{suffix}"
                        self.logger.debug(f"Trying to fetch price history for {symbol} with suffix {suffix} (formatted: {symbol_with_suffix})")

                        # Use yfinance wrapper with rate limiting
                        history = self.yf_wrapper.get_ticker_history(
                            symbol_with_suffix,
                            start=start_date,
                            end=end_date
                        )

                        if not history.empty:
                            self.logger.info(f"Successfully fetched price history for {symbol} with suffix {suffix}")
                            return history
                    except Exception as e:
                        self.logger.debug(f"Failed to fetch price history for {symbol} with suffix {suffix}: {str(e)}")
                        continue

                # If we get here, none of the suffixes worked
                self.logger.warning(f"No price history available for {symbol} with any suffix")
                return None
            else:
                # Normal case for other countries
                self.logger.debug(f"Fetching price history for {symbol} (formatted: {formatted_symbol})")

                # Use yfinance wrapper with rate limiting
                history = self.yf_wrapper.get_ticker_history(
                    formatted_symbol,
                    start=start_date,
                    end=end_date
                )

                if history.empty:
                    self.logger.warning(f"No price history available for {symbol}")
                    return None

                return history

        except Exception as e:
            self.logger.error(f"Error fetching price history for {symbol}: {str(e)}")
            return None

    def store_price_history(self, symbol: str, price_history: pd.DataFrame) -> bool:
        """
        Store price history data in the database

        Args:
            symbol: Ticker symbol
            price_history: DataFrame containing price history

        Returns:
            True if successful, False otherwise
        """
        try:
            table_name = f"stock_{self.table_prefix}_price"
            # convert symbol
            # e.g. 000020.KS -> 20
            symbol = symbol.split('.')[0].lstrip('0')
            with get_db_session() as session:
                # Process each day
                for date, row in price_history.iterrows():
                    # Convert date to datetime.date
                    date_only = date.date()

                    # Handle NaN values by converting them to None
                    price_data = {
                        'symbol': symbol,
                        'date': date_only,
                        'open': float(row['Open']) if pd.notna(row.get('Open')) else None,
                        'high': float(row['High']) if pd.notna(row.get('High')) else None,
                        'low': float(row['Low']) if pd.notna(row.get('Low')) else None,
                        'close': float(row['Close']) if pd.notna(row.get('Close')) else None,
                        'volume': int(row['Volume']) if pd.notna(row.get('Volume')) else None
                    }

                    # Prepare the data for insertion
                    columns = list(price_data.keys())
                    placeholders = ', '.join([':' + col for col in columns])

                    # Create the INSERT ... ON DUPLICATE KEY UPDATE query
                    if self.full_refresh:
                        # Update all fields if full refresh
                        update_clause = ', '.join([f"{col} = VALUES({col})" for col in columns if col not in ('symbol', 'date')])
                    else:
                        # Only insert if not exists (skip update for existing records)
                        update_clause = 'id = id'  # No-op update to avoid changing existing data

                    query = text(f"""
                        INSERT INTO {table_name} ({', '.join(columns)})
                        VALUES ({placeholders})
                        ON DUPLICATE KEY UPDATE {update_clause}
                    """)

                    session.execute(query, price_data)

                session.commit()
                self.logger.debug(f"Successfully stored price history for {symbol}")
                return True

        except Exception as e:
            self.logger.error(f"Error storing price history for {symbol}: {str(e)}")
            return False

    def _clean_numeric_value(self, value: Any) -> Optional[float]:
        """
        Clean numeric values by handling infinity, NaN and converting to proper type.

        Args:
            value: Value to clean

        Returns:
            Optional[float]: Cleaned value or None if invalid
        """
        if value is None:
            return None
        try:
            float_val = float(value)
            if float_val in (float('inf'), float('-inf')) or pd.isna(float_val):
                return None
            return float_val
        except (ValueError, TypeError):
            return None

    def store_fundamental_data(self, symbol: str, ticker_data: Dict[str, Any]) -> bool:
        """
        Store fundamental data in the database

        Args:
            symbol: Ticker symbol
            ticker_data: Raw data from yfinance containing fundamental information

        Returns:
            True if successful, False otherwise
        """
        try:
            table_name = f"stock_{self.table_prefix}_fundamentals"
            today = datetime.now().date()
            # convert symbol
            # e.g. 000020.KS -> 20
            symbol = symbol.split('.')[0].lstrip('0')
            # Extract and clean fundamental data
            fundamental_data = {
                'symbol': symbol,
                'date': today,
                'dividend_rate': self._clean_numeric_value(ticker_data.get('dividendRate')),
                'dividend_yield': self._clean_numeric_value(
                    ticker_data.get('dividendYield', 0) * 100 if ticker_data.get('dividendYield') else None
                ),
                'pe_ratio': self._clean_numeric_value(ticker_data.get('trailingPE')),
                'eps': self._clean_numeric_value(ticker_data.get('trailingEps')),
                'beta': self._clean_numeric_value(ticker_data.get('beta')),
                'fifty_two_week_high': self._clean_numeric_value(ticker_data.get('fiftyTwoWeekHigh')),
                'fifty_two_week_low': self._clean_numeric_value(ticker_data.get('fiftyTwoWeekLow'))
            }

            with get_db_session() as session:
                # Prepare the data for insertion
                columns = list(fundamental_data.keys())
                placeholders = ', '.join([':' + col for col in columns])

                # Create the INSERT ... ON DUPLICATE KEY UPDATE query
                if self.full_refresh:
                    # Update all fields if full refresh
                    update_clause = ', '.join([f"{col} = VALUES({col})" for col in columns if col not in ('symbol', 'date')])
                else:
                    # Only insert if not exists (skip update for existing records)
                    update_clause = 'id = id'  # No-op update to avoid changing existing data

                query = text(f"""
                    INSERT INTO {table_name} ({', '.join(columns)})
                    VALUES ({placeholders})
                    ON DUPLICATE KEY UPDATE {update_clause}
                """)

                session.execute(query, fundamental_data)
                session.commit()

                self.logger.debug(f"Successfully stored fundamental data for {symbol}")
                return True

        except Exception as e:
            self.logger.error(f"Error storing fundamental data for {symbol}: {str(e)}")
            return False

    def save_stock_data(self, stock_data: Dict[str, Any]) -> bool:
        """
        Save stock data to the database

        Args:
            stock_data: Stock data dictionary

        Returns:
            True if successful, False otherwise
        """
        try:
            table_name = f"stock_{self.table_prefix}"

            with get_db_session() as session:
                # Prepare the data for insertion
                columns = list(stock_data.keys())
                placeholders = ', '.join([':' + col for col in columns])

                # Create the INSERT ... ON DUPLICATE KEY UPDATE query
                update_clause = ', '.join([f"{col} = VALUES({col})" for col in columns if col != 'symbol'])

                query = text(f"""
                    INSERT INTO {table_name} ({', '.join(columns)})
                    VALUES ({placeholders})
                    ON DUPLICATE KEY UPDATE {update_clause}
                """)

                session.execute(query, stock_data)
                session.commit()

                self.logger.debug(f"Successfully saved data for {stock_data['symbol']}")
                return True

        except Exception as e:
            self.logger.error(f"Error saving stock data for {stock_data.get('symbol', 'unknown')}: {str(e)}")
            return False

    def run(self) -> Dict[str, int]:
        """
        Run the stock data job for the specified country

        Returns:
            Dictionary with job statistics
        """
        start_time = datetime.now()
        self.logger.info(f"Starting stock data job for {self.country}")

        # Get ticker symbols
        symbols = self.get_ticker_symbols()
        if not symbols:
            self.logger.error(f"No ticker symbols found for {self.country}")
            return {
                'total': 0, 'success': 0, 'failed': 0,
                'price_success': 0, 'price_failed': 0,
                'fundamental_success': 0, 'fundamental_failed': 0
            }

        total_symbols = len(symbols)
        success_count = 0
        failed_count = 0
        price_success_count = 0
        price_failed_count = 0
        fundamental_success_count = 0
        fundamental_failed_count = 0

        self.logger.info(f"Processing {total_symbols} symbols for {self.country}")

        # Process each symbol
        retry_stats = {
            'total_fetch_retries': 0,
            'total_save_retries': 0,
            'total_price_retries': 0,
            'total_fundamental_retries': 0
        }

        # Detailed tracking of success and failure symbols
        success_symbols = []
        failed_symbols = []
        price_success_symbols = []
        price_failed_symbols = []
        fundamental_success_symbols = []
        fundamental_failed_symbols = []

        # Process each symbol
        for i, symbol in enumerate(symbols, 1):
            try:
                self.logger.info(f"Processing {symbol} ({i}/{total_symbols})")

                # Fetch stock information with retry mechanism
                stock_info, ticker_info = self._retry_operation(
                    self.fetch_stock_info,
                    f"fetch_stock_info for {symbol}",
                    retry_stats,
                    symbol
                )

                if stock_info:
                    # Save stock data to database with retry mechanism
                    save_success = self._retry_operation(
                        self.save_stock_data,
                        f"save_stock_data for {symbol}",
                        retry_stats,
                        stock_info
                    )

                    if save_success:
                        success_count += 1
                        success_symbols.append(symbol)
                        self.logger.info(f"Successfully processed stock data for {symbol}")

                        # Fetch and store price history
                        try:
                            price_history = self._retry_operation(
                                self.fetch_price_history,
                                f"fetch_price_history for {symbol}",
                                retry_stats,
                                symbol
                            )

                            if price_history is not None and not price_history.empty:
                                # Store price history with retry mechanism
                                price_save_success = self._retry_operation(
                                    self.store_price_history,
                                    f"store_price_history for {symbol}",
                                    retry_stats,
                                    symbol,
                                    price_history
                                )

                                if price_save_success:
                                    price_success_count += 1
                                    price_success_symbols.append(symbol)
                                    self.logger.info(f"Successfully processed price history for {symbol}")
                                else:
                                    price_failed_count += 1
                                    price_failed_symbols.append(symbol)
                                    self.logger.error(f"Failed to save price history for {symbol}")
                            else:
                                price_failed_count += 1
                                price_failed_symbols.append(symbol)
                                self.logger.warning(f"No price history available for {symbol}")
                        except Exception as e:
                            price_failed_count += 1
                            price_failed_symbols.append(symbol)
                            self.logger.error(f"Error processing price history for {symbol}: {str(e)}")

                        # Store fundamental data (we already have the stock_info from earlier)
                        try:
                            fundamental_save_success = self._retry_operation(
                                self.store_fundamental_data,
                                f"store_fundamental_data for {symbol}",
                                retry_stats,
                                symbol,
                                ticker_info  # Pass the original stock_info data
                            )

                            if fundamental_save_success:
                                fundamental_success_count += 1
                                fundamental_success_symbols.append(symbol)
                                self.logger.info(f"Successfully processed fundamental data for {symbol}")
                            else:
                                fundamental_failed_count += 1
                                fundamental_failed_symbols.append(symbol)
                                self.logger.error(f"Failed to save fundamental data for {symbol}")
                        except Exception as e:
                            fundamental_failed_count += 1
                            fundamental_failed_symbols.append(symbol)
                            self.logger.error(f"Error processing fundamental data for {symbol}: {str(e)}")
                    else:
                        failed_count += 1
                        failed_symbols.append(symbol)
                        price_failed_count += 1  # Count as price failure too since we couldn't save stock data
                        price_failed_symbols.append(symbol)
                        fundamental_failed_count += 1  # Count as fundamental failure too
                        fundamental_failed_symbols.append(symbol)
                        self.logger.error(f"Failed to save stock data for {symbol}")
                else:
                    failed_count += 1
                    failed_symbols.append(symbol)
                    price_failed_count += 1  # Count as price failure too since we couldn't fetch stock data
                    price_failed_symbols.append(symbol)
                    fundamental_failed_count += 1  # Count as fundamental failure too
                    fundamental_failed_symbols.append(symbol)
                    self.logger.error(f"Failed to fetch stock data for {symbol}")

                # Progress logging
                if i % 10 == 0:
                    self.logger.info(f"Progress: {i}/{total_symbols} ({i/total_symbols*100:.1f}%)")

            except Exception as e:
                failed_count += 1
                failed_symbols.append(symbol)
                price_failed_count += 1
                price_failed_symbols.append(symbol)
                fundamental_failed_count += 1
                fundamental_failed_symbols.append(symbol)
                self.logger.error(f"Error processing {symbol}: {str(e)}")

        # Job completion
        end_time = datetime.now()
        duration = end_time - start_time

        stats = {
            'total': total_symbols,
            'success': success_count,
            'failed': failed_count,
            'price_success': price_success_count,
            'price_failed': price_failed_count,
            'fundamental_success': fundamental_success_count,
            'fundamental_failed': fundamental_failed_count,
            'duration_seconds': duration.total_seconds(),
            'retry_stats': retry_stats,
            # Detailed symbol tracking
            'success_symbols': success_symbols,
            'failed_symbols': failed_symbols,
            'price_success_symbols': price_success_symbols,
            'price_failed_symbols': price_failed_symbols,
            'fundamental_success_symbols': fundamental_success_symbols,
            'fundamental_failed_symbols': fundamental_failed_symbols
        }

        self.logger.info(f"Stock data job completed for {self.country}")
        self.logger.info(f"Total: {total_symbols}, Stock Success: {success_count}, Stock Failed: {failed_count}")
        self.logger.info(f"Price Success: {price_success_count}, Price Failed: {price_failed_count}")
        self.logger.info(f"Fundamental Success: {fundamental_success_count}, Fundamental Failed: {fundamental_failed_count}")
        self.logger.info(f"Retry statistics - Fetch: {retry_stats['total_fetch_retries']}, Save: {retry_stats['total_save_retries']}, Price: {retry_stats['total_price_retries']}, Fundamental: {retry_stats['total_fundamental_retries']}")
        self.logger.info(f"Duration: {duration}")

        # Log detailed symbol lists
        if success_symbols:
            self.logger.info(f"Stock Success Symbols ({len(success_symbols)}): {', '.join(success_symbols)}")
        if failed_symbols:
            self.logger.info(f"Stock Failed Symbols ({len(failed_symbols)}): {', '.join(failed_symbols)}")
        if price_success_symbols:
            self.logger.info(f"Price Success Symbols ({len(price_success_symbols)}): {', '.join(price_success_symbols)}")
        if price_failed_symbols:
            self.logger.info(f"Price Failed Symbols ({len(price_failed_symbols)}): {', '.join(price_failed_symbols)}")
        if fundamental_success_symbols:
            self.logger.info(f"Fundamental Success Symbols ({len(fundamental_success_symbols)}): {', '.join(fundamental_success_symbols)}")
        if fundamental_failed_symbols:
            self.logger.info(f"Fundamental Failed Symbols ({len(fundamental_failed_symbols)}): {', '.join(fundamental_failed_symbols)}")

        return stats

    def print_detailed_statistics(self, stats: Dict[str, Any]) -> None:
        """
        Print detailed statistics with symbol breakdown

        Args:
            stats: Statistics dictionary from job run
        """
        print(f"\n📊 Detailed Statistics for {self.country}")
        print("=" * 60)

        # Overall summary
        print(f"📈 Overall Summary:")
        print(f"   Total symbols processed: {stats['total']}")
        print(f"   Duration: {stats['duration_seconds']:.2f} seconds")
        print(f"   Average time per symbol: {stats['duration_seconds']/stats['total']:.2f} seconds")

        # Stock data results
        print(f"\n🏢 Stock Data Results:")
        print(f"   ✅ Successful: {stats['success']} ({stats['success']/stats['total']*100:.1f}%)")
        print(f"   ❌ Failed: {stats['failed']} ({stats['failed']/stats['total']*100:.1f}%)")

        # Price data results
        print(f"\n💰 Price Data Results:")
        print(f"   ✅ Successful: {stats['price_success']} ({stats['price_success']/stats['total']*100:.1f}%)")
        print(f"   ❌ Failed: {stats['price_failed']} ({stats['price_failed']/stats['total']*100:.1f}%)")

        # Fundamental data results
        print(f"\n📊 Fundamental Data Results:")
        print(f"   ✅ Successful: {stats['fundamental_success']} ({stats['fundamental_success']/stats['total']*100:.1f}%)")
        print(f"   ❌ Failed: {stats['fundamental_failed']} ({stats['fundamental_failed']/stats['total']*100:.1f}%)")

        # Retry statistics
        retry_stats = stats['retry_stats']
        total_retries = sum(retry_stats.values())
        if total_retries > 0:
            print(f"\n🔄 Retry Statistics:")
            print(f"   Total retries: {total_retries}")
            print(f"   Fetch retries: {retry_stats['total_fetch_retries']}")
            print(f"   Save retries: {retry_stats['total_save_retries']}")
            print(f"   Price retries: {retry_stats['total_price_retries']}")
            print(f"   Fundamental retries: {retry_stats['total_fundamental_retries']}")

        # Symbol details (limit output for readability)
        max_symbols_to_show = 20

        if stats['success_symbols']:
            symbols_to_show = stats['success_symbols'][:max_symbols_to_show]
            remaining = len(stats['success_symbols']) - len(symbols_to_show)
            print(f"\n✅ Stock Success Symbols ({len(stats['success_symbols'])}):")
            print(f"   {', '.join(symbols_to_show)}")
            if remaining > 0:
                print(f"   ... and {remaining} more")

        if stats['failed_symbols']:
            symbols_to_show = stats['failed_symbols'][:max_symbols_to_show]
            remaining = len(stats['failed_symbols']) - len(symbols_to_show)
            print(f"\n❌ Stock Failed Symbols ({len(stats['failed_symbols'])}):")
            print(f"   {', '.join(symbols_to_show)}")
            if remaining > 0:
                print(f"   ... and {remaining} more")

        # Show unique failed symbols across all categories
        all_failed = set(stats['failed_symbols'] + stats['price_failed_symbols'] + stats['fundamental_failed_symbols'])
        if all_failed:
            failed_to_show = list(all_failed)[:max_symbols_to_show]
            remaining = len(all_failed) - len(failed_to_show)
            print(f"\n⚠️ All Failed Symbols (any category) ({len(all_failed)}):")
            print(f"   {', '.join(failed_to_show)}")
            if remaining > 0:
                print(f"   ... and {remaining} more")


def main():
    """Main function to run the stock others data job"""
    import argparse

    parser = argparse.ArgumentParser(description='Stock Others Data Job - Fetch stock data for various countries')
    parser.add_argument('country', help=f'Country name. Supported: {", ".join(COUNTRY_CONFIG.keys())}')
    parser.add_argument('--price-days', type=int, default=30, help='Number of days of price history to fetch (default: 30)')
    parser.add_argument('--full-refresh', action='store_true', help='Refresh all data including existing records')

    args = parser.parse_args()

    if args.country not in COUNTRY_CONFIG:
        print(f"Error: Unsupported country '{args.country}'")
        print(f"Supported countries: {', '.join(COUNTRY_CONFIG.keys())}")
        sys.exit(1)

    try:
        # Initialize and run the job
        job = StockOthersDataJob(args.country, price_days=args.price_days, full_refresh=args.full_refresh)
        stats = job.run()

        # Print detailed results using the new statistics function
        job.print_detailed_statistics(stats)

        # Exit with appropriate code
        total_failed = stats['failed'] + stats['price_failed'] + stats['fundamental_failed']
        if total_failed > 0:
            print(f"Warning: {total_failed} operations failed to process")
            sys.exit(0)
        else:
            print("All operations processed successfully")
            sys.exit(0)

    except Exception as e:
        print(f"Error running job: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
