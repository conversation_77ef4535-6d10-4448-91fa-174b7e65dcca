#!/usr/bin/env python3
"""
Singapore Ticker Update Job

This job reads Singapore stock ticker information from sgx.json file and updates the all_tickers_sg table.
It follows the same pattern as ticker_update_job.py but is specifically designed for Singapore stocks.

Usage:
    python ticker_sg_update_job.py [--skip-logo]

Options:
    --skip-logo: Skip logo download and processing
"""

import os
import sys
import json
import argparse
import traceback
from typing import List, Dict, Optional
from jobs.db.migrations.migrations import apply_migrations

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.db.mysql_client import get_db_session
from jobs.db.schema import AllTickerSG, JobRun
from utils.logging_utils import setup_logging
from utils.logo_api import get_logo_url
from utils.image_utils import download_logo
from utils.ticker_utils import strip_leading_zeros, generate_yf_symbol
from config.settings import settings

# Configure logging
logger = setup_logging(enable_colors=False)

class TickerSGUpdateJob:
    """
    Singapore Ticker Update Job

    Reads Singapore stock ticker information from sgx.json and updates the all_tickers_sg table.
    """

    def __init__(self, skip_update_logo: bool = False):
        """
        Initialize the Singapore ticker update job.

        Args:
            skip_update_logo: Whether to skip logo update processing
        """
        self.skip_update_logo = skip_update_logo
        self.processed_count = 0
        self.batch_counter = 0
        self.job_run_id = None
        self._init_database()
        logger.info(f"Initialized TickerSGUpdateJob with skip_update_logo={skip_update_logo}")

    def _init_database(self):
        """
        Initialize the database.
        """
        logger.info("Initializing database")
        try:
            # Apply migrations instead of creating tables directly
            apply_migrations()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise

    def run(self):
        """
        Main execution method for the Singapore ticker update job.
        """
        logger.info("Starting Singapore ticker update job...")

        try:
            # Create job run record and store its ID
            with get_db_session() as session:
                job_run = JobRun.create_job_run(session, "ticker_sg_update_job")
                session.flush()  # Ensure we have the ID
                self.job_run_id = job_run.id
                session.commit()
                logger.info(f"Created job run record with ID: {self.job_run_id}")

            # Process Singapore tickers from JSON
            self._get_sg_tickers_from_json()

            # Mark job as completed using a fresh session
            with get_db_session() as session:
                job_run = session.query(JobRun).get(self.job_run_id)
                job_run.complete(session, self.processed_count)
                logger.info(f"Job completed successfully. Processed {self.processed_count} tickers.")

        except Exception as e:
            logger.error(f"Job failed with error: {str(e)}")
            logger.error(traceback.format_exc())

            # Mark job as failed using a fresh session
            try:
                with get_db_session() as session:
                    job_run = session.query(JobRun).get(self.job_run_id)
                    job_run.fail(session, str(e), self.processed_count)
            except Exception as mark_error:
                logger.error(f"Failed to mark job as failed: {str(mark_error)}")

            raise

    def _get_sg_tickers_from_json(self):
        """
        Read Singapore ticker information from sgx.json file and process it.
        """
        # Get the project root directory
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        sgx_path = os.path.join(project_root, 'jobs', 'tickers_sg', 'sgx.json')

        logger.info(f"Reading Singapore tickers from: {sgx_path}")

        def extract_tickers(json_data):
            """Extract ticker symbols and company names from the JSON data format [["TICKER","Company Name"]]"""
            full_data = [(item[0], item[1]) for item in json_data]
            ticker_only = [item[0] for item in json_data]
            return full_data, ticker_only

        try:
            # Load SGX ticker data
            with open(sgx_path, 'r', encoding='utf-8') as file:
                sgx_tickers_data, sgx_tickers = extract_tickers(json.load(file))

            logger.info(f"Loaded {len(sgx_tickers_data)} Singapore tickers from JSON")

            # Get existing logos from database
            existing_logos = {}
            with get_db_session() as session:
                for ticker_obj in session.query(AllTickerSG.symbol, AllTickerSG.logo).all():
                    if ticker_obj.logo:  # If logo exists and is not None/empty
                        existing_logos[ticker_obj.symbol] = ticker_obj.logo

            # Prepare ticker information list
            ticker_info_list = []
            logo_updates_needed = 0

            for symbol, name in sgx_tickers_data:
                # Get current logo URL from database
                current_logo_url = existing_logos.get(symbol)

                # Always get the latest logo URL from logo.dev API (unless skipping logo updates)
                new_logo_url = None
                needs_logo_update = False

                if not self.skip_update_logo and not settings.SKIP_UPDATE_LOGO:
                    # For Singapore stocks, we might need to append .SI suffix for logo.dev
                    logo_ticker = f"{symbol}.SI" if not symbol.endswith('.SI') else symbol
                    new_logo_url = get_logo_url(logo_ticker, name)

                    # Always update logo if we have a logo URL
                    if new_logo_url:
                        needs_logo_update = True
                        logo_updates_needed += 1
                        logger.debug(f"Logo will be updated for {symbol}: {new_logo_url}")

                # Process symbol: strip leading zeros
                stripped_symbol = strip_leading_zeros(symbol)
                yf_symbol = generate_yf_symbol(stripped_symbol, 'SG')

                ticker_info = {
                    'symbol': stripped_symbol,  # Store symbol without leading zeros
                    'yf_symbol': yf_symbol,     # Store Yahoo Finance compatible symbol
                    'name': name,
                    'country': 'Singapore',
                    'currency': 'SGD',  # Default currency for Singapore stocks
                    'exchange': 'SGX',  # Singapore Exchange
                    'isin': None,  # Not available in JSON source
                    'logo_url': new_logo_url,
                    'current_logo_url': current_logo_url,
                    'needs_logo_update': needs_logo_update
                }

                ticker_info_list.append(ticker_info)

            logger.info(f"Prepared {len(ticker_info_list)} Singapore tickers for processing")
            logger.info(f"Will update logos for {logo_updates_needed} tickers")
            logger.info(f"Existing logos in database: {len(existing_logos)}")

            # Save tickers to database
            self._save_tickers_to_db(ticker_info_list)

            return ticker_info_list

        except FileNotFoundError as e:
            logger.error(f"Error reading SGX ticker file: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error processing SGX tickers: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def _save_tickers_to_db(self, ticker_data: List[Dict]):
        """
        Save Singapore tickers to the database.

        Args:
            ticker_data: List of dictionaries containing ticker information
        """
        logger.info(f"Saving {len(ticker_data)} Singapore tickers to database")

        # Remove duplicates from ticker_data first
        seen_tickers = set()
        unique_ticker_data = []
        duplicate_count = 0

        for ticker_info in ticker_data:
            ticker_symbol = ticker_info['symbol']
            if ticker_symbol not in seen_tickers:
                seen_tickers.add(ticker_symbol)
                unique_ticker_data.append(ticker_info)
            else:
                duplicate_count += 1
                logger.debug(f"Skipping duplicate ticker in input data: {ticker_symbol}")

        if duplicate_count > 0:
            logger.warning(f"Found and removed {duplicate_count} duplicate tickers from input data")

        batch_size = 20
        batches = [unique_ticker_data[i:i + batch_size] for i in range(0, len(unique_ticker_data), batch_size)]

        total_saved = 0
        total_updated = 0
        total_logos_processed = 0

        with get_db_session() as session:
            # Get existing tickers for comparison
            existing_tickers = {ticker.symbol: ticker for ticker in session.query(AllTickerSG).all()}

            for batch in batches:
                batch_saved = 0
                batch_updated = 0

                for ticker_info in batch:
                    ticker_symbol = ticker_info['symbol']
                    yf_symbol = generate_yf_symbol(ticker_symbol, 'SG')

                    # Process logo if update is needed
                    logo_path = None
                    should_update_logo = False

                    if ticker_info.get('needs_logo_update', False) and ticker_info.get('logo_url'):
                        logo_url = ticker_info['logo_url']
                        file_path, _ = download_logo(yf_symbol, logo_url, '/sg')
                        
                        if file_path:
                            # Store the GCS URL or local file path in the database
                            logo_path = file_path
                            should_update_logo = True
                            total_logos_processed += 1
                            logger.info(f"Downloaded and uploaded updated logo for Singapore ticker {ticker_symbol} to GCS: {file_path}")
                        else:
                            # If download fails, keep the current logo
                            logger.warning(f"Failed to download logo for Singapore ticker {ticker_symbol}, keeping current logo")
                    elif ticker_info.get('logo_url') and not ticker_info.get('current_logo_url'):
                        # New ticker without existing logo
                        logo_url = ticker_info['logo_url']
                        file_path, _ = download_logo(yf_symbol, logo_url, '/sg')

                        if file_path:
                            logo_path = file_path
                            should_update_logo = True
                            total_logos_processed += 1
                            logger.info(f"Downloaded logo for new Singapore ticker {ticker_symbol} to GCS: {file_path}")

                    if ticker_symbol in existing_tickers:
                        # Update existing ticker
                        existing = existing_tickers[ticker_symbol]

                        # Update fields
                        existing.name = ticker_info['name']
                        existing.country = ticker_info['country']
                        existing.currency = ticker_info['currency']
                        existing.exchange = ticker_info['exchange']
                        existing.isin = ticker_info['isin']
                        existing.yf_symbol = ticker_info['yf_symbol']  # Update yf_symbol field

                        # Update logo field if we have a new logo or if it's a new ticker
                        if should_update_logo and logo_path:
                            existing.logo = logo_path

                        batch_updated += 1
                    else:
                        # Create new AllTickerSG object and add to session
                        new_ticker = AllTickerSG(
                            symbol=ticker_symbol,
                            name=ticker_info['name'],
                            country=ticker_info['country'],
                            currency=ticker_info['currency'],
                            exchange=ticker_info['exchange'],
                            isin=ticker_info['isin'],
                            yf_symbol=ticker_info['yf_symbol'],  # Add yf_symbol field
                            logo=logo_path
                        )
                        session.add(new_ticker)
                        # Add to existing_tickers to prevent duplicates in subsequent batches
                        existing_tickers[ticker_symbol] = new_ticker
                        batch_saved += 1

                # Commit each batch with error handling
                try:
                    session.commit()
                    total_saved += batch_saved
                    total_updated += batch_updated
                    self.batch_counter += 1
                    logger.info(f"Committed batch {self.batch_counter} with {len(batch)} tickers ({batch_saved} new, {batch_updated} updated)")
                except Exception as e:
                    logger.error(f"Error committing batch {self.batch_counter}: {str(e)}")
                    session.rollback()
                    # Try to process each ticker individually to identify the problematic one
                    self._process_batch_individually(session, batch, existing_tickers)

            # Update processed count
            self.processed_count = total_saved + total_updated

        logger.info(f"Saved {total_saved} new Singapore tickers and updated {total_updated} existing tickers")
        logger.info(f"Processed {total_logos_processed} logos")

    def _process_batch_individually(self, session, batch: List[Dict], existing_tickers: Dict):
        """
        Process each ticker in a batch individually when batch commit fails.

        Args:
            session: SQLAlchemy session
            batch: List of ticker info dictionaries
            existing_tickers: Dictionary of existing tickers
        """
        logger.info(f"Processing {len(batch)} Singapore tickers individually due to batch commit failure")

        for ticker_info in batch:
            ticker_symbol = ticker_info['symbol']
            try:
                if ticker_symbol in existing_tickers:
                    # Update existing ticker
                    existing = existing_tickers[ticker_symbol]
                    existing.name = ticker_info['name']
                    existing.country = ticker_info['country']
                    existing.currency = ticker_info['currency']
                    existing.exchange = ticker_info['exchange']
                    existing.isin = ticker_info['isin']
                    existing.yf_symbol = ticker_info['yf_symbol']

                    logger.debug(f"Updated Singapore ticker {ticker_symbol} individually")
                else:
                    # Create new ticker
                    new_ticker = AllTickerSG(
                        symbol=ticker_symbol,
                        name=ticker_info['name'],
                        country=ticker_info['country'],
                        currency=ticker_info['currency'],
                        exchange=ticker_info['exchange'],
                        isin=ticker_info['isin'],
                        yf_symbol=ticker_info['yf_symbol']
                    )
                    session.add(new_ticker)
                    existing_tickers[ticker_symbol] = new_ticker
                    logger.debug(f"Added new Singapore ticker {ticker_symbol} individually")

                # Commit each ticker individually
                session.commit()

            except Exception as e:
                logger.error(f"Failed to process Singapore ticker {ticker_symbol} individually: {str(e)}")
                session.rollback()
                continue


def main():
    """
    Main function to run the Singapore ticker update job.
    """
    parser = argparse.ArgumentParser(description='Update Singapore ticker information from SGX JSON file')
    parser.add_argument('--skip-logo', action='store_true',
                       help='Skip logo download and processing')

    args = parser.parse_args()

    try:
        job = TickerSGUpdateJob(skip_update_logo=args.skip_logo)
        job.run()
        logger.info("Singapore ticker update job completed successfully")
    except Exception as e:
        logger.error(f"Singapore ticker update job failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
