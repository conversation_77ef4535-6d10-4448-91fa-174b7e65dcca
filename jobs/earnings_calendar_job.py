"""
Earnings calendar job implementation for fetching company earnings data.
"""

import logging
import time
from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from jobs.db.mysql_client import get_db_session
from jobs.db.schema import (
    EarningsCalendarEvent,
    JobRun,
    Base
)
from jobs.earnings_calendar import get_earnings_calendar, get_earnings_calendar_compare_yf
from jobs.db.migrations.migrations import apply_migrations
from utils.logging_utils import setup_logging
from utils.pubsub_utils import PubSubPublisher
from openai import OpenAI

from utils.yq import get_earning_history

logger = setup_logging(enable_colors=False)

class EarningsCalendarJob:
    def __init__(self, settings=None):
        self.db_session = get_db_session()
        self.settings = settings
        
        # Apply migrations instead of creating tables directly
        apply_migrations()
        
        # Initialize PubSub publisher only if enabled
        self.enable_pubsub = False
        if settings and hasattr(settings, 'ENABLE_PUBSUB'):
            self.enable_pubsub = settings.ENABLE_PUBSUB
            if self.enable_pubsub and hasattr(settings, 'PUBSUB_EARNINGS_CALENDAR_EVENT_TOPIC'):
                self.pubsub_publisher = PubSubPublisher(settings.PUBSUB_EARNINGS_CALENDAR_EVENT_TOPIC)
            else:
                self.pubsub_publisher = None
        else:
            self.pubsub_publisher = None


    def fetch_upcoming_earnings(self) -> List[EarningsCalendarEvent]:
        """Fetch upcoming earnings events from configured sources."""
        events = []
        
        try:
            # Use the get_earnings_calendar function to fetch data
            df = get_earnings_calendar_compare_yf()
            
            if df is None or df.empty:
                logger.error("Failed to fetch data from Investing.com or empty results returned")
                return []
            
            # Transform DataFrame rows to EarningsCalendarEvent objects
            for _, row in df.iterrows():
                event = self._parse_event(row)
                if event:
                    events.append(event)
                
            logger.info(f"Fetched {len(events)} earnings events")
            
            # Save events to database
            self._save_events(events)

            self._check_actual()
                
            return events
        except Exception as e:
            logger.error(f"Error fetching earnings data: {str(e)}")
            raise
    
    def _parse_event(self, event_data) -> Optional[EarningsCalendarEvent]:
        event = EarningsCalendarEvent(
            company=event_data.company,
            company_name=event_data.company_name,
            ticker=event_data.ticker,
            eps_forecast=event_data.eps_forecast,
            revenue_forecast=event_data.revenue_forecast,
            eps_actual=event_data.eps_actual,
            revenue_actual=event_data.revenue_actual,
            market_cap=event_data.market_cap,
            release_date=event_data.release_date,
            release_time=event_data.release_time,
        )

        return event
    
    def _check_actual(self):
        not_actual = self.db_session.query(EarningsCalendarEvent).filter(
            EarningsCalendarEvent.eps_actual == ''
        ).all()

        past_7_days = {}
        for event in not_actual:
            # Make event.release_date timezone-aware (assume UTC if naive)
            release_date_aware = event.release_date
            if release_date_aware.tzinfo is None:
                release_date_aware = release_date_aware.replace(tzinfo=timezone.utc)
            
            if release_date_aware < datetime.now(tz=timezone.utc) - timedelta(days=7):
                continue
            past_7_days[event.ticker] = event
        
        history = get_earning_history(list(past_7_days.keys()))
        pass

    def _save_events(self, events: List[EarningsCalendarEvent]):
        """Save or update events in database."""
        try:
            # First deduplicate the new events among themselves
            seen = set()
            unique_events = []
            for event in events:
                key = (event.release_date.strftime('%Y-%m-%d %H:%M:%S'), event.company, event.company_name, event.ticker)
                if key not in seen:
                    seen.add(key)
                    unique_events.append(event)
                else:
                    logger.warning(f"Duplicate event found: {key}. Skipping.")
            
            # For each unique event, try to merge it
            saved_count = 0
            updated_count = 0
            have_actual_count = 0
            for event in unique_events:
                try:
                    # Check if event exists
                    existing = self.db_session.query(EarningsCalendarEvent).filter(
                        EarningsCalendarEvent.release_date == event.release_date,
                        EarningsCalendarEvent.company_name == event.company_name,
                        EarningsCalendarEvent.ticker == event.ticker
                    ).first()
                    
                    if existing:
                        have_actual = (existing.eps_actual == '' and event.eps_actual != '') or (existing.revenue_actual == '' and event.revenue_actual != '')
                        existing.company = event.company
                        existing.company_name = event.company_name
                        existing.ticker = event.ticker
                        existing.eps_forecast = event.eps_forecast
                        existing.revenue_forecast = event.revenue_forecast
                        existing.eps_actual = event.eps_actual
                        existing.revenue_actual = event.revenue_actual
                        existing.market_cap = event.market_cap
                        existing.release_date = event.release_date
                        existing.release_time = event.release_time
                        updated_count += 1
                        
                        # Publish event to Pub/Sub only if it has new actual
                        if have_actual:
                            self._publish_event(existing, is_new=False)
                            have_actual_count += 1
                    else:
                        # Insert new record
                        self.db_session.add(event)
                        saved_count += 1
                        self.db_session.flush()
                        
                        # Publish new event to Pub/Sub if enabled
                        if self.enable_pubsub and self.pubsub_publisher:
                            self._publish_event(event, is_new=True)
                    
                except Exception as e:
                    logger.error(f"Error processing event {event.company_name}: {str(e)}")
                    continue
                    
            self.db_session.commit()
            logger.info(f"Processed {len(unique_events)} events: {saved_count} new, {updated_count} updated, {have_actual_count} have actual")
                
        except Exception as e:
            logger.error(f"Error saving events to database: {str(e)}")
            self.db_session.rollback()
            raise
    
    def _publish_event(self, event, is_new=False):
        """Publish event to Pub/Sub."""
        if not self.pubsub_publisher:
            return
            
        try:
            event_data = {
                'event_id': event.id,
                'company': event.company,
                'company_name': event.company_name,
                'ticker': event.ticker,
                'release_date': event.release_date.strftime('%Y-%m-%d'),
                'release_time': event.release_time,
                'eps_forecast': event.eps_forecast,
                'eps_actual': event.eps_actual,
                'revenue_forecast': event.revenue_forecast,
                'revenue_actual': event.revenue_actual,
            }
            
            attrs = {
                'event_id': event.id,
                'is_new': '1' if is_new else '0',
            }
            
            self.pubsub_publisher.publish(event_data, attrs=attrs)
            logger.debug(f"Published event to Pub/Sub: {event.company_name}")
        except Exception as e:
            logger.error(f"Error publishing event to Pub/Sub: {str(e)}")
    
    def run(self):
        """Main job execution method."""
        try:
            self.fetch_upcoming_earnings()
        finally:
            self.db_session.close()

if __name__ == "__main__":
    from config.settings import settings
    job = EarningsCalendarJob(settings=settings)
    job.run()
