#!/usr/bin/env python3
"""
Job to fetch US stock market data and store it in MySQL database.
"""

import os
import sys
import time
import logging
import argparse
import datetime
import traceback
import io
import requests
from typing import List, Dict, Any, Optional, Set, Tuple
import pandas as pd
import yfinance as yf
from sqlalchemy.exc import SQLAlchemyError
import json
from yfinance.exceptions import YFRateLimitError

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.db.mysql_client import MySQLClient, get_db_session
from jobs.db.schema import AllTicker, Stock, StockPrice, StockFundamental, JobRun, DelistTicker, SP500Ticker, Nasdaq100Ticker, NasdaqCompositeTicker, DowJonesTicker, Russell2000Ticker
from jobs.db.migrations.migrations import apply_migrations  # Add this import
from utils.logging_utils import logger
from utils.yfinance_wrapper import YFinanceWrapper
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('stock_data_job.log')
    ]
)

class StockDataJob:
    """
    Job to fetch US stock market data and store it in MySQL database.
    """
    
    def __init__(
        self,
        full_refresh: bool = False,
        specific_tickers: Optional[List[str]] = None,
        batch_size: int = 25,
        delay: float = 1.0,
        price_days: int = 30,
        max_retries: int = 5,
        min_wait: float = 60.0,
        max_wait: float = 120.0
    ):
        """
        Initialize the job.
        
        Args:
            full_refresh: Whether to refresh all data
            specific_tickers: List of specific tickers to process
            batch_size: Number of stocks to process in each batch
            delay: Delay between batches in seconds
            price_days: Number of days of price history to fetch
            max_retries: Maximum number of retry attempts for rate limiting
            min_wait: Minimum wait time between retries in seconds
            max_wait: Maximum wait time between retries in seconds
        """
        self.full_refresh = full_refresh
        self.specific_tickers = specific_tickers
        self.batch_size = batch_size
        self.delay = delay
        self.price_days = price_days
        self.job_run_id = None  # Store ID instead of instance
        self.processed_count = 0
        self.error_count = 0
        self.batch_counter = 0  # Add batch counter
        
        # Initialize YFinance wrapper with retry settings
        self.yf_wrapper = YFinanceWrapper(
            max_retries=max_retries,
            min_wait=min_wait,
            max_wait=max_wait,
            batch_size_reduction_factor=0.5,
            min_batch_size=5
        )
        
        # Initialize index tickers
        # logger.info("Fetching index membership data")
        # self.sp500_tickers = self._get_sp500_tickers()
        # self.nasdaq100_tickers = self._get_nasdaq100_tickers()
        # self.nasdaq_composite_tickers = self._get_nasdaq_composite_tickers()
        # self.dow_tickers = self._get_dow_tickers()
        # self.russell2000_tickers = self._get_russell2000_tickers()
        # logger.info(f"Found {len(self.sp500_tickers)} S&P 500, {len(self.nasdaq100_tickers)} Nasdaq-100, "
        #            f"{len(self.nasdaq_composite_tickers)} Nasdaq Composite, {len(self.dow_tickers)} Dow Jones, "
        #            f"{len(self.russell2000_tickers)} Russell 2000 tickers")

        # Initialize database
        self._init_database()

        with get_db_session() as session:
            self.sp500_tickers = set(ticker[0] for ticker in session.query(SP500Ticker.ticker).all())
            self.nasdaq100_tickers = set(ticker[0] for ticker in session.query(Nasdaq100Ticker.ticker).all())
            self.nasdaq_composite_tickers = set(ticker[0] for ticker in session.query(NasdaqCompositeTicker.ticker).all())
            self.dow_tickers = set(ticker[0] for ticker in session.query(DowJonesTicker.ticker).all())
            self.russell2000_tickers = set(ticker[0] for ticker in session.query(Russell2000Ticker.ticker).all())
    
    def _init_database(self):
        """
        Initialize the database.
        """
        logger.info("Initializing database")
        try:
            # Apply migrations instead of creating tables directly
            apply_migrations()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise
    
    def run(self):
        """
        Run the job.
        """
        logger.info("Starting stock data job")
        start_time = time.time()
        
        try:
            # Create job run record and store its ID
            with get_db_session() as session:
                job_run = JobRun.create_job_run(session, "stock_data_job")
                session.flush()  # Ensure we have the ID
                self.job_run_id = job_run.id
                session.commit()
                logger.info(f"Created job run record with ID: {self.job_run_id}")
            
            # Get list of tickers
            with get_db_session() as session:
                tickers = self._get_tickers(session)
            logger.info(f"Found {len(tickers)} tickers to process")
            
            # Process tickers in batches
            self._process_tickers(tickers)
            
            # Mark job as completed using a fresh session
            with get_db_session() as session:
                job_run = session.query(JobRun).get(self.job_run_id)
                job_run.complete(session, self.processed_count)
            
            end_time = time.time()
            logger.info(f"Job completed successfully in {end_time - start_time:.2f} seconds")
            logger.info(f"Processed {self.processed_count} stocks with {self.error_count} errors")
            
            return True
        except Exception as e:
            logger.error(f"Error running job: {str(e)}")
            logger.error(traceback.format_exc())
            
            # Mark job as failed using a fresh session
            if self.job_run_id:
                with get_db_session() as session:
                    job_run = session.query(JobRun).get(self.job_run_id)
                    if job_run:
                        job_run.fail(session, str(e), self.processed_count)
            
            return False
    
    def _get_delisted_tickers(self) -> List[str]:
        """
        Get list of delisted tickers using Alpha Vantage Listing Status API.
        
        Returns:
            List[str]: List of delisted tickers
        """
        logger.info("Fetching list of delisted tickers from Alpha Vantage")
        
        try:
            # Construct API URL
            api_key = settings.ALPHA_VANTAGE_API_KEY
            if not api_key:
                raise ValueError("Alpha Vantage API key not configured. Please set ALPHA_VANTAGE_API_KEY in your environment variables.")

            base_url = settings.ALPHA_VANTAGE_BASE_URL
            function = settings.ALPHA_VANTAGE_LISTING_FUNCTION

            url = f"{base_url}?function={function}&state=delisted&apikey={api_key}"
            
            # Make API request
            logger.info(f"Requesting delisted ticker list from Alpha Vantage API...")
            response = requests.get(url)
            response.raise_for_status()

            # Parse CSV response
            delisted_data = pd.read_csv(io.StringIO(response.text))
            
            # Filter for US exchanges
            us_exchanges = ['NYSE', 'NASDAQ', 'AMEX', 'BATS', 'NYSEARCA', 'NYSEAMERICAN']
            us_delisted = delisted_data[delisted_data['exchange'].isin(us_exchanges)]
            
            # Filter out ETFs based on assetType field
            if 'assetType' in us_delisted.columns:
                stocks_only = us_delisted[us_delisted['assetType'] == 'Stock']
                logger.info(f"Filtered out {len(us_delisted) - len(stocks_only)} ETFs and other non-stock assets")
            else:
                stocks_only = us_delisted
                logger.warning("assetType column not found in Alpha Vantage response, unable to filter out ETFs")

            # Extract ticker symbols and clean them
            delisted_tickers = stocks_only['symbol'].tolist()
            
            # Filter tickers: must be non-null and no hyphens
            delisted_tickers = [
                str(ticker) for ticker in delisted_tickers 
                if pd.notna(ticker) and '-' not in str(ticker)
            ]

            logger.info(f"Found {len(delisted_tickers)} unique delisted tickers from Alpha Vantage")
            
            # Store delisted tickers in the database
            with get_db_session() as session:
                # Get existing delisted tickers
                existing_delisted = set(ticker[0] for ticker in session.query(DelistTicker.ticker).all())
                
                # Add new delisted tickers
                for ticker in delisted_tickers:
                    ticker = str(ticker).strip().upper()
                    if ticker not in existing_delisted:
                        try:
                            session.add(DelistTicker(ticker=ticker))
                            session.flush()  # Try to flush each insert individually
                        except SQLAlchemyError as e:
                            # If there's an integrity error, just skip this ticker
                            session.rollback()
                            logger.warning(f"Skipping duplicate delisted ticker: {ticker}")
                            continue
                
                session.commit()
                logger.info(f"Stored {len(delisted_tickers)} delisted tickers in the database")
            
            return delisted_tickers
        except Exception as e:
            logger.error(f"Error fetching delisted tickers from Alpha Vantage: {str(e)}")
            logger.error(traceback.format_exc())
            
            # Return existing delisted tickers from the database as a fallback
            with get_db_session() as session:
                existing_delisted = [ticker[0] for ticker in session.query(DelistTicker.ticker).all()]
                logger.info(f"Using {len(existing_delisted)} existing delisted tickers from the database")
                return existing_delisted
    
    def _get_tickers(self, session) -> List[str]:
        return [row[0] for row in session.query(AllTicker.ticker).all()]
    
    def _get_ticker_detail(self, session, ticker: str) -> AllTicker:
        return session.query(AllTicker).filter(AllTicker.ticker == ticker).first()
    
    def _process_tickers(self, tickers: List[str]):
        """
        Process tickers in batches using the YFinanceWrapper.
        
        Args:
            tickers: List of tickers to process
        """
        logger.info(f"Processing {len(tickers)} tickers with initial batch size of {self.batch_size}")
        
        # Define a function to process each batch that will be passed to the wrapper
        def process_batch_func(tickers_data, batch_symbols):
            batch_results = []
            for ticker_symbol in batch_symbols:
                try:
                    # Get the ticker from the batch
                    ticker = tickers_data.tickers[ticker_symbol]
                    
                    # Process the ticker
                    self._process_ticker(ticker_symbol, ticker)
                    
                    # Increment processed count
                    self.processed_count += 1
                    batch_results.append(ticker_symbol)
                except Exception as e:
                    logger.error(f"Error processing ticker {ticker_symbol}: {str(e)}")
                    logger.error(traceback.format_exc())
                    self.error_count += 1
            
            # Increment batch counter and update index memberships every 5 batches
            # self.batch_counter += 1
            # if self.batch_counter % 5 == 0:
            #     logger.info(f"Updating index memberships after {self.batch_counter} batches")
            #     self._update_index_memberships()
            
            return batch_results
        
        # Use the wrapper to process tickers in adaptive batches
        try:
            results = self.yf_wrapper.process_tickers_in_adaptive_batches(
                tickers,
                process_batch_func,
                initial_batch_size=self.batch_size,
                delay_between_batches=self.delay
            )
            
            # Log the final optimal batch size for future reference
            if self.yf_wrapper.current_optimal_batch_size != self.batch_size:
                logger.info(f"Adjusted optimal batch size to {self.yf_wrapper.current_optimal_batch_size} due to rate limiting")
                
        except Exception as e:
            logger.error(f"Error processing tickers: {str(e)}")
            logger.error(traceback.format_exc())
            self.error_count += len(tickers)
    
    def _process_batch(self, batch: List[str]):
        """
        Process a batch of tickers.
        
        Args:
            batch: List of tickers to process
        """
        logger.debug(f"Fetching data for batch: {batch}")
        
        try:
            # Use the wrapper to fetch data for the batch with retry logic
            tickers_data = self.yf_wrapper.get_tickers_batch(batch)
            
            # Process each ticker
            for ticker_symbol in batch:
                try:
                    # Get the ticker from the batch
                    ticker = tickers_data.tickers[ticker_symbol]
                    
                    # Process the ticker
                    self._process_ticker(ticker_symbol, ticker)
                    
                    # Increment processed count
                    self.processed_count += 1
                except Exception as e:
                    logger.error(f"Error processing ticker {ticker_symbol}: {str(e)}")
                    logger.error(traceback.format_exc())
                    self.error_count += 1
        except YFRateLimitError as e:
            # This should be caught by the wrapper's retry logic, but just in case
            logger.error(f"Rate limit exceeded when fetching batch data: {str(e)}")
            logger.error(traceback.format_exc())
            # Reduce batch size for next time and add delay
            self.batch_size = max(5, int(self.batch_size * 0.5))
            logger.warning(f"Reducing batch size to {self.batch_size} due to rate limiting")
            time.sleep(10)  # Add extra delay before retrying
            # Retry this batch with reduced size
            self._process_batch(batch)
        except Exception as e:
            logger.error(f"Error fetching data for batch: {str(e)}")
            logger.error(traceback.format_exc())
            self.error_count += len(batch)
    
    def _process_ticker(self, ticker_symbol: str, ticker: yf.Ticker, max_retries: int = 5):
        """
        Process a single ticker.
        
        Args:
            ticker_symbol: Ticker symbol
            ticker: yfinance Ticker object
            max_retries: Maximum number of retry attempts for fetching ticker info
        """
        logger.debug(f"Processing ticker: {ticker_symbol}")
        
        try:
            info = None
            retry_count = 0
            retry_delay = 60  # Initial delay in seconds
            
            while info is None and retry_count <= max_retries:
                try:
                    # Get stock info
                    info = self.yf_wrapper.get_ticker_info(ticker_symbol)
                    
                    # If we've retried and succeeded, log it
                    if retry_count > 0 and info:
                        logger.info(f"Successfully retrieved ticker {ticker_symbol} info after {retry_count} retries")
                        
                except Exception as e:
                    logger.warning(f"Attempt {retry_count + 1}/{max_retries + 1} to get ticker {ticker_symbol} info failed: {str(e)}")
                    
                # If still no info, retry with exponential backoff unless we've hit max retries
                if info is None:
                    retry_count += 1
                    if retry_count <= max_retries:
                        wait_time = retry_delay * (2 ** (retry_count - 1))  # Exponential backoff
                        logger.info(f"No info available for ticker {ticker_symbol}, retrying in {wait_time} seconds (attempt {retry_count}/{max_retries})")
                        time.sleep(wait_time)
                    else:
                        logger.warning(f"No info available for ticker {ticker_symbol} after {max_retries + 1} attempts, skipping")
            
            # Skip if no info available after all retries
            if not info:
                logger.warning(f"No info available for ticker {ticker_symbol} after all retries, skipping")
                return
            
            # Store basic stock info
            self._store_stock_info(ticker_symbol, info)
            
            # Store price history
            self._store_price_history(ticker_symbol, ticker)
            
            # Store fundamental data
            self._store_fundamental_data(ticker_symbol, info)
            
            logger.debug(f"Successfully processed ticker: {ticker_symbol}")
        except Exception as e:
            logger.error(f"Error processing ticker {ticker_symbol}: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    
    def _store_stock_info(self, ticker_symbol: str, info: Dict[str, Any]):
        """Store basic stock info in the database."""
        logger.debug(f"Storing basic info for ticker: {ticker_symbol}")
        
        def convert_value(value: Any) -> Any:
            """Convert values to appropriate format for database storage."""
            if value is None:
                return None
            if isinstance(value, (list, dict)):
                return json.dumps(value)
            if isinstance(value, bool):
                return 1 if value else 0
            if isinstance(value, (int, float)):
                return str(value)
            return value

        try:
            with get_db_session() as session:
                # We'll update indexes separately
                ticker_detail = self._get_ticker_detail(session, ticker_symbol)
                indices = []
                if ticker_detail.is_sp_500:
                    indices.append("SPX")
                if ticker_detail.is_nasdap_100:
                    indices.append("NDX")
                if ticker_detail.is_nasdaq_composite:
                    indices.append("IXIC")
                if ticker_detail.is_dow_jones:
                    indices.append("DJIA")
                if ticker_detail.is_russell_2000:
                    indices.append("RUT")
                indexes = ",".join(indices)

                existing_stock = session.query(Stock).filter(Stock.ticker == ticker_symbol).first()
                
                # Convert all values to appropriate format before storing
                stock_data = {k: convert_value(v) for k, v in {
                    # Primary identifiers
                    'ticker': ticker_symbol,
                    'name': info.get('shortName', info.get('longName', ticker_symbol)),
                    'indexes': indexes,
                    'last_updated': datetime.datetime.now(),
                    
                    # Company information
                    'address1': info.get('address1'),
                    'city': info.get('city'),
                    'state': info.get('state'),
                    'zip': info.get('zip'),
                    'country': ticker_detail.country,
                    'phone': info.get('phone'),
                    'website': info.get('website'),
                    
                    # Industry classification
                    'industry': info.get('industry'),
                    'industry_key': info.get('industryKey'),
                    'industry_disp': info.get('industryDisp'),
                    'sector': info.get('sector'),
                    'sector_key': info.get('sectorKey'),
                    'sector_disp': info.get('sectorDisp'),
                    
                    # Company details
                    'long_business_summary': info.get('longBusinessSummary') if info.get('fullTimeEmployees') is not None else None,
                    'full_time_employees': str(info.get('fullTimeEmployees')) if info.get('fullTimeEmployees') is not None else None,
                    'company_officers': json.dumps(info.get('companyOfficers')) if info.get('companyOfficers') is not None else None,
                    
                    # Risk metrics
                    'audit_risk': info.get('auditRisk'),
                    'board_risk': info.get('boardRisk'),
                    'compensation_risk': info.get('compensationRisk'),
                    'shareholder_rights_risk': info.get('shareHolderRightsRisk'),
                    'overall_risk': info.get('overallRisk'),
                    
                    # Governance and IR
                    'governance_epoch_date': info.get('governanceEpochDate'),
                    'compensation_as_of_epoch_date': info.get('compensationAsOfEpochDate'),
                    'ir_website': info.get('irWebsite'),
                    
                    # Market data
                    'max_age': info.get('maxAge'),
                    'price_hint': info.get('priceHint'),
                    'previous_close': info.get('previousClose'),
                    'open_price': info.get('open'),
                    'day_low': info.get('dayLow'),
                    'day_high': info.get('dayHigh'),
                    'regular_market_previous_close': info.get('regularMarketPreviousClose'),
                    'regular_market_open': info.get('regularMarketOpen'),
                    'regular_market_day_low': info.get('regularMarketDayLow'),
                    'regular_market_day_high': info.get('regularMarketDayHigh'),
                    'regular_market_price': info.get('regularMarketPrice'),
                    'regular_market_volume': info.get('regularMarketVolume'),
                    'regular_market_change': info.get('regularMarketChange'),
                    'regular_market_change_percent': info.get('regularMarketChangePercent'),
                    'regular_market_time': info.get('regularMarketTime'),
                    
                    # Pre/Post market
                    'pre_market_price': info.get('preMarketPrice'),
                    'pre_market_change': info.get('preMarketChange'),
                    'pre_market_change_percent': info.get('preMarketChangePercent'),
                    'post_market_time': info.get('postMarketTime'),
                    
                    # Dividend information
                    'dividend_rate': info.get('dividendRate'),
                    'dividend_yield': info.get('dividendYield'),
                    'ex_dividend_date': info.get('exDividendDate'),
                    'dividend_date': info.get('dividendDate'),
                    'payout_ratio': info.get('payoutRatio'),
                    'five_year_avg_dividend_yield': info.get('fiveYearAvgDividendYield'),
                    'trailing_annual_dividend_rate': info.get('trailingAnnualDividendRate'),
                    'trailing_annual_dividend_yield': info.get('trailingAnnualDividendYield'),
                    'last_dividend_value': info.get('lastDividendValue'),
                    'last_dividend_date': info.get('lastDividendDate'),
                    
                    # Trading metrics
                    'beta': info.get('beta'),
                    'volume': info.get('volume'),
                    'average_volume': info.get('averageVolume'),
                    'average_daily_volume_10_day': info.get('averageDailyVolume10Day'),
                    'bid': info.get('bid'),
                    'ask': info.get('ask'),
                    'bid_size': info.get('bidSize'),
                    'ask_size': info.get('askSize'),
                    
                    # Market cap and shares
                    'market_cap': info.get('marketCap'),
                    'enterprise_value': info.get('enterpriseValue'),
                    'shares_outstanding': info.get('sharesOutstanding'),
                    'float_shares': info.get('floatShares'),
                    'implied_shares_outstanding': info.get('impliedSharesOutstanding'),
                    
                    # Price metrics
                    'fifty_two_week_low': info.get('fiftyTwoWeekLow'),
                    'fifty_two_week_high': info.get('fiftyTwoWeekHigh'),
                    'fifty_two_week_low_change': info.get('fiftyTwoWeekLowChange'),
                    'fifty_two_week_low_change_percent': info.get('fiftyTwoWeekLowChangePercent'),
                    'fifty_two_week_range': info.get('fiftyTwoWeekRange'),
                    'fifty_two_week_high_change': info.get('fiftyTwoWeekHighChange'),
                    'fifty_two_week_high_change_percent': info.get('fiftyTwoWeekHighChangePercent'),
                    'fifty_two_week_change_percent': info.get('fiftyTwoWeekChangePercent'),
                    'fifty_day_average': info.get('fiftyDayAverage'),
                    'fifty_day_average_change': info.get('fiftyDayAverageChange'),
                    'two_hundred_day_average': info.get('twoHundredDayAverage'),
                    'two_hundred_day_average_change': info.get('twoHundredDayAverageChange'),
                    
                    # Financial ratios
                    'trailing_pe': info.get('trailingPE'),
                    'forward_pe': info.get('forwardPE'),
                    'price_to_book': info.get('priceToBook'),
                    'price_to_sales_trailing_12_months': info.get('priceToSalesTrailing12Months'),
                    'price_eps_current_year': info.get('priceEpsCurrentYear'),

                    # Short interest
                    'shares_short': info.get('sharesShort'),
                    'short_ratio': info.get('shortRatio'),
                    'shares_short_prior_month': info.get('sharesShortPriorMonth'),
                    'shares_short_previous_month_date': info.get('sharesShortPreviousMonthDate'),
                    'date_short_interest': info.get('dateShortInterest'),
                    'shares_percent_shares_out': info.get('sharesPercentSharesOut'),
                    'held_percent_insiders': info.get('heldPercentInsiders'),
                    'held_percent_institutions': info.get('heldPercentInstitutions'),
                    'short_percent_of_float': info.get('shortPercentOfFloat'),
                    
                    # Financial metrics
                    'book_value': info.get('bookValue'),
                    'earnings_quarterly_growth': info.get('earningsQuarterlyGrowth'),
                    'net_income_to_common': info.get('netIncomeToCommon'),
                    'trailing_eps': info.get('trailingEps'),
                    'forward_eps': info.get('forwardEps'),
                    'eps_trailing_twelve_months': info.get('epsTrailingTwelveMonths'),
                    'eps_forward': info.get('epsForward'),
                    'eps_current_year': info.get('epsCurrentYear'),
                    'enterprise_to_revenue': info.get('enterpriseToRevenue'),
                    'enterprise_to_ebitda': info.get('enterpriseToEbitda'),

                     # Financial statements
                    'total_cash': info.get('totalCash'),
                    'total_debt': info.get('totalDebt'),
                    'quick_ratio': info.get('quickRatio'),
                    'current_ratio': info.get('currentRatio'),
                    'total_revenue': info.get('totalRevenue'),
                    'debt_to_equity': info.get('debtToEquity'),
                    'revenue_per_share': info.get('revenuePerShare'),
                    'return_on_assets': info.get('returnOnAssets'),
                    'return_on_equity': info.get('returnOnEquity'),
                    'gross_profits': info.get('grossProfits'),
                    'free_cashflow': info.get('freeCashflow'),
                    'operating_cashflow': info.get('operatingCashflow'),
                    'earnings_growth': info.get('earningsGrowth'),
                    'revenue_growth': info.get('revenueGrowth'),
                    'gross_margins': info.get('grossMargins'),
                    'ebitda_margins': info.get('ebitdaMargins'),
                    'operating_margins': info.get('operatingMargins'),
                    'ebitda': info.get('ebitda'),
                    'total_cash_per_share': info.get('totalCashPerShare'),

                    # Exchange information
                    'exchange': info.get('exchange'),
                    'full_exchange_name': info.get('fullExchangeName'),
                    'exchange_timezone_name': info.get('exchangeTimezoneName'),
                    'exchange_timezone_short_name': info.get('exchangeTimezoneShortName'),
                    'gmt_offset_milliseconds': info.get('gmtOffSetMilliseconds'),

                    # Market analysis
                    'current_price': info.get('currentPrice'),
                    'target_high_price': info.get('targetHighPrice'),
                    'target_low_price': info.get('targetLowPrice'),
                    'target_mean_price': info.get('targetMeanPrice'),
                    'recommendation_mean': info.get('recommendationMean'),
                    'recommendation_key': info.get('recommendationKey'),
                    'number_of_analyst_opinions': info.get('numberOfAnalystOpinions'),
                    'average_analyst_rating': info.get('averageAnalystRating'),
                    
                    # Trading status
                    'market_state': info.get('marketState'),
                    'quote_source_name': info.get('quoteSourceName'),
                    'tradeable': info.get('tradeable'),
                    'crypto_tradeable': info.get('cryptoTradeable'),
                    'currency': info.get('currency'),
                    'exchange_data_delayed_by': info.get('exchangeDataDelayedBy'),
                    
                    # Other fields
                    'executive_team': json.dumps(info.get('executiveTeam')) if info.get('executiveTeam') is not None else None,
                    'quote_type': info.get('quoteType'),
                    'language': info.get('language'),
                    'region': info.get('region'),
                    'market': info.get('market'),
                    'type_disp': info.get('typeDisp'),
                    'source_interval': info.get('sourceInterval'),
                    'message_board_id': info.get('messageBoardId'),
                    'custom_price_alert_confidence': info.get('customPriceAlertConfidence'),
                    'corporate_actions': info.get('corporateActions'),
                    'financial_currency': info.get('financialCurrency'),
                    
                    # Timestamps
                    'last_fiscal_year_end': info.get('lastFiscalYearEnd'),
                    'next_fiscal_year_end': info.get('nextFiscalYearEnd'),
                    'most_recent_quarter': info.get('mostRecentQuarter'),
                    'last_split_date': info.get('lastSplitDate'),
                    'last_split_factor': info.get('lastSplitFactor'),
                    'first_trade_date_milliseconds': info.get('firstTradeDateMilliseconds'),
                    'earnings_timestamp': info.get('earningsTimestamp'),
                    'earnings_timestamp_start': info.get('earningsTimestampStart'),
                    'earnings_timestamp_end': info.get('earningsTimestampEnd'),
                    'earnings_call_timestamp_start': info.get('earningsCallTimestampStart'),
                    'earnings_call_timestamp_end': info.get('earningsCallTimestampEnd'),
                    'is_earnings_date_estimate': info.get('isEarningsDateEstimate'),
                    'esg_populated': info.get('esgPopulated'),
                    'has_pre_post_market_data': info.get('hasPrePostMarketData'),
                }.items()}
                
                if existing_stock and not self.full_refresh:
                    logger.debug(f"Stock {ticker_symbol} already exists, updating")
                    # Update existing stock with all fields
                    for key, value in stock_data.items():
                        setattr(existing_stock, key, value)
                else:
                    logger.debug(f"Creating new stock record for {ticker_symbol}")
                    # Create new stock
                    stock = Stock(**stock_data)
                    session.add(stock)
                
                session.commit()
                logger.debug(f"Successfully stored basic info for ticker: {ticker_symbol}")
        except SQLAlchemyError as e:
            logger.error(f"Database error storing stock info for {ticker_symbol}: {str(e)}")
            raise
    
    def _store_price_history(self, ticker_symbol: str, ticker: yf.Ticker):
        """Store price history in the database."""
        logger.debug(f"Storing price history for ticker: {ticker_symbol}")
        
        try:
            # Get price history for the last N days
            end_date = datetime.datetime.now()
            start_date = end_date - datetime.timedelta(days=self.price_days)
            
            # Fetch history with retry logic
            try:
                history = self.yf_wrapper.get_ticker_history(
                    ticker_symbol,
                    start=start_date,
                    end=end_date
                )
            except Exception:
                # Fallback to direct ticker.history if wrapper fails
                history = ticker.history(start=start_date, end=end_date)
            
            # Skip if no history available
            if history.empty:
                logger.warning(f"No price history available for ticker {ticker_symbol}")
                return
            
            with get_db_session() as session:
                # Process each day
                for date, row in history.iterrows():
                    # Convert date to datetime.date
                    date_only = date.date()
                    
                    # Handle NaN values by converting them to None
                    price_data = {
                        'ticker': ticker_symbol,
                        'date': date_only,
                        'open': float(row['Open']) if pd.notna(row.get('Open')) else None,
                        'high': float(row['High']) if pd.notna(row.get('High')) else None,
                        'low': float(row['Low']) if pd.notna(row.get('Low')) else None,
                        'close': float(row['Close']) if pd.notna(row.get('Close')) else None,
                        'volume': int(row['Volume']) if pd.notna(row.get('Volume')) else None
                    }
                    
                    # Check if price already exists for this date
                    existing_price = session.query(StockPrice).filter(
                        StockPrice.ticker == ticker_symbol,
                        StockPrice.date == date_only
                    ).first()
                    
                    if existing_price and not self.full_refresh:
                        logger.debug(f"Price for {ticker_symbol} on {date_only} already exists, updating")
                        # Update existing price
                        for key, value in price_data.items():
                            if key not in ('ticker', 'date'):  # Skip primary key fields
                                setattr(existing_price, key, value)
                    else:
                        logger.debug(f"Creating new price record for {ticker_symbol} on {date_only}")
                        # Create new price record
                        price = StockPrice(**price_data)
                        session.add(price)
                
                session.commit()
                logger.debug(f"Successfully stored price history for ticker: {ticker_symbol}")
        except SQLAlchemyError as e:
            logger.error(f"Database error storing price history for {ticker_symbol}: {str(e)}")
            raise
    
    def _clean_numeric_value(self, value: Any) -> Optional[float]:
        """
        Clean numeric values by handling infinity, NaN and converting to proper type.
        
        Args:
            value: Value to clean

        Returns:
            Optional[float]: Cleaned value or None if invalid
        """
        if value is None:
            return None
        try:
            float_val = float(value)
            if float_val in (float('inf'), float('-inf')) or pd.isna(float_val):
                return None
            return float_val
        except (ValueError, TypeError):
            return None

    def _store_fundamental_data(self, ticker_symbol: str, info: Dict[str, Any]):
        """Store fundamental data in the database."""
        logger.debug(f"Storing fundamental data for ticker: {ticker_symbol}")
        
        try:
            with get_db_session() as session:
                today = datetime.date.today()

                existing_fundamental = session.query(StockFundamental).filter(
                    StockFundamental.ticker == ticker_symbol,
                    StockFundamental.date == today
                ).first()
                
                if existing_fundamental and not self.full_refresh:
                    logger.debug(f"Fundamental data for {ticker_symbol} on {today} already exists, updating")
                    
                    existing_fundamental.dividend_rate = self._clean_numeric_value(info.get('dividendRate'))
                    existing_fundamental.dividend_yield = self._clean_numeric_value(info.get('dividendYield', 0) * 100 if info.get('dividendYield') else None)
                    existing_fundamental.pe_ratio = self._clean_numeric_value(info.get('trailingPE'))
                    existing_fundamental.eps = self._clean_numeric_value(info.get('trailingEps'))
                    existing_fundamental.beta = self._clean_numeric_value(info.get('beta'))
                    existing_fundamental.fifty_two_week_high = self._clean_numeric_value(info.get('fiftyTwoWeekHigh'))
                    existing_fundamental.fifty_two_week_low = self._clean_numeric_value(info.get('fiftyTwoWeekLow'))
                else:
                    logger.debug(f"Creating new fundamental data record for {ticker_symbol} on {today}")

                    fundamental = StockFundamental(
                        ticker=ticker_symbol,
                        date=today,
                        dividend_rate=self._clean_numeric_value(info.get('dividendRate')),
                        dividend_yield=self._clean_numeric_value(info.get('dividendYield', 0) * 100 if info.get('dividendYield') else None),
                        pe_ratio=self._clean_numeric_value(info.get('trailingPE')),
                        eps=self._clean_numeric_value(info.get('trailingEps')),
                        beta=self._clean_numeric_value(info.get('beta')),
                        fifty_two_week_high=self._clean_numeric_value(info.get('fiftyTwoWeekHigh')),
                        fifty_two_week_low=self._clean_numeric_value(info.get('fiftyTwoWeekLow'))
                    )
                    session.add(fundamental)
                
                session.commit()
                logger.debug(f"Successfully stored fundamental data for ticker: {ticker_symbol}")
        except SQLAlchemyError as e:
            logger.error(f"Database error storing fundamental data for {ticker_symbol}: {str(e)}")
            raise

    def _get_sp500_tickers(self) -> List[str]:
        """
        Get list of S&P 500 tickers from Wikipedia.

        Returns:
            List[str]: List of S&P 500 tickers
        """
        logger.info("Fetching S&P 500 tickers from Wikipedia")
        try:
            url = 'https://en.wikipedia.org/wiki/List_of_S%26P_500_companies'
            tables = pd.read_html(url)
            sp500_df = tables[0]
            tickers = sp500_df['Symbol'].tolist()
            # Clean tickers (replace dots with hyphens to match our format)
            tickers = [ticker.replace('.', '-') for ticker in tickers]
            logger.info(f"Found {len(tickers)} S&P 500 tickers")
            return tickers
        except Exception as e:
            logger.error(f"Error fetching S&P 500 tickers: {str(e)}")
            logger.error(traceback.format_exc())
            return []
    
    def _get_nasdaq100_tickers(self) -> List[str]:
        """
        Get list of Nasdaq-100 tickers from Wikipedia.

        Returns:
            List[str]: List of Nasdaq-100 tickers
        """
        logger.info("Fetching Nasdaq-100 tickers from Wikipedia")
        try:
            url = 'https://en.wikipedia.org/wiki/Nasdaq-100'
            tables = pd.read_html(url)
            # The table with tickers is usually the 4th table on the page
            nasdaq_df = tables[4]
            tickers = nasdaq_df['Ticker'].tolist()
            # Clean tickers (replace dots with hyphens to match our format)
            tickers = [ticker.replace('.', '-') for ticker in tickers]
            logger.info(f"Found {len(tickers)} Nasdaq-100 tickers")
            return tickers
        except Exception as e:
            logger.error(f"Error fetching Nasdaq-100 tickers: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def _get_nasdaq_composite_tickers(self) -> List[str]:
        """
        Get list of Nasdaq Composite tickers from Wikipedia category pages.
        Iterates through all pages (0-9 and A-Z) to get a comprehensive list.

        Returns:
            List[str]: List of Nasdaq Composite tickers
        """
        logger.info("Fetching Nasdaq Composite tickers from Wikipedia")
        try:
            from bs4 import BeautifulSoup
            
            # Define all possible starting points (0-9 and A-Z)
            starting_points = ['0-9'] + list('ABCDEFGHIJKLMNOPQRSTUVWXYZ')
            
            all_company_names = []
            
            # Iterate through each starting point
            for start_point in starting_points:
                url = f'https://en.wikipedia.org/w/index.php?title=Category:Companies_listed_on_the_Nasdaq&from={start_point}'
                logger.debug(f"Fetching Nasdaq companies starting with '{start_point}' from {url}")
                
                try:
                    # Make request to the category page
                    response = requests.get(url)
                    response.raise_for_status()
                    
                    # Parse the HTML
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # Find the category content
                    category_content = soup.find('div', {'id': 'mw-pages'})
                    if not category_content:
                        logger.warning(f"Could not find category content on the page for '{start_point}'")
                        continue
                    
                    # Find all links in the category content
                    links = category_content.find_all('a')
                    
                    # Extract company names
                    page_company_names = []
                    for link in links:
                        # Skip navigation links
                        if link.text in ['previous page', 'next page', 'Category:Companies listed on the Nasdaq']:
                            continue
                        
                        # Add company name
                        page_company_names.append(link.text)
                    
                    logger.debug(f"Found {len(page_company_names)} companies starting with '{start_point}'")
                    all_company_names.extend(page_company_names)
                    
                except Exception as e:
                    logger.error(f"Error fetching Nasdaq companies starting with '{start_point}': {str(e)}")
                    continue
            
            logger.info(f"Found {len(all_company_names)} total Nasdaq Composite companies")
            
            # Return company names directly instead of mapping to tickers
            # This is per the requirement to use stock names instead of tickers for NASDAQ Composite
            if all_company_names:
                logger.info(f"Returning {len(all_company_names)} Nasdaq Composite company names")
                return all_company_names
            
            # Fallback to a list of known company names if scraping failed
            fallback_company_names = [
                'Apple Inc.', 'Microsoft Corporation', 'Alphabet Inc.', 'Amazon.com Inc.', 
                'Meta Platforms Inc.', 'Tesla Inc.', 'NVIDIA Corporation', 'Netflix Inc.', 
                'Adobe Inc.', 'Intel Corporation', 'Cisco Systems Inc.', 'Comcast Corporation',
                'PepsiCo Inc.', 'Broadcom Inc.', 'Costco Wholesale Corporation', 
                'T-Mobile US Inc.', 'Texas Instruments Inc.', 'Qualcomm Inc.', 
                'Amgen Inc.', 'Starbucks Corporation'
            ]
            
            logger.warning(f"Using fallback list of {len(fallback_company_names)} Nasdaq Composite company names")
            return fallback_company_names
        except Exception as e:
            logger.error(f"Error fetching Nasdaq Composite tickers: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def _get_dow_tickers(self) -> List[str]:
        """
        Get list of Dow Jones Industrial Average tickers from Wikipedia.

        Returns:
            List[str]: List of Dow Jones tickers
        """
        logger.info("Fetching Dow Jones tickers from Wikipedia")
        try:
            url = 'https://en.wikipedia.org/wiki/Dow_Jones_Industrial_Average'
            tables = pd.read_html(url)
            # Find the table with the current components
            for table in tables:
                if 'Symbol' in table.columns:
                    dow_df = table
                    break
            else:
                logger.error("Could not find Dow Jones components table")
                return []

            tickers = dow_df['Symbol'].tolist()
            # Clean tickers (replace dots with hyphens to match our format)
            tickers = [ticker.replace('.', '-') for ticker in tickers]
            logger.info(f"Found {len(tickers)} Dow Jones tickers")
            return tickers
        except Exception as e:
            logger.error(f"Error fetching Dow Jones tickers: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def _get_russell2000_tickers(self) -> List[str]:
        """
        Get list of Russell 2000 tickers from annualreports.com.

        Returns:
            List[str]: List of Russell 2000 tickers
        """
        logger.info("Fetching Russell 2000 tickers from annualreports.com")
        try:
            from bs4 import BeautifulSoup
            import re
            import string
            
            # URL specified in the task
            url = 'https://www.annualreports.com/FeaturedProgram/15'
            
            # Make request to the website
            response = requests.get(url)
            response.raise_for_status()
            
            # Parse the HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find all company rows in the table
            company_section = soup.find_all('section', class_='category_section')
            if len(company_section) == 0:
                logger.error("Could not find company section in the Russell 2000 table")
                return []
            spans = company_section[0].find_all('span', class_='companyName')
            
            # Extract company names
            company_names = []
            for row in spans:
                company_names.append(row.text.strip())
            
            logger.info(f"Found {len(company_names)} Russell 2000 company names")
            
            # Return company names directly instead of mapping to tickers
            # This is per the requirement to use stock names instead of tickers for Russell 2000
            if company_names:
                logger.info(f"Returning {len(company_names)} Russell 2000 company names")
                return company_names
            
            # Fallback to a list of known company names if scraping failed
            fallback_company_names = [
                'U.S. Concrete', '1-800-FLOWERS.COM', '1st Source Corporation', '2U Inc.',
                'AAON Inc.', 'ABM Industries', 'Acadia Healthcare', 'Acadia Pharmaceuticals',
                'Accelerate Diagnostics', 'Accel Entertainment', 'Acer Therapeutics',
                'Aci Worldwide', 'Aclaris Therapeutics', 'Acme United Corporation',
                'Acorda Therapeutics', 'Adamas Pharmaceuticals', 'Adamis Pharmaceuticals',
                'Addus HomeCare', 'Adesto Technologies', 'Adtalem Global Education',
                'Advanced Energy Industries', 'Advanced Emissions Solutions', 'Advansix Inc.',
                'Aegion Corporation', 'Aerie Pharmaceuticals', 'Aerojet Rocketdyne Holdings',
                'Agilysys Inc.', 'Agios Pharmaceuticals', 'Aimmune Therapeutics',
                'Air Transport Services Group', 'Akamai Technologies', 'Akero Therapeutics',
                'Akoustis Technologies', 'Albany International', 'Albireo Pharma',
                'Alcentra Capital Corporation', 'Alector Inc.', 'Alexander & Baldwin',
                'Allegiant Travel Company', 'Allegiance Bancshares', 'Alliance Data Systems',
                'Allied Motion Technologies', 'Allscripts Healthcare Solutions'
            ]
            
            logger.warning(f"Using fallback list of {len(fallback_company_names)} Russell 2000 company names")
            return fallback_company_names
        except Exception as e:
            logger.error(f"Error fetching Russell 2000 tickers: {str(e)}")
            logger.error(traceback.format_exc())
            return []

    def _update_index_memberships(self):
        """
        Update index memberships for all tickers in the database.
        Uses pre-fetched index membership data.
        """
        logger.info("Updating index memberships for tickers")

        try:
            # Create sets for faster lookups using stored values
            sp500_set = set(self.sp500_tickers)
            nasdaq100_set = set(self.nasdaq100_tickers)
            nasdaq_composite_set = set(self.nasdaq_composite_tickers)
            dow_set = set(self.dow_tickers)
            russell2000_set = set(self.russell2000_tickers)

            # Update database records
            with get_db_session() as session:
                # Get all stocks from database
                stocks = session.query(Stock).all()

                update_count = 0
                for stock in stocks:
                    indexes = []

                    # Check if ticker is in each index
                    if stock.ticker in sp500_set:
                        indexes.append("SPX")
                    if stock.ticker in nasdaq100_set:
                        indexes.append("NDX")
                    # For NASDAQ Composite and Russell 2000, use stock name instead of ticker
                    if stock.name and any(stock.name.lower() in company.lower() for company in nasdaq_composite_set):
                        indexes.append("IXIC")
                    if stock.ticker in dow_set:
                        indexes.append("DJIA")
                    if stock.name and any(stock.name.lower() in company.lower() for company in russell2000_set):
                        indexes.append("RUT")
                    
                    # Update the indexes field if any indices found
                    if indexes:
                        stock.indexes = ",".join(indexes)
                        update_count += 1

                # Commit changes
                session.commit()

            logger.info(f"Updated index memberships for {update_count} stocks")
        except Exception as e:
            logger.error(f"Error updating index memberships: {str(e)}")
            logger.error(traceback.format_exc())


def parse_args():
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description='Fetch US stock market data and store it in MySQL database')
    parser.add_argument('--full-refresh', action='store_true', help='Force refresh of all data')
    parser.add_argument('--tickers', type=str, default=settings.JOB_SPECIFIC_TICKERS, help='Comma-separated list of specific tickers to process')
    parser.add_argument('--batch-size', type=int, default=settings.JOB_BATCH_SIZE, help='Number of stocks to process in each batch')
    parser.add_argument('--delay', type=float, default=settings.JOB_DELAY, help='Delay between batches in seconds')
    parser.add_argument('--price-days', type=int, default=settings.JOB_PRICE_DAYS, help='Number of days of price history to fetch')
    parser.add_argument('--max-retries', type=int, default=5, help='Maximum number of retry attempts for rate limiting')
    parser.add_argument('--min-wait', type=float, default=60.0, help='Minimum wait time between retries in seconds')
    parser.add_argument('--max-wait', type=float, default=120.0, help='Maximum wait time between retries in seconds')
    
    return parser.parse_args()

def main():
    """
    Main entry point.
    """
    args = parse_args()
    
    # Parse tickers if provided
    specific_tickers = args.tickers.split(',') if args.tickers else None
    
    # Create and run the job
    job = StockDataJob(
        full_refresh=args.full_refresh,
        specific_tickers=specific_tickers,
        batch_size=args.batch_size,
        delay=args.delay,
        price_days=args.price_days,
        max_retries=args.max_retries,
        min_wait=args.min_wait,
        max_wait=args.max_wait
    )
    
    success = job.run()
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
