import pandas as pd
from datetime import datetime, timedelta
import time
import random
import requests

from jobs.earnings_calendar import parse_date
from utils.logging_utils import setup_logging

logger = setup_logging(enable_colors=False)

def smooth_scroll(page, current_position, target_position, steps=10):
    """
    Smoothly scroll from current_position to target_position in multiple steps.
    
    Args:
        current_position: Starting scroll position
        target_position: Target scroll position
        steps: Number of steps to use for the scroll
    """
    step_size = (target_position - current_position) / steps
    for i in range(1, steps + 1):
        next_step = current_position + (step_size * i)
        page.evaluate(f"window.scrollTo(0, {int(next_step)});")
        logger.info(f"Smooth scroll to position {next_step}")
        # Small random delay between scroll steps
        page.wait_for_timeout(random.uniform(0.5, 3)*1000)
    return target_position

# Headers (mimic browser headers, adjust as needed)
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Content-Type": "application/x-www-form-urlencoded",
    "X-Requested-With": "XMLHttpRequest",
    "Origin": "https://www.investing.com",
    "Referer": "https://www.investing.com/economic-calendar/"
}

# Example payload (modify based on your filtering needs)
payload = {
    "country[]": [25, 32, 6, 37, 72, 22, 17, 39, 14, 10, 35, 43, 56, 36, 110, 11, 26, 12, 4, 5],
    "timeZone": 8,
    "timeFilter": "timeRemain",
}

def get_economic_calendar(max_retries=3, retry_delay=5):
    """
    Alternative implementation for fetching economic calendar data using Playwright.
    
    Args:
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries in seconds
    
    Returns:
        DataFrame containing economic calendar events or None if failed
    """
    url = 'https://www.investing.com/economic-calendar/Service/getCalendarFilteredData'
    
    logger.info(f"Economic calendar loading (alternative method)")

    for attempt in range(max_retries):
        try:
            limit_from = 0
            last_time_scope = None
            events = []
            from bs4 import BeautifulSoup
            current_date = None
            for tab in ['thisWeek', 'nextWeek']:
                while True:
                    data = get_economic_calendar_data(url, limit_from, tab, last_time_scope)
                    bind_scroll_handler = data.get('bind_scroll_handler', True)
                    item = data.get('data', [])
                    soup = BeautifulSoup(item, 'html.parser')
                    rows = soup.find_all('tr')
                    for row in rows:
                        tds = row.find_all('td')
                        if len(tds) == 1:
                            date_str = tds[0].text.strip()
                            parsed_date = parse_date(date_str)
                            current_date = parsed_date
                            continue
                        
                        if len(tds) != 8:
                            continue

                        # Extract event details
                        country = tds[1].find('span').get('title')
                        impact_title = tds[2].get('title')
                        event = tds[3].find('a').text.strip()
                        actual = tds[4].text.strip()
                        forecast = tds[5].text.strip()
                        previous = tds[6].text.strip()

                        date_time = row.get('data-event-datetime', 'N/A')
                        date_time_str = datetime.strptime(date_time, '%Y/%m/%d %H:%M:%S').strftime('%Y-%m-%d %H:%M:%S') if date_time != 'N/A' else 'N/A'

                        impact_level = 'Low'
                        if 'High' in impact_title:
                            impact_level = 'High'
                        elif 'Moderate' in impact_title:
                            impact_level = 'Medium'
                        
                        # Clean and process the extracted data
                        event_details = {
                            'DateTime': date_time_str,
                            'Country': country,
                            'Impact': impact_level,
                            'Event': event,
                            'Actual': actual,
                            'Forecast': forecast,
                            'Previous': previous
                        }
                        
                        events.append(event_details)
                    
                    if not bind_scroll_handler:
                        break
                    limit_from += 1
                    last_time_scope = current_date
                    time.sleep(3)
                # reset limit_from for the next tab
                limit_from = 0
                last_time_scope = None
            
            # Convert to DataFrame
            df = pd.DataFrame(events)
            return df

        except Exception as e:
            logger.error(f"Error on attempt {attempt + 1}: {str(e)}")
            
        if attempt < max_retries - 1:
            logger.info(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
            
    logger.error("Max retries reached. Failed to fetch economic calendar.")
    return None

def get_economic_calendar_data(url, limit_from=0, tab='thisWeek', last_time_scope=None):
    p = {
        'currentTab': tab,
        "country[]": payload['country[]'],
        "timeZone": payload['timeZone'],
        "timeFilter": payload['timeFilter'],
        'limit_from': limit_from
    }
    if limit_from > 0:
        dt = datetime.combine(last_time_scope, datetime.min.time())
        dt_plus_8 = dt + timedelta(hours=8)
        p['last_time_scope'] = str(dt_plus_8.timestamp())
        p['submitFilters'] = 0
        p['byHandler'] = True
    response = requests.post(url, headers=headers, data=p)
    response.raise_for_status()
    data = response.json()
    return data