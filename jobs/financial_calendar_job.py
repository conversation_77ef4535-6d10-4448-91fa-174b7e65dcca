"""
Financial calendar job implementation for fetching economic indicators.
"""

import logging
import time
import requests
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from jobs.db.mysql_client import get_db_session
from jobs.db.schema import (
    FinancialCalendarEvent,
    EventMonitoringStatus,
    JobRun,
    Base
)
from jobs.economy_calendar import get_economic_calendar
from jobs.db.migrations.migrations import apply_migrations
from utils.logging_utils import setup_logging
from utils.pubsub_utils import PubSubPublisher
from openai import OpenAI
from config.settings import settings

logger = setup_logging(enable_colors=False)

class FinancialCalendarJob:
    def __init__(self):
        self.db_session = get_db_session()
        
        # Apply migrations instead of creating tables directly
        apply_migrations()
        
        # Initialize sources from settings
        self.default_source = (settings.FINANCIAL_CALENDAR_SOURCE 
                             if settings and hasattr(settings, 'FINANCIAL_CALENDAR_SOURCE') 
                             else 'alpha_vantage')
        
        # Define available sources directly from settings
        self.sources = [{
            'name': self.default_source.title(),
            'type': 'API',
            'url': 'https://www.alphavantage.co' if self.default_source == 'alpha_vantage' else 'https://www.investing.com',
            'api_key': settings.ALPHA_VANTAGE_API_KEY if hasattr(settings, 'ALPHA_VANTAGE_API_KEY') else None,
            'is_active': True
        }]
        
        self.real_time_mode = False
        self.check_interval = 60  # Default to 60 seconds between checks in real-time mode
        
        if settings and hasattr(settings, 'FINANCIAL_CALENDAR_REAL_TIME'):
            self.real_time_mode = settings.FINANCIAL_CALENDAR_REAL_TIME
            
        if settings and hasattr(settings, 'FINANCIAL_CALENDAR_CHECK_INTERVAL'):
            self.check_interval = settings.FINANCIAL_CALENDAR_CHECK_INTERVAL

        # Initialize PubSub publisher only if enabled
        self.enable_pubsub = settings.ENABLE_PUBSUB
        self.pubsub_publisher = PubSubPublisher(settings.PUBSUB_FINANCIAL_CALENDAR_TOPIC) if self.enable_pubsub else None
        
        # Initialize OpenAI API
        if settings and hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
            self.openai_client = OpenAI(api_key=settings.OPENAI_API_KEY)
            self.enable_openai = True
            logger.info("OpenAI API key configured. Summary generation will be enabled.")
        else:
            self.enable_openai = False
            logger.info("OpenAI API key not configured. Summary generation will be disabled.")

    def refresh_data_sources(self):
        """Not needed anymore as sources are defined in settings"""
        pass

    def fetch_upcoming_events(self, days_ahead: int = 7) -> List[FinancialCalendarEvent]:
        """Fetch upcoming economic events from configured sources."""
        events = []
        end_date = datetime.now() + timedelta(days=days_ahead)
        
        for source in self.sources:
            if source['is_active']:
                try:
                    source_events = self._fetch_from_source(source, end_date)
                    events.extend(source_events)
                    logger.info(f"Fetched {len(source_events)} events from {source['name']}")
                except Exception as e:
                    logger.error(f"Error fetching from {source['name']}: {str(e)}")
                    
        return self._deduplicate_events(events)

    def _fetch_from_source(self, source: dict, end_date: datetime) -> List[FinancialCalendarEvent]:
        """Fetch events from a specific data source."""
        if source['name'].lower() == 'alpha vantage':
            return self._fetch_from_alpha_vantage(source, end_date)
        elif source['name'].lower() == 'investing':
            return self._fetch_from_investing(source, end_date)
        else:
            logger.warning(f"Unknown data source: {source['name']}")
            return []

    def _fetch_from_alpha_vantage(self, source: dict, end_date: datetime) -> List[FinancialCalendarEvent]:
        """Fetch events from Alpha Vantage API."""
        if not self.settings or not self.settings.ALPHA_VANTAGE_API_KEY:
            raise ValueError("Alpha Vantage API key not configured")
        
        api_key = self.settings.ALPHA_VANTAGE_API_KEY
        base_url = self.settings.ALPHA_VANTAGE_BASE_URL
        function = self.settings.ALPHA_VANTAGE_FUNCTION
        
        # Format dates for API request
        today = datetime.now().strftime('%Y%m%d')
        end_date_str = end_date.strftime('%Y%m%d')
        
        # Construct API URL
        url = f"{base_url}?function={function}&apikey={api_key}&from={today}&to={end_date_str}"
        
        try:
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()
            
            if 'calendar' not in data:
                logger.error(f"Unexpected response format from Alpha Vantage: {data}")
                return []
            
            events = []
            for event_data in data['calendar']:
                event = self._parse_alpha_vantage_event(event_data)
                if event:
                    events.append(event)
            
            # Save events before returning
            self._save_events(events)
            return events
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching data from Alpha Vantage: {str(e)}")
            raise
    
    def _fetch_from_investing(self, source: dict, end_date: datetime) -> List[FinancialCalendarEvent]:
        """Fetch events from Investing.com economic calendar."""
        try:
            # Get calendar data using the existing function
            df = get_economic_calendar()
            if df is None:
                logger.error("Failed to fetch data from Investing.com")
                return []

            events = []
            for _, row in df.iterrows():
                event = self._parse_investing_event(row)
                if not settings.FINANCIAL_CALENDAR_NOTIFICATION_FOR_DEBUG:
                    if event.impact == 'Low':
                        # no need save Low
                        continue
                    if event.country != 'United States' and event.impact != 'High':
                        # United States save Middle and High, other only save High
                        continue
                else:
                    nowsub4 = datetime.now() - timedelta(hours=4)
                    if event.date_time < nowsub4:
                        logger.warning(f"Event {event.event_name} is in the past {event.date_time.strftime('%Y-%m-%d %H:%M:%S')}<{nowsub4.strftime('%Y-%m-%d %H:%M:%S')}, skip")
                        continue
                events.append(event)

            # Save events before returning
            self._save_events(events)
            return events

        except Exception as e:
            logger.error(f"Error fetching data from Investing.com: {str(e)}")
            raise

    def _parse_alpha_vantage_event(self, event_data: Dict[str, Any]) -> Optional[FinancialCalendarEvent]:
        """Parse event data from Alpha Vantage response."""
        try:
            # Extract required fields
            event_name = event_data.get('event')
            event_type = self._determine_event_type(event_name)
            country = event_data.get('country')
            date_str = event_data.get('date')
            time_str = event_data.get('time')
            
            if not all([event_name, event_type, country, date_str]):
                logger.warning(f"Missing required fields in event data: {event_data}")
                return None
            
            # Parse date and time
            try:
                date = datetime.strptime(date_str, '%Y-%m-%d').date()
                date_time = None
                if time_str:
                    try:
                        time_obj = datetime.strptime(time_str, '%H:%M').time()
                        date_time = datetime.combine(date, time_obj)
                    except ValueError:
                        logger.warning(f"Invalid time format: {time_str}")
            except ValueError:
                logger.warning(f"Invalid date format: {date_str}")
                return None
            
            # Create event object with is_released based on actual value
            event = FinancialCalendarEvent(
                event_name=event_name,
                event_type=event_type,
                country=country,
                date=date,
                time=time_str,
                date_time=date_time,
                forecast=event_data.get('forecast'),
                previous=event_data.get('previous'),
                actual=event_data.get('actual'),
                impact=self._determine_impact(event_type),
                is_released=bool(event_data.get('actual'))
            )
            
            return event
        except Exception as e:
            logger.error(f"Error parsing event data: {str(e)}")
            return None

    def _parse_investing_event(self, event_data: pd.Series) -> Optional[FinancialCalendarEvent]:
        """Parse event data from Investing.com response."""
        try:
            event_name = event_data.get('Event')
            country = event_data.get('Country')
            event_date_time = event_data.get('DateTime')

            if not all([event_name, country]):
                logger.warning(f"Missing required fields in event data: {event_data}")
                return None

            # Parse date and time from the datetime string
            date_time_str = ''
            date_str = ''
            time_str = ''
            try:
                date_time_str = datetime.strptime(event_date_time, '%Y-%m-%d %H:%M:%S')
                date_str = date_time_str.date()
                time_str = date_time_str.strftime('%H:%M')
            except (ValueError, TypeError):
                logger.warning(f"Invalid datetime format: {event_date_time}")
                return None

            event_type = self._determine_event_type(event_name)

            impact = event_data.get('Impact', 'Low')
            event_type_impact = self._determine_impact(event_type)
            if event_type_impact != 'Low' and impact == 'Low':
                impact = event_type_impact

            actual = event_data.get('Actual') if event_data.get('Actual') != 'N/A' else None
            
            return FinancialCalendarEvent(
                event_name=event_name,
                event_type=event_type,
                country=country,
                date=date_str,
                time=time_str,
                date_time=date_time_str,
                forecast=event_data.get('Forecast') if event_data.get('Forecast') != 'N/A' else None,
                previous=event_data.get('Previous') if event_data.get('Previous') != 'N/A' else None,
                actual=actual,
                impact=impact,
                is_released=bool(actual)
            )

        except Exception as e:
            logger.error(f"Error parsing Investing.com event data: {str(e)}")
            return None

    def _handle_source_error(self, source: dict):
        """Handle errors from data sources and update their status."""
        source['error_count'] = source.get('error_count', 0) + 1
        if source['error_count'] > 3:  # Disable source after 3 consecutive errors
            source['is_active'] = False
            logger.warning(f"Disabling data source {source['name']} due to repeated errors")

    def _deduplicate_events(self, events: List[FinancialCalendarEvent]) -> List[FinancialCalendarEvent]:
        """Deduplicate events within the same batch."""
        if not events:
            return []
        
        # Only deduplicate within the current batch
        seen = set()
        unique_events = []
        for event in events:
            key = (event.date, event.country, event.event_name)
            if key not in seen:
                seen.add(key)
                unique_events.append(event)
        
        logger.info(f"Deduplicated {len(events)} events to {len(unique_events)} unique events")
        return unique_events

    def monitor_event_releases(self):
        """Monitor scheduled events for data releases."""
        job_run = JobRun.create_job_run(self.db_session, "financial_calendar_monitor")
        
        try:
            # Get events that need monitoring
            events_to_monitor = self.db_session.query(FinancialCalendarEvent)\
                .filter(
                    FinancialCalendarEvent.is_released == False,
                    FinancialCalendarEvent.date <= datetime.now()
                )\
                .all()

            for event in events_to_monitor:
                self._monitor_single_event(event)
                
            job_run.complete(self.db_session, len(events_to_monitor))
        except Exception as e:
            logger.error(f"Error in monitoring job: {str(e)}")
            job_run.fail(self.db_session, str(e))
            raise

    def _monitor_single_event(self, event: FinancialCalendarEvent):
        """Monitor a single event for data release."""
        status = self.db_session.query(EventMonitoringStatus)\
            .filter(EventMonitoringStatus.event_id == event.id)\
            .first()

        if not status:
            status = EventMonitoringStatus(
                event_id=event.id,
                status="pending"
            )
            self.db_session.add(status)
            self.db_session.commit()

        if status.status == "completed":
            return

        # Check if event data is available from any source
        for source in self.sources:
            if source['is_active']:
                try:
                    if self._check_event_release(source, event):
                        event.is_released = True
                        status.status = "completed"
                        status.monitoring_end = datetime.now()
                        self.db_session.commit()
                        logger.info(f"Event {event.event_type} for {event.country} released")
                        break
                except Exception as e:
                    logger.error(f"Error checking release from {source['name']}: {str(e)}")
                    self._handle_source_error(source)

    def _determine_event_type(self, event_name: str) -> str:
        """Determine event type from event name."""
        event_name = event_name.lower()
        
        if 'cpi' in event_name:
            if 'core' in event_name:
                return 'CORE_CPI'
            return 'CPI'
        elif 'ppi' in event_name:
            if 'core' in event_name:
                return 'CORE_PPI'
            return 'PPI'
        elif 'non-farm payroll' in event_name or 'nonfarm payroll' in event_name:
            return 'NFP'
        elif 'gdp' in event_name:
            return 'GDP'
        elif 'unemployment' in event_name:
            return 'UNEMPLOYMENT'
        elif 'interest rate' in event_name:
            return 'INTEREST_RATE'
        elif 'retail sales' in event_name:
            return 'RETAIL_SALES'
        elif 'consumer sentiment' in event_name:
            return 'CONSUMER_SENTIMENT'
        else:
            return 'OTHER'
    
    def _determine_impact(self, event_type: str) -> str:
        """Determine impact level based on event type."""
        high_impact_events = ['CPI', 'CORE_CPI', 'PPI', 'NFP', 'GDP', 'INTEREST_RATE']
        medium_impact_events = ['UNEMPLOYMENT', 'RETAIL_SALES', 'CONSUMER_SENTIMENT']
        
        if event_type in high_impact_events:
            return 'High'
        elif event_type in medium_impact_events:
            return 'Medium'
        else:
            return 'Low'
    
    def _check_event_release(self, source: dict, event: FinancialCalendarEvent) -> bool:
        """Check if an event's data has been released from a specific source."""
        if source['name'].lower() == 'alpha vantage':
            return self._check_alpha_vantage_release(source, event)
        elif source['name'].lower() == 'investing':
            return self._check_investing_release(source, event)
        else:
            logger.warning(f"Unknown data source for release check: {source['name']}")
            return False

    def _check_alpha_vantage_release(self, source: dict, event: FinancialCalendarEvent) -> bool:
        """Check if an event has been released using Alpha Vantage API."""
        if not self.settings or not self.settings.ALPHA_VANTAGE_API_KEY:
            raise ValueError("Alpha Vantage API key not configured")
        
        api_key = self.settings.ALPHA_VANTAGE_API_KEY
        base_url = self.settings.ALPHA_VANTAGE_BASE_URL
        function = self.settings.ALPHA_VANTAGE_FUNCTION
        
        # Format date for API request
        date_str = event.date.strftime('%Y%m%d')
        
        # Construct API URL for specific date
        url = f"{base_url}?function={function}&apikey={api_key}&from={date_str}&to={date_str}"
        
        try:
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()
            
            if 'calendar' not in data:
                logger.error(f"Unexpected response format from Alpha Vantage: {data}")
                return False
            
            # Look for matching event with actual value
            for event_data in data['calendar']:
                if (event_data.get('event') == event.event_name and 
                    event_data.get('country') == event.country and
                    'actual' in event_data and event_data['actual']):
                    
                    # Update event with actual value
                    event.actual = event_data['actual']
                    self.db_session.commit()
                    
                    return True
            
            return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Error checking release from Alpha Vantage: {str(e)}")
            raise

    def _check_investing_release(self, source: dict, event: FinancialCalendarEvent) -> bool:
        """Check if an event has been released using Investing.com data."""
        try:
            if event.retry_count >= 5:
                logger.error("Retry count exceed 5, stop check")
                return False

            # Get today's calendar data
            df = get_economic_calendar()
            if df is None:
                logger.error("Failed to fetch data from Investing.com")
                return False

            # Use the time field from event for matching
            event_time = event.date_time.strftime('%Y-%m-%d %H:%M:%S') if event.date_time else 'N/A'
            
            # Look for matching event with time comparison
            matching_events = df[
                (df['Event'] == event.event_name) & 
                (df['Country'] == event.country) &
                (df['DateTime'] == event_time)
            ]

            logger.info(f"Found {len(matching_events)} matching events for {event.event_name} at time {event_time}")

            if not matching_events.empty:
                event_row = matching_events.iloc[0]
                actual = event_row.get('Actual')
                forecast = event_row.get('Forecast')
                previous = event_row.get('Previous')
                date_time_str = event_row.get('DateTime')

                # Update event data if any field has changed
                updated = False
                if actual and actual != 'N/A' and actual != event.actual:
                    event.actual = actual
                    event.is_released = True
                    updated = True

                if forecast and forecast != 'N/A' and forecast != event.forecast:
                    event.forecast = forecast
                    updated = True

                if previous and previous != 'N/A' and previous != event.previous:
                    event.previous = previous
                    updated = True

                if date_time_str and date_time_str != 'N/A':
                    try:
                        date_time = datetime.strptime(date_time_str, '%Y-%m-%d %H:%M:%S')
                        if date_time != event.date_time:
                            event.date_time = date_time
                            event.time = date_time.strftime('%H:%M')
                            updated = True
                    except ValueError:
                        logger.warning(f"Invalid datetime format: {date_time_str}")
                
                if updated:
                    event.last_updated = datetime.now()
                    self.db_session.flush()
                    logger.info(f"Updated event {event.id} fields - Actual: {actual}, Forecast: {forecast}, Previous: {previous}, DateTime: {date_time_str}")
                if event.is_released:
                    return True
            # not match or not released retry
            event.retry_count += 1
            self.db_session.flush()
            logger.info(f"No changes in event {event.id}, increase retry count to {event.retry_count}")
                
            from jobs.cloud_scheduler_manager import CloudSchedulerManager
            scheduler = CloudSchedulerManager()
            # deleay some minutes try again
            logger.info(f"Event {event.id} not release, delay 10 minutes to check again")
            scheduler.delete_scheduler_trigger_if_exists(event)
            scheduler.create_scheduler_trigger_if_not_exist(event, delay=10*event.retry_count)
            return False

        except Exception as e:
            logger.error(f"Error checking release from Investing.com: {str(e)}")
            return False

    def _generate_event_summary(self, event):
        """Generate a summary for the economic event using OpenAI."""
        if not self.enable_openai:
            logger.warning("OpenAI integration not enabled. Cannot generate summary.")
            return None
            
        try:
            # Format the prompt for OpenAI
            date_str = event.date.strftime("%B %d, %Y") if event.date else "recent date"
            prompt = f"""
            You are a financial news writer. Please write a short and objective news article (around 3 paragraphs) and a push notification (20–50 words) based only on the following economic data, without making assumptions or inferences.This article should be divided into at least two paragraphs.

            Data:
            Indicator: {event.event_name}
            Country: {event.country}
            Month: {date_str}
            Actual: {event.actual}
            Previous: {event.previous}
            Forecast: {event.forecast}

            Requirements:
            Report the new data release as the main news. Do not include speculative statements (e.g., reasons for the change, possible implications). Explain briefly what the indicator measures. Maintain a neutral and factual tone. No quotes unless provided. Write in clear and professional English, in the tone of a Reuters or Bloomberg news brief. Only the article body must be in markdown format. Output json object with article_title, article_body, notification_title, notification_body.
            Maintain a neutral and factual tone. No quotes unless provided. Write in clear and professional English, in the tone of a Reuters or Bloomberg news brief.
            Then, summarise the article into a short paragraph suitable for app push notification with title.
            Output in the following json format: {{"article_title": "Article Title", "article_body": "Article Body", "notification_title": "Notification Title", "notification_body": "Notification Body", "post_type": "EconomicCalendar"}}
            """
            
            # Call the OpenAI API
            response = self.openai_client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=[
                    {"role": "system", "content": "You are a financial analyst that writes concise economic data summaries."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=20000,
                temperature=0.9,
                top_p=0.2,
            )
            
            summary = response.choices[0].message.content.strip()
            logger.info(f"Generated summary for event {event.id}: {summary[:50]}...")
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary for event {event.id}: {str(e)}")
            return None

    def run(self):
        """Main job execution method."""
        try:
            # Check if we're monitoring a specific event
            event_id = settings.FINANCIAL_CALENDAR_EVENT_ID if hasattr(settings, 'FINANCIAL_CALENDAR_EVENT_ID') else None
            
            if event_id:
                # Monitor specific event
                self.monitor_specific_event(event_id)
            else:
                self.schedule_event_monitoring()
                self.clear_monitoring_event()

        finally:
            self.db_session.close()

    def do_summary_after_released(self, event: FinancialCalendarEvent):
        if settings.FINANCIAL_CALENDAR_DISABLED_SUMMARY:
            return event
        if event.summary:
            logger.warning(f"Event {event.id} summary already exists")
            return event
        if not event.is_released:
            logger.warning(f"Event {event.id} is not released")
            return event
        if not settings.FINANCIAL_CALENDAR_NOTIFICATION_FOR_DEBUG and event.impact != 'High':
            logger.warning(f"Event {event.id} impact is not High")
            return event
        elif settings.FINANCIAL_CALENDAR_NOTIFICATION_FOR_DEBUG:
            logger.info(f"Event {event.id} with FINANCIAL_CALENDAR_NOTIFICATION_FOR_DEBUG enabled")
        # Generate summary by AI
        event.summary = self._generate_event_summary(event)
        if not event.summary:
            logger.warning(f"Event {event.id} summary is empty")
            return event
        if not self.pubsub_publisher:
            logger.warning(f"PubSub publisher not enabled")
            return event
        if not event.is_enabled_notification:
            logger.warning(f"Event {event.id} is disabled summary notification")
            return event
        if event.is_sent_notification:
            logger.warning(f"Event {event.id} summary notification already sent")
            return event
        event_data = {
            'event_id': event.id,
            'event_name': event.event_name,
            'event_type': event.event_type,
            'country': event.country,
            'date_time': event.date_time.strftime('%Y-%m-%d %H:%M:%S'),
            'actual': event.actual,
            'forecast': event.forecast,
            'previous': event.previous,
            'impact': event.impact,
            'is_released': event.is_released,
            'description': event.description,
            'summary': event.summary
        }
        attrs = {
            'event_id': event.id,
        }
        self.pubsub_publisher.publish(event_data, attrs=attrs)
        event.is_sent_notification = True
        logger.info(f"Event {event.id} summary notification sent")
        return event
    
    def monitor_specific_event(self, event_id):
        """
        Monitor a specific event by ID.
        
        Args:
            event_id: ID of the event to monitor
        """
        logger.info(f"Monitoring specific event with ID {event_id}")
        job_run = JobRun.create_job_run(self.db_session, f"financial_calendar_event_{event_id}")
        
        try:
            # Get the event
            event = self.db_session.query(FinancialCalendarEvent).filter(FinancialCalendarEvent.id == event_id).first()
            
            if not event:
                logger.error(f"Event with ID {event_id} not found")
                job_run.fail(self.db_session, f"Event with ID {event_id} not found")
                return
            
            # Monitor the event
            if event.is_released:
                logger.info(f"Event {event.id} {event.event_type} for {event.country} ({event.date}) is already released")
                return
            logger.info(f"Checking release status for event {event.id} {event.event_type} ({event.date})")
            
            for source in self.sources:
                if source['is_active']:
                    try:
                        if self._check_event_release(source, event):
                            event.is_released = True
                            event.last_updated = datetime.now()
                            self.db_session.flush()
                            event = self.do_summary_after_released(event)
                            logger.info(f"Event {event.event_type} for {event.country} released with value {event.actual}")
                            break
                    except Exception as e:
                        logger.error(f"Error checking release from {source['name']}: {str(e)}")
                        self._handle_source_error(source)
        except Exception as e:
            logger.error(f"Error monitoring event {event_id}: {str(e)}")
            job_run.fail(self.db_session, str(e))
            raise
        finally:
            job_run.complete(self.db_session)
    
    def schedule_event_monitoring(self, events=None):
        """Schedule monitoring for upcoming events using Cloud Scheduler."""
        logger.info("Scheduling monitoring for upcoming financial calendar events")
        job_run = JobRun.create_job_run(self.db_session, "financial_calendar_scheduler")
        
        try:
            # If no events provided, fetch upcoming events
            if events is None:
                events = self.fetch_upcoming_events()
            
            # Filter for events that need monitoring
            current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            events_to_schedule = [
                event for event in events 
                if not event.is_released and event.date_time >= current_date
            ]
            
            if not events_to_schedule:
                logger.info("No upcoming events to schedule")
                job_run.complete(self.db_session, 0)
                return
            
            # Save events and schedule them with Cloud Scheduler
            saved_events = []
            # Make sure all events are saved and have IDs
            for event in events_to_schedule:
                if not event.id:  # If event doesn't have an ID, it needs to be saved
                    try:
                        existing = self.db_session.query(FinancialCalendarEvent).filter(
                            FinancialCalendarEvent.date == event.date,
                            FinancialCalendarEvent.country == event.country,
                            FinancialCalendarEvent.event_name == event.event_name
                        ).first()
                        
                        if existing:
                            saved_events.append(existing)
                        else:
                            self.db_session.add(event)
                            self.db_session.flush()  # Assign ID without committing
                            saved_events.append(event)
                    except Exception as e:
                        logger.error(f"Error saving event {event.event_name}: {str(e)}")
                        continue
                else:
                    saved_events.append(event)
            
            self.db_session.commit()
            
            # Schedule events that were successfully saved
            if saved_events:
                try:
                    from jobs.cloud_scheduler_manager import CloudSchedulerManager
                    scheduler = CloudSchedulerManager()
                    scheduler.schedule_events(saved_events)
                    
                    job_run.complete(self.db_session, len(saved_events))
                    logger.info(f"Scheduled monitoring for {len(saved_events)} upcoming events")
                except Exception as e:
                    logger.error(f"Error scheduling events: {str(e)}")
                    job_run.fail(self.db_session, str(e))
                    raise
            else:
                logger.warning("No events were saved successfully for scheduling")
                job_run.complete(self.db_session, 0)
            
        except Exception as e:
            logger.error(f"Error in event scheduling: {str(e)}")
            job_run.fail(self.db_session, str(e))
            raise

    def clear_monitoring_event(self):
        """Clear monitoring for events that have exceeded maximum retry count or expired."""
        logger.info("Clearing monitoring for events that exceeded max retries or expired")
        job_run = JobRun.create_job_run(self.db_session, "clear_monitoring_event")
        
        try:
            # Query events with max retries
            events = self.db_session.query(FinancialCalendarEvent).filter(
                FinancialCalendarEvent.is_completed == False,
            ).all()
            
            if not events:
                logger.info("No events found need to be cleaned up")
                job_run.complete(self.db_session, 0)
                return
            
            need_clear_events = []
            for event in events:
                if event.is_released:
                    need_clear_events.append(event)
                    logger.info(f"Event {event.id} is released")
                    continue
                if event.retry_count >= 5:
                    need_clear_events.append(event)
                    logger.info(f"Event {event.id} exceed max retry count")
                    continue
                # Compare event datetime with current time in UTC-4, minus one day
                if event.date_time and event.date_time < (datetime.now() - timedelta(hours=4, days=1)):
                    logger.info(f"Event {event.id} expired {event.date_time} < {datetime.now() - timedelta(hours=4, days=1)}(UTC-4), now: {datetime.now()}(UTC)")
                    need_clear_events.append(event)
                    continue
            
            if not need_clear_events:
                logger.info("No events exceed max retry count or expired need to be cleaned up")
                job_run.complete(self.db_session, 0)
                return
            
            from jobs.cloud_scheduler_manager import CloudSchedulerManager
            scheduler = CloudSchedulerManager()
            
            cleared_count = 0
            for event in need_clear_events:
                try:
                    scheduler.remove_monitor_event(event)
                    self.db_session.commit()
                    cleared_count += 1
                except Exception as e:
                    logger.error(f"Error clearing monitoring for event {event.id}: {str(e)}")
            
            logger.info(f"Cleared monitoring for {cleared_count} events that exceeded max retries")
            job_run.complete(self.db_session, cleared_count)
            
        except Exception as e:
            logger.error(f"Error in clearing events with max retries: {str(e)}")
            job_run.fail(self.db_session, str(e))
            raise

    def _save_events(self, events: List[FinancialCalendarEvent]):
        """Save or update events in database."""
        try:
            # First deduplicate the new events among themselves
            seen = set()
            unique_events = []
            for event in events:
                key = (event.date, event.country, event.event_name)
                if key not in seen:
                    seen.add(key)
                    unique_events.append(event)
                else:
                    logger.warning(f"Duplicate event found: {key}. Skipping.")
            
            # For each unique event, try to merge it
            saved_count = 0
            updated_count = 0
            for event in unique_events:
                try:
                    # Check if event exists
                    existing = self.db_session.query(FinancialCalendarEvent).filter(
                        FinancialCalendarEvent.date == event.date,
                        FinancialCalendarEvent.country == event.country,
                        FinancialCalendarEvent.event_name == event.event_name
                    ).first()
                    
                    if existing:
                        # Update existing record
                        existing.event_type = event.event_type
                        existing.time = event.time
                        existing.date_time = event.date_time
                        existing.forecast = event.forecast
                        existing.previous = event.previous
                        existing.actual = event.actual
                        existing.impact = event.impact
                        existing.is_released = bool(event.actual)
                        existing.last_updated = datetime.now()
                        updated_count += 1
                        existing = self.do_summary_after_released(existing)
                    else:
                        # Insert new record
                        event.is_released = bool(event.actual)
                        self.db_session.add(event)
                        self.db_session.flush()
                        self.db_session.commit()
                        event = self.do_summary_after_released(event)
                        saved_count += 1
                except Exception as e:
                    logger.error(f"Error processing event {event.event_name}: {str(e)}")
                    continue
                    
            self.db_session.commit()
            logger.info(f"Processed {len(unique_events)} events: {saved_count} new, {updated_count} updated")
                
        except Exception as e:
            logger.error(f"Error saving events to database: {str(e)}")
            self.db_session.rollback()
            raise

if __name__ == "__main__":
    job = FinancialCalendarJob()
    job.run()
